<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title> <Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">
<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/pay-2b02ca7987.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newindex-09d04b32f3.css">
<style>
	.kuren {
	    padding: 2px 0;
	    padding-right: 10px;
	}
	.cominfo{
	    padding: 0 0 0 15px;
	}
	.kuai {
	    padding-top: 0px;
	    margin-top: 4px;
	}
	.container .inpifo {
	    padding: 4px 10px;
	}
	.hsma {
	    width: 24px;
	    height: 24px;
	    margin-right: 10px;
	}
	.mb80{
		padding-bottom: 80px;
	}
</style>

</head>
<body>
    <!-- header -->
    <header class="mui-bar2 hnav" style="background:#00BFFF;box-shadow: none;">
	</header>
	<!-- header end-->
<div class="mui-content" style="margin-top: -1px;">
	
	<div class="maotop">
		<div class="hedpic">
			<a href="{:U('User/login')}">
				<img src="__PUBLIC__/home/<USER>/m_04.png" alt="">
			</a>
			<if condition="$user eq 0">
				<a class="indtel" href="{:U('User/login')}">登录/注册</a>
			<else/>
				<span class="indtel">{$user}</span>
			</if>
		</div>
	</div>
<!-- group1 -->
	<section class="allgrp mt10">
		<!-- 上 -->
		<article class="cominfo">
			<div class="container">
	    		<!-- 右箭头 -->
	    		<div class="inpifo">
	    			<a href="{:U('Info/index')}">
	    				<div class="input-group">
							<div class="cf kuren">
								<div class="fl kuai hsma">
							    	<img src="__PUBLIC__/home/<USER>/m_07.png" alt="">
							    </div>
							    <div class="fl mname">      
							    	<p>我的资料</p>
							    </div>
							    <div class="fr rarr">
								    <span class="seltarr1"></span>
						    	</div>
						    </div>
			    		</div>
		    		</a>
		    	</div>
	    		<!-- 右箭头 -->
	    		<!-- 右箭头 -->
	    		<div class="inpifo">
					<a href="{:U('Order/lists')}">
						<div class="input-group">
							<div class="cf kuren">
								<div class="fl hsma kuai">
							    	<img src="__PUBLIC__/home/<USER>/m_10.png" alt="">
							    </div>
							    <div class="fl mname">      
							    	<p>我的借款</p>
							    </div>
							    <div class="fr rarr">
								    <span class="seltarr1"></span>
						    	</div>
						    </div>
			    		</div>
		    		</a>
		    	</div>
	    		<!-- 右箭头 -->
	    		<!-- 右箭头 -->
	    		<div class="inpifo">
					<a href="{:U('Order/bills')}">
						<div class="input-group">
							<div class="cf kuren">
								<div class="fl hsma kuai">
							    	<img src="__PUBLIC__/home/<USER>/m_12.png" alt="">
							    </div>
							    <div class="fl mname">      
							    	<p>我的还款</p>
							    </div>
							    <div class="fr rarr">
								    <span class="seltarr1"></span>
						    	</div>
						    </div>
			    		</div>
		    		</a>
		    	</div>
	    		<!-- 右箭头 -->
	    	</div>
		</article>
	</section>
<!-- group1 end -->
<if condition="$user neq 0">
<!-- group2  -->
<section class="allgrp mt10">
	<article class="cominfo">
		<div class="container">
			<!-- 右箭头 -->
    		<div class="inpifo">
				<a href="{:U('User/backpwd')}">
					<div class="input-group">
						<div class="cf kuren">
							<div class="fl hsma kuai">
						    	<img src="__PUBLIC__/home/<USER>/hm_13.png" alt="">
						    </div>
						    <div class="fl mname">      
						    	<p>修改密码&nbsp;</p>
						    </div>
						    <div class="fr rarr">
							    <span class="seltarr1"></span>
					    	</div>
					    </div>
		    		</div>
	    		</a>
	    	</div>
	    	<!-- 右箭头 -->
		</div>
	</article>
	<article class="cominfo">
		<div class="container">
			<!-- 右箭头 -->
    		<div class="inpifo">
				<a href="javascript:logout();">
					<div class="input-group">
						<div class="cf kuren">
							<div class="fl hsma kuai">
						    	<img src="__PUBLIC__/home/<USER>/m_13.png" alt="">
						    </div>
						    <div class="fl mname">      
						    	<p>退出登录</p>
						    </div>
						    <div class="fr rarr">
							    <span class="seltarr1"></span>
					    	</div>
					    </div>
		    		</div>
	    		</a>
	    	</div>
    		<!-- 右箭头 -->
		</div>
	</article>
</section>
</if>

</div>
	<!-- bottom bar -->
   <!-- bottom bar -->
    <nav class="mui-bar mui-bar-tab bottom-bar">
        <a class="mui-tab-item" href="{:U('Index/index')}">
                <span class="mui-icon mui-icon-home home">
        </span>
            <span class="mui-tab-label">首页</span>
        </a>
        <a class="mui-tab-item" href="{:U('Index/member')}">
                <span class="mui-icon mui-icon-home home">
        </span>
            <span class="mui-tab-label">会员选项</span>
        </a>
		   <a class="mui-tab-item" href="/index.php?m=Qianbao&a=index">
                 <span class="mui-icon"><img src="/Public/home/<USER>/ico_foot2.png" width="100%" alt=""></span>
        </span>
            <span class="mui-tab-label">钱包</span>
        </a>
        <a class="mui-tab-item" href="{:U('Help/index')}">
            <span class="mui-icon mui-icon-contact muihelp"></span>
            <span class="mui-tab-label">客服</span>
        </a>
        <a class="mui-tab-item cur" href="{:U('User/index')}">
            <span class="mui-icon mui-icon-email myself cur"></span>
            <span class="mui-tab-label">我</span>
        </a>
    </nav>
    <!-- 底部固定栏 end-->

<div class="deowin" style="display: none;" id="deowin">
	<div class="deocon">
		<div class="divpad" style="text-align:center;line-height:80px">
			确定要退出当前账号？
		</div>
		<div class="wobtn">
			<div class="twobtn"><!-- 两个按钮用这个结构 -->
				<a id="winbtn" href="javascript:;" style="color:#666">取消</a>
				<!-- href="/account/passport/logout" -->
				<a class="obtn" id='lgbtn'  style="color: #0894ec;">确定</a>
			</div>
		</div>
	</div>
</div>
<div class="emask" style="display: none;" id="mask"></div>

<script src="__PUBLIC__/home/<USER>/jquery.js"></script>
<script src="__PUBLIC__/home/<USER>/fontsizeset.js"></script>
<script src="__PUBLIC__/home/<USER>/fukuang.js"></script>
<script>
	var h = $('#deowin').height();
	var t = -h/2 + "px";
	$('#deowin').css('marginTop',t);
	function logout(){
		$("#deowin").show();
		$('#mask').show();
	}
	$('.bottom-bar a').click(function(){
		$('.bottom-bar a').removeClass('cur');
		$('.bottom-bar a span').removeClass('cur');
		$(this).addClass('cur');
		$(this).find('span').eq(0).addClass('cur');
	});
	$("#lgbtn").click(function() {
		window.location.href = '{:U("User/logout")}';
	});
	$('#winbtn').click(function(){
    	$('#deowin').hide();
    	$('#mask').hide();
	});
</script>	
<div style="display: none;">
	<Somnus:sitecfg name="sitecode"/>
</div>
</body>
</html>