<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
    <title>借款评估 - 站长源码库（zzmaku.com） </title>

    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">

    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/evaluation.css">


</head>

<body id="evaluation">
    <div class="comm_top_nav" data-am-sticky>
        <div class="am-g">
            <b>
                <div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i
                        class="am-icon-angle-left am-icon-fw"></i></div>
                <div class="am-u-sm-8">大数据风险测评</div>
                <div class="am-u-sm-2"></div>
            </b>
        </div>
    </div>

    <div class="evaluation_box">
        <div class="am-form-group evaluation_list evaluation_list_1">
            <h3>工作是？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_1" value="A.企事业单位及公务员" data-am-ucheck required>
                A.企事业单位及公务员
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_1" value="B.蓝领" data-am-ucheck>
                B.蓝领
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_1" value="C.在校学生" data-am-ucheck>
                C.在校学生
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_1" value="D.自由职业" data-am-ucheck>
                D.自由职业
            </label>

            <div class="bottom_line am-g">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small>

                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>1</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_2">
            <h3>收入主要来自？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_2" value="A.工资奖金" data-am-ucheck required>
                A.工资奖金
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_2" value="B.生产经营" data-am-ucheck>
                B.生产经营
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_2" value="C.金融或房地产投资" data-am-ucheck>
                C.金融或房地产投资
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_2" value="D.其他" data-am-ucheck>
                D.其他
            </label>

            <div class="bottom_line am-g after" data-next="1">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>2</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_3">
            <h3>辛苦一年赚了？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="A.5万及以下" data-am-ucheck required>
                A.5万及以下
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="B.5万-10万" data-am-ucheck>
                B.5万-10万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="C.10万-30万" data-am-ucheck>
                C.10万-30万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="D.30万-50万" data-am-ucheck>
                D.30万-50万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="E.50万-100万" data-am-ucheck>
                E.50万-100万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_3" value="F.100万以上" data-am-ucheck>
                F.100万以上
            </label>

            <div class="bottom_line am-g after" data-next="2">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>3</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_4">
            <h3>多少钱用于投资？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="A.1万及以下" data-am-ucheck required>
                A.1万及以下
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="B.1万-5万" data-am-ucheck>
                B.1万-5万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="C.5万-10万" data-am-ucheck>
                C.5万-10万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="D.10万-30万" data-am-ucheck>
                D.10万-30万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="E.30万-100万" data-am-ucheck>
                E.30万-100万
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_4" value="F.100万以上" data-am-ucheck>
                F.100万以上
            </label>

            <div class="bottom_line am-g after" data-next="3">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>4</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_5">
            <h3>每月钱花哪了？ <small class="e_color">（多选）</small></h3>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_5" value="A.还信用卡等消费贷" data-am-ucheck>
                A.还信用卡等消费贷
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_5" value="B.还车贷" data-am-ucheck>
                B.还车贷
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_5" value="C.还房贷" data-am-ucheck>
                C.还房贷
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_5" value="D.日常花销" data-am-ucheck>
                D.日常花销
            </label>

            <div class="bottom_line am-g after" data-next="4">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>5</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_6">
            <h3>理财经验有哪些？ <small class="e_color">（多选）</small></h3>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_6" value="A.银行存款" data-am-ucheck>
                A.银行存款
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_6" value="B.基金（含余额宝）" data-am-ucheck>
                B.基金（含余额宝）
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_6" value="C.定期理财" data-am-ucheck>
                C.定期理财
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_6" value="D.股票" data-am-ucheck>
                D.股票
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_6" value="E.有金融学习或工作经验" data-am-ucheck>
                E.有金融学习或工作经验
            </label>

            <div class="bottom_line am-g after" data-next="5">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>6</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_7">
            <h3>有多少年的理财经验？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_7" value="A.1年以下" data-am-ucheck required>
                A.1年以下
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_7" value="B.1-3年" data-am-ucheck>
                B.1-3年
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_7" value="C.2-5年" data-am-ucheck>
                C.2-5年
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_7" value="D.5年以上" data-am-ucheck>
                D.5年以上
            </label>

            <div class="bottom_line am-g after" data-next="6">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>7</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_8">
            <h3>你想投资什么产品？ <small class="e_color">（多选）</small></h3>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_8" value="A.定期" data-am-ucheck>
                A.定期
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_8" value="B.基金" data-am-ucheck>
                B.基金
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_8" value="C.股票" data-am-ucheck>
                C.股票
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_8" value="D.商品（黄金、原油等）" data-am-ucheck>
                D.商品（黄金、原油等）
            </label>

            <div class="bottom_line am-g after" data-next="7">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>8</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_9">
            <h3>打算投资多久？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_9" value="A.不固定" data-am-ucheck required>
                A.不固定
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_9" value="B.1年以内" data-am-ucheck>
                B.1年以内
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_9" value="C.1-3年" data-am-ucheck>
                C.1-3年
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_9" value="D.3年以上" data-am-ucheck>
                D.3年以上
            </label>

            <div class="bottom_line am-g after" data-next="8">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>9</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_10">
            <h3>投资目标是？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_10" value="A.不希望本金损失，仅追求资产保值" data-am-ucheck required>
                A.不希望本金损失，仅追求资产保值
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_10" value="B.能够承担适当风险，追求资产的稳步增长" data-am-ucheck>
                B.能够承担适当风险，追求资产的稳步增长
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_10" value="C.能够承担较大的风险，追求资产大幅增长" data-am-ucheck>
                C.能够承担较大的风险，追求资产大幅增长
            </label>

            <div class="bottom_line am-g after" data-next="9">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>10</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_11">
            <h3>你要照顾的人？ <small class="e_color">（多选）</small></h3>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_11" value="A.照顾自己" data-am-ucheck>
                A.照顾自己
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_11" value="B.照顾另一半" data-am-ucheck>
                B.照顾另一半
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_11" value="C.抚养孩子" data-am-ucheck>
                C.抚养孩子
            </label>
            <label class="am-checkbox am-danger check_list">
                <input type="checkbox" name="evaluation_11" value="D.赡养老人" data-am-ucheck>
                D.赡养老人
            </label>

            <div class="bottom_line am-g after" data-next="10">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>11</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_12">
            <h3>偏好什么样的风险？ <small class="e_color">（单选）</small></h3>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_12" value="A.低" data-am-ucheck required>
                A.低
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_12" value="B.中低" data-am-ucheck>
                B.中低
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_12" value="C.中" data-am-ucheck>
                C.中
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_12" value="D.中高" data-am-ucheck>
                D.中高
            </label>
            <label class="am-radio am-danger check_list">
                <input type="radio" name="evaluation_12" value="E.高" data-am-ucheck>
                E.高
            </label>

            <div class="bottom_line am-g after" data-next="11">
                <div class="am-u-sm-6" style="color: #a26c00;">
                    <small class="after_txt">
                        上一题
                    </small>
                </div>
                <div class="am-u-sm-6" style="text-align: right;">
                    <small>
                        <span>12</span>
                        /
                        <span>12</span>
                    </small>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_13">
            <h2>确认12题信息？ <small class="e_color">（点击可修改）</small></h2>
            <div class="question question_1" data-number="1">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_2" data-number="2">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_3" data-number="3">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_4" data-number="4">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_5" data-number="5">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_6" data-number="6">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_7" data-number="7">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_8" data-number="8">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_9" data-number="9">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_10" data-number="10">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_11" data-number="11">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
            <div class="question question_12" data-number="12">
                <div class="am-g">
                    <div class="am-u-sm-10">
                        <span>工作是？</span>
                        <br>
                        <small>风险测评</small>


                    </div>
                    <div class="angle" style="">
                        <i class="am-icon-angle-right am-icon-fw"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="am-form-group evaluation_list evaluation_list_14" style="text-align: center;">
            <div class="evaluation_img">
                <img src="__PUBLIC__/home/<USER>/image/evaluation.png" alt="" width="100%">
            </div>
            <h2>您可获取的借款额度为 <small class="e_color"></small></h2>
            <span class="f_number" style="font-size: 25px;"><span class="evaluation" style="color: #a26c00;">{$userlogin.fxmoney}</span>
                &nbsp; <b>元</b></span>
            <div style="height: 20px;"></div>
            <span class="double">重测</span>
        </div>



    </div>



    <div class="fix_bottom next" data-next="2">
        <div class="am-g">
            <button type="button" class="am-btn am-btn-block bt">下一题</button>
        </div>
    </div>


    <script type="text/javascript">
        document.documentElement.addEventListener('touchmove', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);
    </script>

    <script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
    <!--<![endif]-->
    <script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

    <script>
        var evaluation_number = 1,
            check = '',
            up = 0
            ;
    function rand(min,max) {
        return Math.floor(Math.random()*(max-min))+min;
    }
      var money=$('.evaluation').text();
        $.post(
            "{:U('User/evaluation')}",
            {
            },
            function (data, status) {
                if (status=='success') {
                    $(".evaluation_list_14").css("display", "block");
                    $(".next").data('next', 15);
                    $(".bt").html('完成');
                } else {
                    $(".evaluation_list_" + evaluation_number).css("display", "block");
                }

            }
        );


        $(".next").unbind('click').on('click', function () {

            evaluation_number = $(this).data('next');

            if (up == 0) {
                check = evaluation_number - 1;
            } else {
                check = up;
            }

            $(".evaluation_list_" + evaluation_number + " .after_txt").css("display", "block");

            if ($("input[name='evaluation_" + check + "']").is(':checked')) {
                var question = $(".evaluation_list_" + check + " h3").text();

                var t = $("input[name='evaluation_" + check + "']:checked").attr("type");

                var strgetSelectValue = '';

                if (t == "radio") {
                    strgetSelectValue = $("input:radio[name='evaluation_" + check + "']:checked").val();
                }

                if (t == "checkbox") {
                    var getSelectValueMenbers = $("input:checkbox[name='evaluation_" + check + "']:checked").each(function (j) {

                        if (j >= 0) {
                            strgetSelectValue += $(this).val() + ","

                        }
                    });
                }

                $(".question_" + check + " span").html(question);
                $(".question_" + check + " small").html(strgetSelectValue);



                console.log(t);
                console.log(strgetSelectValue);

                if (evaluation_number != 15) {
                    $(".evaluation_list").css("display", "none");
                    $(".evaluation_list_" + evaluation_number).css("display", "block");
                    $(this).data('next', evaluation_number + 1);
                }


                if (evaluation_number == 12) {
                    $(".bt").html('确认提交');
                }

            }

            if (evaluation_number == 14) {
                $(".evaluation_list").css("display", "none");
                $(".evaluation_list_" + evaluation_number).css("display", "block");
                $(".bt").html('完成');
                $(".next").addClass('next_qr');
                $(this).data('next', evaluation_number + 1);
                var obj = rand(20000,30000);
                $.post( 
                    "{:U('User/evaluation')}",
                    {
                     fxmoney:obj
                    },
                    function (data, status) {
                        $(".evaluation").html(obj);
                    }
                );
            }

            if (evaluation_number == 15) {
                window.location.href = "{:U('User/index')}";
            }

            up = 0;
            console.log(evaluation_number);
        });

        $(".after").unbind('click').on('click', function () {
            evaluation_number = $(this).data('next');
            $(".evaluation_list").css("display", "none");
            $(".evaluation_list_" + evaluation_number).css("display", "block");
            $(".next").data('next', evaluation_number + 1);
            $(".bt").html('下一题');
            console.log(evaluation_number);
        });

        $(".question").unbind('click').on('click', function () {
            evaluation_number = $(this).data('number');
            up = evaluation_number;
            $(".evaluation_list").css("display", "none");
            $(".evaluation_list_" + evaluation_number).css("display", "block");
            $(".evaluation_list_" + evaluation_number + " .after_txt").css("display", "none");
            $(".next").data('next', 13);
        });

        $(".double").unbind('click').on('click', function () {
            evaluation_number = 1;
            $(".evaluation_list").css("display", "none");
            $(".evaluation_list_" + evaluation_number).css("display", "block");
            $(".next").data('next', 2);
            $(".bt").html('下一题');
        });

    </script>


</body>

</html>