
<div class="layui-table-tool">
<h3  class="layui-table-tool-self">

    <a href="{:U(GROUP_NAME.'/Block/add')}" class="actionBtn add  layui-btn ">

        添加自由块
    </a>
</h3>
</div>
<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

    <tr>

        <th width="30">ID</th>

        <th width="300" align="left">名称</th>

        <th width="140" align="left">添加时间</th>

        <th width="100" align="center">操作</th>

    </tr>

    <foreach name="data" item="vo">

        <tr>

            <td align="center">{$vo.id}</td>

            <td align="left">{$vo.name}</td>

            <td>{$vo.addtime|date="Y/m/d",###}</td>

            <td align="center">

                <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="{:U(GROUP_NAME.'/Block/edit',array('id'=>$vo['id']))}"><i class="layui-icon"></i>编辑</a>
                </button>

                <button class="layui-btn layui-btn-sm layui-btn-normal"><a
                        href="javascript:delCat('{$vo.name}','{:U(GROUP_NAME.'/Block/del',array('id'=>$vo['id']))}');"><i class="layui-icon"></i>删除</a></button>

            </td>

        </tr>

    </foreach>

</table>

<script>

    function delCat(name, jumpurl) {

        layer.confirm(

            '确定要删除自由块:[' + name + ']吗?',

            function () {

                window.location.href = jumpurl;

            }

        );

    }

</script>