<html>
<head>  
<link rel="stylesheet" href="/Public/Manage/css/table.css">
		<title>保单截图：{$order.oid} - 站长源码库（zzmaku.com） </title>
		<style>
			td { font-size:13px; }
			.nestable{
				margin: 0 auto;
				*margin: 20px !important;
				*border: 1px solid #5961c7;
				width: 1275px !important;
				height:555px !important;
				padding: 0px !important;
				background:url(Public/images/hd_bg1.png) repeat-x;
				background-size: cover;
	
			}
			.nestable{
				background-color: #999;
			}
			.content{
				width: 100%;
				padding-top:250px !important;
				padding-left:125px !important;
				*margin: 16px auto;
			}

			.pp{
				-webkit-box-sizing:border-box;
				-moz-box-sizing:border-box;
				box-sizing:border-box;
				display: block;
				float:left;
				width: 100%;
				font-size: 12px;
				height:30px;
				line-height:30px;
			}
			.tits{
				float:left; 
				width:585px;
				text-align: right;
			}
			.co{float:left; }
			.copy{
				width: 100%;
				margin: 16px auto;
				text-align: center;
				}
			.copy button{
			 padding: 8px 25px;
			margin:15px auto;
			}
.copy a{
	width: 10%;
    padding: 8px 30px;
	margin:15px auto;
    height: 32px;
    font-size: 12px;
    font-weight: normal;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 4px;
	background-color: #f2f2f2;
 line-height: 14px;
  text-decoration:none;
}
.copy a:hover{
  text-decoration:none;
}
		</style>
		<script src="/Public/Manage/js/html2canvas.js"></script>
	</head>
	<body>
		<div id="div_hd" class="nestable">
			
			<div class="content">
			<table border="1" width="1101px" height="108px" style="border-collapse:collapse;border-color:#c9d4da;">
				 <tbody>
			<tr align="center">
            <td bgcolor="#F5F6FA"><strong>保险名称</strong></td>
            <td bgcolor="#F5F6FA"><strong>贷款概要</strong></td>
            <td bgcolor="#F5F6FA"><strong>投保人</strong></td>
            <td bgcolor="#F5F6FA"><strong>投保金额</strong></td>
            <td bgcolor="#F5F6FA"><strong>保险期限</strong></td>
            <td bgcolor="#F5F6FA"><strong>保单状态</strong></td>
			<td bgcolor="#F5F6FA"><strong>保单类型</strong></td>
			<td bgcolor="#F5F6FA"><strong>投保资料</strong></td>
			</tr>
		
			 <tr>
				<td align="center" bgcolor="#fff">金融网贷商业险</td>
               <td align="center" bgcolor="#fff"><Somnus:sitecfg name="sitetitle"/>{$order.ordernum}</br>贷款金额{$order.money}元整</td>
              <td align="center" bgcolor="#fff">{$info.name}</td>
              <td align="center" bgcolor="#fff"><input type="text" name="T1" size="15" style="border: 1px solid #FFFFFF" value="<?=($order['money']*$config['insurance'])/100?>元整"></td>
              <td align="center" bgcolor="#fff"><input type="text" name="T1" size="25" style="border: 1px solid #FFFFFF" value="<?=date('Y/m/d')?>日至<?=date('Y/m/d',strtotime('+3 year')) ?>日"></td>
              <td align="center" bgcolor="#fff"><input type="text" name="T1" size="6" style="border: 1px solid #FFFFFF" value="未生效"></td><td align="center" bgcolor="#fff">电子保单</td>
              <td align="center" bgcolor="#fff"><span style="color:#003399;">贷款合同 已上传√</br>投保人资料 已上传√</span></td>
      		</tr>
				<tr>
				<td colspan="8" align="left" bgcolor="#ffffff">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="color: #e81b1b;"></span></td>
				</tr>	
			    </tbody>
			</table>
				
			</div>
		</div>
			<div class="copy">
			
			<button class="but"  style="display:block;" >点我截屏</button>
			<a class="down" style="display:none;" href="" download="hd">下载</a>
			 <div id="box"></div>
			</div>
			
<script>
    $(document).ready(function () {

        html2canvas(document.getElementById('div_hd'), { 
            onrendered: function (canvas) { 
                var canvasData = canvas.toDataURL(); 
                var eg = new Image(); 
                eg.src = canvasData;a
                $("button").on("click", function () {
					$(".down").css("display","block");
					$(".but").css("display","none");
					$box = $("#box");
                    $box.prepend(eg);
					document.querySelector(".down").setAttribute('href',canvasData);
                })
            }, 
            // useCORS: true// 此代码针对跨域问题 
        });
    })
            </script>
	</body>
</html>