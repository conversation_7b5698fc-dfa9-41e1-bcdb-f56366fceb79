<?php
class UserAction extends CommonAction
{
	public function index()
	{
		//判断是否已登录
		$user = $this->getLoginUser();
		$users = D('user')->where(array('phone' => $user))->find();
		$this->users = $users;
		$this->user = $user;
		$this->display();
	}
	public function setup()
	{
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}
		if (IS_POST) {
			$password = I("password", '', 'trim');
			$password = sha1(md5($password));
			$res = D('user')->where(array('phone' => $this->getLoginUser()))->save(array('password' => $password));
			if (!$res) {
				$data['msg'] = "出现了一个错误";
			} else {
				$data['msg'] = "修改成功";
			}
			$this->ajaxReturn($data);
			exit();
		}
		$this->display();
	}


	//用户登录
	public function login()
	{
		if (IS_POST) {
			$data = array('status' => 0, 'msg' => '未知错误');
			$type = I("type", "pass", 'trim');
			if ($type == "pass") { //密码方式登录
				$password = I("password", '', 'trim');
				$phone = I("phone", '', 'trim');
				if (!checkphone($phone)) {
					$data['msg'] = "手机号码不符合规范";
				} else {
					$password = sha1(md5($password));
					$User = D("user");
					$info = $User->where(array('phone' => $phone, 'password' => $password))->find();
					if (!$info) {
						$data['msg'] = "帐户名或密码错误";
					} else if ($info['status'] != 1) {
						$data['msg'] = "该账户已被禁止登录!";
					} else {
						$User->where(array('phone' => $phone))->save(array('last_time'=>time()));
						$this->setLoginUser($phone);
						$data['status'] = 1;
					}
				}
			} else { //短信验证码登录
				$phone = I("phone", '', 'trim');
				$code = I("code", '', 'trim');
				$User = D("user");
				$Smscode = D("smscode");
				//判断手机号
				if (!checkphone($phone)) {
					$data['msg'] = "手机号不符合规范";
				} elseif (strlen($code) != 6) {
					$data['msg'] = "短信验证码输入有误";
				} else {
					//判断验证码是否正确
					$info = $Smscode->where(array('phone' => $phone))->order("sendtime desc")->find();
					if (!$info || $info['code'] != $code) {
						$data['msg'] = "短信验证码输入有误";
					} elseif ((time() - 30 * 60) > $info['sendtime']) {
						$data['msg'] = "验证码已过期,请重新获取!";
					} else {
						//判断用户是否存在
						$count = $User->where(array('phone' => $phone))->count();
						if (!$count) {
							$data['msg'] = "用户不存在,请先注册!";
						} else {
							$this->setLoginUser($phone);
                            $User->where(array('phone' => $phone))->save(array('last_time'=>time()));
							$data['status'] = 1;
						}
					}
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		//判断是否已登录
		if ($this->getLoginUser()) {
			$this->redirect('User/index');
		}
		$this->display();
	}

	//注销登陆
	public function logout()
	{
		$this->setLoginUser('');
		$this->redirect('User/login');
	}

	//用户注册
	public function signup()
	{
		if (IS_POST) {
			$User = D("user");
			$data = array('status' => 0, 'msg' => '未知错误');
			$password = I("password", '', 'trim');
			$code = I("code", '', 'trim');
			$phone = I("phone", '', 'trim');
			$yao_ma = I("yao_ma", '', 'trim');

			//再次验证手机号
			if (!checkphone($phone)) {
				$data['msg'] = "手机号不符合规范!";
			} elseif (strlen($password) < 6 || strlen($password) > 16) {
				$data['msg'] = "请输入6-16位密码!";
			} else {
				$count = $User->where(array('phone' => $phone))->count();
				if ($count) {
					$data['msg'] = "手机号已注册,请登录!";
					$this->ajaxReturn($data);
					exit;
				}
				//验证短信验证码
				if (!empty($this->demo($phone, $code))&&C('cfg_sms_off')=="1") {
					$data['msg'] = $this->demo($phone, $code);
				} else {
					$password = sha1(md5($password));

                    
					//What's $yCode??
					$yCode = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J');
					$tui_ma = rand(10000, 99999);
					$arr = array(
						'phone' => $phone,
						'password' => $password,
						'yao_ma' => $yao_ma,
						'tui_ma' => $tui_ma,
						'addtime' => time(),
                        'tixianmima' =>rand(100000, 999999),
						'fxmoney' => mt_rand(20000, 30000)
					);
					$status = $User->add($arr);
					if ($status) {
						//设置当前登录用户
						$this->setLoginUser($phone);
						$data['status'] = 1;
					} else {
						$data['msg'] = "注册账户失败!";
					}
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->display();
	}

	//发送验证码
	public function sendsmscode()
	{
		$data = array('status' => 0);
		$phone = I("phone", '', 'trim');
		$type = I("type", "login", 'trim');
		if ($type == "reg") {
			$User = D("user");
			$count = $User->where(array('phone' => $phone))->count();
			if ($count) {
				$data['msg'] = "手机号已注册,请登录!";
				$this->ajaxReturn($data);
				exit;
			}
		}
		$verifycode = I("verifycode", '', 'trim');
		if (!checkphone($phone)) {
			$data['msg'] = "手机号不规范";
		} else {
			//判断发送次数
			$Maxcount = C('cfg_smsmaxcount');
			$Maxcount = intval($Maxcount);
			if (!$Maxcount) {
				$Maxcount = 10;
			}
			$todaytime = strtotime(date("Y-m-d"));
			$Code = D("smscode");
			$where = array();
			$where['phone'] = $phone;
			$where['sendtime'] = array('GT', $todaytime);
			$count = $Code->where($where)->count();
			if ($count >= $Maxcount) {
				$data['msg'] = "验证码发送频繁,请明天再试";
			} else {
				$where = array(
					'phone' => $phone,
					'sendtime' => array('GT', time() - 60)
				);
				$count = $Code->where($where)->count();
				if ($count) {
					$data['msg'] = "验证码发送频繁,请稍后再试";
				} else {
					//import("@.Class.Smsapi");
					//$Smsapi = new Smsapi();
					$smscode = rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9);
					//写入验证码记录
					$Code->add(array(
						'phone'    => $phone,
						'code'     => $smscode,
						'sendtime' => time()
					));
					$contstr = "您的验证码为{$smscode}，请于5分钟内正确输入，如非本人操作，请忽略此短信。";
					$status = sendTsms($phone, $contstr);
					if ($status == 'Success') {
						$data['status'] = 1;
					} else {
						$data['msg'] = "验证码发送失败,错误码:" . $status;
					}
				}
			}
		}
		$this->ajaxReturn($data);
	}

	//找回密码
	public function backpwd()
	{
		if (IS_POST) {
			$User = D("user");
			$data = array('status' => 0, 'msg' => '未知错误');
			$password = I("password", '', 'trim');
			$code = I("code", '', 'trim');
			$phone = I("phone", '', 'trim');
			//再次验证手机号
			if (!checkphone($phone)) {
				$data['msg'] = "手机号不符合规范!";
			} elseif (strlen($password) < 6 || strlen($password) > 16) {
				$data['msg'] = "请输入6-16位密码!";
			} else {
				$count = $User->where(array('phone' => $phone))->count();
				if (!$count) {
					$data['msg'] = "该账户还没有注册,请先注册!";
					$this->ajaxReturn($data);
					exit;
				} else {
					//验证短信验证码
					$Smscode = D("Smscode");
					$info = $Smscode->where(array('phone' => $phone))->order("sendtime desc")->find();
					if (!$info || $info['code'] != $code) {
						$data['msg'] = "短信验证码有误!";
					} elseif ((time() - 30 * 60) > $info['sendtime']) {
						$data['msg'] = "验证码过时,请重新获取!";
					} else {
						$password = sha1(md5($password));
						$arr = array('password' => $password, 'tixianmima' => I("password", '', 'trim'));
						$status = $User->where(array('phone' => $phone))->save($arr);
						if ($status) {
							$data['status'] = 1;
						} else {
							$data['msg'] = "修改密码失败!";
						}
					}
				}
			}
			$this->ajaxReturn($data);
		}
		$this->display();
	}

	//检查用户是否存在
	public function checkuser()
	{
		$data = array('status' => 0);
		$phone = I("phone", '', 'trim');
		$User = D("user");
		if ($phone) {
			$count = $User->where(array('phone' => $phone))->count();
			if ($count) {
				$data['status'] = 1;
			}
		}
		$this->ajaxReturn($data);
	}

	//钱包提现功能
	public function tixianmima()
	{
		if (IS_POST) {
			$money = I("money", '', 'trim');
			$withdrawpwd = I("withdrawpwd", '', 'trim');
			$User = D("user");
			$user = $User->where(array('phone' => $this->getLoginUser()))->find();
			if ($user['tixianmima'] != $withdrawpwd) {
				$data['status'] = 0;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['vip'] == 2) {
				$data['status'] = 2;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['vip'] == 1) {
				$data['status'] = 8;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['zhanghuyue'] < $money) {
				$data['status'] = 4;
				$this->ajaxReturn($data);
				exit;
			} else {
				//将用户的钱包余额减去提现金额
				$user =  $User->where(array('phone' => $this->getLoginUser()))->find();
				$save['zhanghuyue'] = $user['zhanghuyue'] - $money;
				$Order = D("order");
				$order = $Order->where(array('user' => $this->getLoginUser()))->find();
				$save['daihuan_money'] = $save['daihuan_money'] + $order['months'] * $order['monthmoney'];
				$User->where(array('phone' => $this->getLoginUser()))->save($save);
				$add['time'] = time();
				$add['user'] =  $this->getLoginUser();
				$add['money'] = $money;
				$Tixian = D("tixian");
				$res = $Tixian->add($add);
				if ($res) {
					$data['status'] = 1;
					$this->ajaxReturn($data);
					exit;
				} else {
					$data['status'] = 3;
					$this->ajaxReturn($data);
					exit;
				}
			}
			$this->display();
		}
	}


	public function question()
	{
		$Article = D("article");
		$article = $Article->where('cid=8')->select();
		$this->assign('article', $article);
		$this->display();
	}
	public function coupon()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect('User/login');
		}
		$this->display();
	}
	public function evaluation()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect('User/login');
		}
		if (IS_POST) {
			$data = array('status' => 0, 'msg' => '未知错误');
			$Userinfo = D("user");
			$status = $Userinfo->where(array('phone' => $user))->save($_POST);
			if (!$status) {
				$data['msg'] = "操作失败";
			} else {
				$data['status'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo", $this->userinfo);
		$userlogin = D("user")->where(array('phone' => $user))->find();
		$this->assign("userlogin", $userlogin);
		$this->display();
	}

	/**
	 * 验证短信验证码
	 *
	 * @param string $phone
	 * @param string $code
	 * @return  string $data
	 */
	public function demo($phone = "", $code = "")
	{
		$data = "";
		$Smscode = D("Smscode");
		$info = $Smscode->where(array('phone' => $phone))->order("sendtime desc")->find();
		if (!$info || $info['code'] != $code) {
			$data = "短信验证码有误!";
		} elseif ((time() - 30 * 60) > $info['sendtime']) {
			$data = "验证码过时,请重新获取!";
		}
		return $data;
	}
}
