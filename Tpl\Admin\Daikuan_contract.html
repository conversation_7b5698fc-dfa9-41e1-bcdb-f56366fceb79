
<script type="text/javascript" charset="utf-8" src="__PUBLIC__/admin/UEditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="__PUBLIC__/admin/UEditor/ueditor.all.min.js"> </script>

<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<script type="text/javascript" charset="utf-8" src="__PUBLIC__/admin/UEditor/lang/zh-cn/zh-cn.js"></script>

<style type="text/css">
    div{
        width:100%;
    }
</style>

<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
    <legend>合同设置</legend>
</fieldset>
<div class="layui-form">
	<div style="width:70%">
		<form action="{:U(GROUP_NAME.'/Daikuan/contract')}" method="post">
			<label class="left control-label">内容：</label>
	        <div class="right">
	            <textarea id="introduction" class="form-control textarea" rows="5" name="content">{$data}</textarea>
	            <span class="check-tips text-muted small">填写合同内容</span>
	        </div>
	        <button type="submit" class="layui-btn layui-btn-danger">&nbsp;&nbsp;增加&nbsp;&nbsp;</button>
		</form>
	</div>
</div>

<script>
        UE.getEditor("introduction");
</script>