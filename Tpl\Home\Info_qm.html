<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">





	<title>手写签名 - 站长源码库（zzmaku.com） </title>

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">


</head>
<style>
	#signature {
		border: 2px dotted black;
		background-color: lightgrey;
	}
</style>

<body>


	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">手写签名</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>

	</div>

	<div class="alipay">
		<form action="" enctype="multipart/form-data">

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">手写签名:</div>
						<div class="am-u-sm-8 f_number">
							<php>
								if($userinfo['signature']){
									echo "<img src='$userinfo[signature]' width='120px' height='120px'>";
								}
							</php>
							<!-- <img id="qmsrc" src="{$userinfo.signature}" width="120px" height="120px;"> -->
						</div>
					</div>
				</div>

				<div class="input_text_group">
					<div class="input_text_list">
						<div class="am-g">

							<div class="am-u-sm-4" style="width: 100%;">请在虚线内签字,签名需规范、完整、字迹清晰</div>

						</div>
					</div>
				</div>
			</div>
			<if condition="$userinfo['signature'] eq '' ">
			<div class="input_text_group">
				<div id="signature" style="height: 16rem;width: 90%;margin: 0 auto;"></div>
				<form action="/index.php?m=Info&a=qm" method="post">
					<input type="hidden" name="signature" value="">

				</form>

			</div>
			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">

					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">

						<div class="am-u-sm-3">
							<div class="hq_button fw-button" id="resetBtn" data-type="2">
								复位
							</div>
						</div>
					</div>
				</div>
			</div>
			</if>



			<div style="height: 70px;"></div>

			<div class="fix_bottom">
				<div class="am-g">
					
					<if condition="$userinfo['signature'] eq '' ">
						<button type="button" class="am-btn am-btn-block" id="qm-button">
							确认提交
						</button>
					<else />
					<button type="button" class="am-btn am-btn-block" onclick="tijiaoguo()">
						确认提交
					</button>
					</if>
				</div>
			</div>
		</form>
	</div>
	<div class="message">
		<p></p>
	</div>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/jSignature.min.js"></script>
	<script>
		// 弹窗

    // 倒计时
    function myTimer(){
		var sec = 3;
		var timer;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }
	
	function tijiaoguo(){
		message("已经提交过了!");
	}
	
    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }	
		var path, signature;
		var $sigdiv = $("#signature").jSignature('init', { height: '16rem', width: '100%' });
		$(function () {
			$("#resetBtn").on('click', function () {
				$sigdiv.jSignature("reset");
			});
			$("#qm-button").on('click', function () {
				var datapair = $sigdiv.jSignature("getData", "image");
				$("input[name='signature']").val(datapair[1]);

				signature = $("input[name='signature']").val();
				path = 'data:image/png;base64,' + signature;
				var qq = $("#qq").val();
				mesg_default();
				if(path != '' && path != null){
					$.post(
						"{:U('Info/qm')}",
						{
							signature:path
						},
						function (data,state){
							if(state != "success"){
								message("请求数据失败,请重试!");
							}else if(data.status == 1){
								$("#qmsrc").attr('src', path);
								message("保存成功!");
								window.location.href = "{:U('Index/jiekuang')}";
							}else{
								message(data.msg);
							}
						}
					);
				}else{
					message("手写签名填写不完整!");
				}
			});
		});
	</script>
</body>

</html>