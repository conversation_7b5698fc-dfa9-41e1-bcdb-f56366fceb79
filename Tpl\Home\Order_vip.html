<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title> <Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">
<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newindex-09d04b32f3.css">
</head>
<body>
    <!-- header -->
    <header class="mui-bar mui-bar-nav hnav">
		<a href="{:U('Index/index')}" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
		<h1 class="mui-title">VIP开通</h1>
	</header>
	<!-- header end-->
<div class="mui-content">
	<article class="mt10 jiekuan">
		<div class="oktit">确认升级VIP</div>
		<div class="cf okdan">
			<div class="oktable">
				<span class="fc9 listit">升级费用</span>
				<span>￥198</span>
			</div>
		</div>
	</article>

	<div class="protit sevagreee " style="background-color:#f5f5f9;">
		<a class="logBtn" href="javascript:subForm();">提交订单</a>
	</div>
</div>

<div class="deowin2" style="display:none;" id="deowin31">
    <div class="deocon2">
        <div class="divpad2" style="text-align:center;height:110px">
            <p class='tex' style="color: #4c4c4c;line-height: 30px;font-size:16px;"></p>
        </div>
        <div class="wobtn">
            <!-- 一个按钮用这个结构 -->
                <a id="winbtn3" href="javascript:;">确定</a>
        </div>
    </div>
</div>
<div class="emask" id="mask3" style="display: none;"></div>
<script src="__PUBLIC__/home/<USER>/jquery-1-fe84a54bc0.11.1.min.js"></script>
<script>
var isload = false;//避免重复提交

function subForm(){

    if(isload){
        $(".tex").html('请勿重复提交!');
        $("#deowin31").show();
        $('#mask3').show();
    }else{
    	isload = true;
	    //提交获取支付订单号
	    $.post(
	    	"{:U('Order/vip')}",
	    );
	    //isload = false;
    }
}
</script>
<div style="display: none;">
	<Somnus:sitecfg name="sitecode"/>
</div>
</body>
</html>