<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>上传支付凭证 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">

	<style>
		.upload_box_icon {
			width: 50px;
			position: absolute;
			top: 30px;
			left: 50%;
			margin-left: -25px;
			height: 50px;
			border-radius: 50%;
			background: #E5E5E5;
			color: #ffffff;
			text-align: center;
			line-height: 50px;
		}

		.upload_box {
			width: 100%;
			height: 150px;
			margin: 20px 0 0;
			border:  solid 2px #E5E5E5;
			border-radius: 5px;
			color: #E5E5E5;
			text-align: center;
			position: relative;
		}

		.up_txt {
			top: 85px;
		}

		#sfz_zm_div {
			height: 100%;
		}

		#sfz_zm_div img {
			height: 100%;
			width: 100%;
		}
	</style>

</head>

<body>

	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">上传支付凭证</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>

	</div>

	<div class="identity">
		<form action="" enctype="multipart/form-data">
			<div class="upload_group">
				<div class="am-g">
					<div class="am-u-sm-12">
						<b>上传支付凭证照片</b>
					</div>
				</div>
				<div class="am-g">
					<div class="am-u-sm-12">
						<div class="upload_box">
							<div id="sfz_zm_div"></div>
							<input type="hidden" id="sfz_zm" />
							<div style="display:none;">
								<input type="file" id="sfz_zm_input"
									onchange="uploadImg('sfz_zm','sfz_zm_div',this);" />
							</div>
							<div class="upload_box_icon" onclick="Selfile('sfz_zm_input');">
								<i class="am-icon-camera am-icon-fw"></i>
							</div>
							<span class="up_txt">上传上传支付凭证</span>
						</div>
					</div>
				</div>
			</div>
			<div class="fix_bottom">
				<div class="am-g">
					<button type="button" class="am-btn am-btn-block" id="add-button" onclick="saveInfo();">

						确认提交
					</button>
				</div>
			</div>
		</form>
	</div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script type="text/javascript">
		var isupload = false;
		//判断如果已经上传了图片就显示
		var sfz_zm = "{$data.img}";
		if (sfz_zm != '') {
			$("#sfz_zm").val(sfz_zm);
			$("#sfz_zm_div").html('<img src="' + sfz_zm + '">');
		}

		$('#sel').change(function () {
			change('sel', 'sela')
		});
		$('.inputblur').click(function () {
			$(this).blur();
			$('.nofocus').blur();
		});

		// 弹窗

		// 倒计时
		function myTimer() {
			var sec = 3;
			var timer;
			clearInterval(timer);
			timer = setInterval(function () {
				console.log(sec--);
				if (sec == 1) {
					$(".message").addClass("m-hide");
					$(".message").removeClass("m-show");
				}
				if (sec == 0) {
					$(".message").hide();
					$(".message").removeClass("m-hide");
					clearInterval(timer);
				}
			}, 1000);
		}

		// 弹窗内容
		function message(data) {
			msg = $(".message p").html(data);
			$(".message").addClass("m-show");
			$(".message").show();

			myTimer();

		}

		// 初始化弹窗
		function mesg_default() {
			msg = '';
			$(".message").hide();
			$(".message").removeClass("m-show");
			$(".message").removeClass("m-hide");
		}

		function Selfile(inputid) {
			if (isupload != false) {
				message("其他文件正在上传...请稍后");
			} else {
				$("#" + inputid).click();
			}
		}
		function uploadImg(hiddenid, divid, obj) {
			var filename = $(obj).val();
			if (filename != '' && filename != null) {
				isupload = true;
				var pic = $(obj)[0].files[0];
				var fd = new FormData();
				fd.append('imgFile', pic);
				$.ajax({
					url: "__PUBLIC__/main/js/kindeditor/php/upload_json.php",
					type: "post",
					dataType: 'json',
					data: fd,
					cache: false,
					contentType: false,
					processData: false,
					success: function (data) {
						if (data && data.error == '0') {
							message("上传成功");
							var imgurl = data.url;
							$("#" + divid).html('<img src="' + imgurl + '">');
							$("#" + hiddenid).val(imgurl);
						} else {
							message("上传出错了...");
						}
					},
					error: function () {
						message("上传出错了...");
					}
				});
				isupload = false;
			}
			isupload = false;
		}

		function checkval(val_) {
			if (val_ == '' || val_ == null) {
				return false;
			} else {
				return true;
			}
		}

	
	//保存资料
	function saveInfo(){
			var cardphoto_1 = $("#sfz_zm").val();
			var type = "{$data.type}";
			var id = "{$data.id}";
			var ordernum = "{$data.ordernum}";
			if(!id || !type || !cardphoto_1){
				message("参数有误!");return;
			}
			if(checkval(cardphoto_1)){
				$.post(
						"{:U('Order/savevoucher')}",
						{
							img:cardphoto_1,
							id:id,
							type:type,
							ordernum:ordernum,
						},
						function (data,state){
							if(state != "success"){
								message("请求数据失败,请重试!");
							}else if(data.status == 1){
								message("保存成功!");
								setTimeout(function(){
									window.location.href = data.payurl;
								},2000);
							}else{
								message(data.msg);
							}
						}
				);
			}else{
				showalert("凭证不完整,请检查!");
			}
			}

	</script>


</body>

</html>