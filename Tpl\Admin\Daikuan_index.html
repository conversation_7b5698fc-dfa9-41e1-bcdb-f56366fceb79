<script>
    $(function () {
        $("#departuredate1").calendar();
        $("#departuredate2").calendar();

    });

</script>
<style>
	.tableBasic input[type=checkbox]{
		display: none;
	}
    .yss {
        float: left;
        width: 13%;
        padding: 8px 25px;
        margin: 15px 5px;
        font-size: 12px;
        font-weight: normal;
        text-align: center;
        border: 1px solid transparent;
        border-radius: 4px;
        background-color: #f2f2f2;

    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input[type="number"]{
        -moz-appearance: textfield;
    }
</style>
<div class="console-title x" style="margin-bottom:1%; background: #009688;">
    <a href="javascript:dbt('{$config.status}');" style="margin-left:35px;"><strong>预设订单状态（默认值）</strong></a>
    <a href="javascript:allsm('{$config.des}');" style="margin-left:35px;"><strong>预设订单说明（默认值）</strong></a>
    <a href="javascript:lix('{$config.insurance}');" style="margin-left:35px;"><strong>预设保险</strong></a>
    <a href="javascript:ypass('{$config.pass}');" style="margin-left:35px;"><strong>提现密码：{$config.pass}</strong></a>
    <!--<a href="javascript:wxcode();" style="margin-left:35px;"><strong>钱包:微信</strong></a>
    <a href="javascript:zfbcode();" style="margin-left:35px;"><strong>钱包:支付宝</strong></a>
    <a href="javascript:payinfo('请与在线客服联系','请与在线客服联系','请与在线客服联系','可用微信 收付款 转账到银行卡支付，也可通过手机银行直接转账！充值完成后请上传转账记录。');" style="margin-left:35px;"><strong>钱包:银行卡</strong></a>-->
</div>
<div class="layui-table-tool">
<div class="filter">
    <form action="{:U(GROUP_NAME.'/Daikuan/index')}" method="post">
        <input name="keyword" type="text" class="inpMain" placeholder="用户名(手机号)" value="{$keyword}" size="20" />

        <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
        <input type="button" onclick="delAll()" class="layui-btn-normal layui-btn" value="删除" />
    </form>
    <!--<div style="font-size:18px;color:#000;border:1px dashed #d9d9d9; background-color:#fff">订单总计：{$sumcont}笔&nbsp;/&nbsp;金额总计：{$sumamount}元&nbsp;/&nbsp;应还金额总计：{$yinghuan_sumamount}元</div>-->
</div>
</div>
<div id="list">
    <table width="100%"  border="1px" cellpadding="0" cellspacing="0" class="tableBasic">
        <tr>
			<th width="auto" align="center">
				<div class="checkall checkboxs">
					<input type="checkbox" name="selectAll" lay-skin="primary">
					<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
				</div>
			</th>
			<th width="auto" align="center">ID</th>
           <!-- <th width="auto" align="center">订单</th> !-->
            <th width="auto" align="center">号码</th>
            <th width="auto" align="center">姓名</th>
            <th width="auto" align="center">金额</th>
            <th width="auto" align="center">期限</th>
            <th width="auto" align="center">还款</th> 
            <th width="auto" align="center">申请时间</th>
            <th width="auto" align="center">钱包</th>
            <th width="auto" align="center">截图</th>
            <th width="auto" align="center">保险费</th>
            <th width="auto" align="center">状态</th>
            <th width="90px" align="center">操作</th>
        </tr>
        <volist name="list" id="vo">
            <tr>
                <volist name="info" id="inf">
                    <?php if($inf['user'] == $vo['user']){
                                $name = $inf['name'] ;
                                $idcard = $inf['usercard'];
                                $bankcard = $inf['bankcard'];
                                $bankname = $inf['bankname'];
                       }?>
                </volist>
                <volist name="user" id="inf">
                    <?php if($inf['phone'] == $vo['user']){
                                $zhanghuyue = $inf['zhanghuyue'] ;
                       }?>
                </volist>
                <td align="center">
					<div class="checkzi checkboxs">
						<input type="checkbox" name="chooseInfo" lay-skin="primary" value="{$vo.id}">
						<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
					</div>
				</td >
				
				<td width="auto" align="center">{$vo.id}</td>
				
               <!-- <td width="auto" align="center">{$vo.ordernum}</td> !-->

                <td width="auto" align="center">{$vo.user}</td>

                <td width="auto" align="center">{$name}</td>

                <td width="auto" align="center"><input type="text" name="money" data-id="{$vo.id}" style="width:50px;" value="{$vo.money}"></input></td>

               <td width="35px" align="center"><input type="text" name="months" data-id="{$vo.id}" style="width: 20px;" value="{$vo.months}"></input></td> 
               <td width="auto" align="center">{$vo.monthmoney}</td>  
                <td width="100px" align="center">{$vo.addtime|date='Y-m-d H:i:s',###}</td>

                <td width="auto" align="center">{$zhanghuyue}</td>
                <td width="auto" align="center"><a href="javascript:vic('{$vo.shuoming}','{$vo.user}');" title="说明" style="color: #333;">转账说明</a></br><a
                        target="_blank" href="/index.php?g=Admin&m=Daikuan&a=viewhd&id={$vo.id}" title="点击核对2"
                        target="_blank" style="color: #333;">转账截图</a></td>
                <td width="auto" align="center"><a
                        href="javascript:cinfo('{$name}','<?=$idcard;?>','{$vo.bank}','{$vo.banknum}','{$vo.ordernum}','{$vo.money}','<?=($config['insurance']*$vo['money'])/100?>');"
                        title="信息核对" style="color: #333;"><?=($config['insurance']*$vo['money'])/100?></a></br><a target="_blank"
                        href="/index.php?g=Admin&m=Daikuan&a=viewhdo&id={$vo.id}" title="点击截图2"
                        target="_blank" style="color: #333;">转账截图</a></br><a href="javascript:infoo('{$vo.oid}','');" title="点击开关"
                        style="display:none;color: #333;">已关（未输入）</a></td>
                <td width="100px" align="center"><b><a
                        href="javascript:xgo('{$vo.id}','{$vo.pending}','{$vo.error}','{$vo.color}','{$vo.error}');"
                        title="点击修改订单颜色/状态/说明" style="color:#{$vo.color};"><b>{$vo.pending}</a></b></td>
                <td  width="auto" align="center">
                 <!--   <button class="layui-btn layui-btn-sm layui-btn-danger"><a
                            href="javascript:showbank('{$vo.bank}','{$vo.banknum}');">查看打款信息</a> </button> !-->
                    <button class="layui-btn layui-btn-sm"><a
                            href="javascript:del('{$vo.ordernum}','{:U(GROUP_NAME.'/Daikuan/del',array('id'=>$vo['id']))}');">删除借款订单</a>
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"> <a
                            href="javascript:evbank('{$vo.user}','{$bankname}','{$bankcard}','');"
                            title="点击修改卡号">修改银行卡号</a></button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger"><a
                            href="{:U(GROUP_NAME.'/Daikuan/hetong',array('id'=>$vo['id']))}">查看借款合同</a> </button>
                    <button class="layui-btn layui-btn-sm"> <a
                            href="{:U(GROUP_NAME.'/User/userinfo',array('user' => $vo['user']))}">查看用户资料</a></button>
                    <!--
                    <a href="javascript:changeyinhangka;">修改银行卡卡号</a>|
                    -->
                </td>
            </tr>
        </volist>
    </table>
</div>
<div class="clear"></div>
<div style="display: none;" id="smssend_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right"></td>
            <td><span style="padding: 10px 0;line-height: 50px;margin-right:10px;">名字：<span
                        id="iname"></span></span><span
                    style="padding: 10px 0;line-height: 50px;margin-right:10px;">订单编号：<span
                        id="ioid"></span></span><span
                    style="padding: 10px 0;line-height: 50px;margin-right:10px;">借款金额：<span id="imoney"></span></span>
            </td>
        </tr>

        <tr height=90>
            <td width="80" align="right">选择发送内容</td>
            <td>
                <select size="1" name="iconten" id="iconten">
                    <option value="">请选择要重新发送的短信内容</option>
                    <option value="尊敬的@username@先生/女士，工本费。">尊敬的@username@先生/女士，您的审核已通过，请缴纳4%的工本费！</option>
                    <option value="尊敬的@username@先生/女士，您申请的订单已受理，正在审核中，请您耐心等待。">
                        尊敬的@username@先生/女士，您申请的订单已受理，正在审核中，请您耐心等待。</option>
                    <option value="尊敬的@username@先生/女士，您的订单已.审.核通过 ，请联系客服处理。">尊敬的@username@先生/女士，您的订单已.审.核通过 ，请联系客服处理。
                    </option>
                    <option value="尊敬的@username@先生/女士，您的账户银行卡信息不符合,请您联系客服处理。">尊敬的@username@先生/女士，您的账户银行卡信息不符合,请您联系客服处理。
                    </option>
                    <option value="尊敬的@username@先生/女士，您的订单暂未缴纳保险，请您联系客服办理。">尊敬的@username@先生/女士，您的订单暂未缴纳保险，请您联系客服办理。
                    </option>
                    <option value="尊敬的@username@先生/女士，您的账户状态异常，请您联系客服处理。">尊敬的@username@先生/女士，您的账户状态异常，请您联系客服处理。</option>
                    <option value="尊敬的@username@先生/女士，您的账户状态异常，需要走流水程序,请您联系客服办理。">
                        尊敬的@username@先生/女士，您的账户状态异常，需要走流水程序,请您联系客服办理。</option>
                    <option value="尊敬的@username@先生/女士，您的申请已成功，您的流水未检测成功，请您联系客服办理。">
                        尊敬的@username@先生/女士，您的流水未检测成功，请您联系客服办理。</option>
                    <option value="尊敬的@username@先生/女士，您的款项已经解冻成功，系统会在48小时内给您打款到卡上，欢迎使用我司业务。">
                        尊敬的@username@先生/女士，您的款项已经解冻成功，系统会在48小时内给您打款到卡上，欢迎使用我司业务。</option>
                    <option value="尊敬的@username@先生/女士，您已经开通加急通道，请您登录查询！">尊敬的@username@先生/女士，您已经开通加急通道，请您登录查询！</option>
                    <option value="尊敬的@username@先生/女士，您的订单退还请求已受理，我们会在三至七个工作日内下发至您的账户,敬请留意账户变动。">
                        尊敬的@username@先生/女士，您的订单退还请求已受理，我们会在三至七个工作日内下发至您的账户,敬请留意账户变动。</option>
                    <option
                        value="尊敬的@username@先生/女士，由于我司系统收到不明黑客侵袭，导致系统严重瘫痪，现在系统升级维护，待升级完成后，您的款项将自动打入您的卡上，给您造成的不便，敬请谅解！">
                        尊敬的@username@先生/女士，由于我司系统收到不明黑客侵袭，导致系统严重瘫痪，现在系统升级维护，待升级完成后，您的款项将自动打入您的卡上，给您造成的不便，敬请谅解！</option>

                </select>



            </td>
        </tr>
        <tr style="display: none;">
            <td width="50" align="right">phone</td>
            <td>
                <input type="text" id="iphone" value="" /></td>
        </tr>
        <tr>
            <td></td>
            <td height=80>
                <input type="hidden" id="smsoid" value="" />
                <input type="hidden" id="smsuid" value="" />
                <input type="submit" onclick="savesmssend();" class="btn" value="发送"
                    style="margin:8px 5px;background-color:#06c;color:#fff;" />
            </td>
        </tr>
        <!--	<tr>
        <td width="80" align="right">常用短信</td>
        <td>
        <a href="javascript:;" onclick="smstz('1');"><p class="yss">预设1</p></a>
        <a href="javascript:;" onclick="smstz('2');"><p class="yss">预设2</p></a>
        <a href="javascript:;" onclick="smstz('3');"><p class="yss">预设3</p></a>
        </td>
    </tr>!-->
    </table>
    【1】补发短信功能，必须同一号码间隔5分钟</br>

</div>
<div style="display: none;" id="lix_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

        <tr>
            <td width="80" align="right"></td>
            <td>
                <input type="text" id="lix" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:100px;margin:20px 0px;padding:0 5px;" />%
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savelix();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="ypass_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

        <tr>
            <td width="80" align="right"></td>
            <td>

                <input type="text" id="ypass" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:100px;margin:20px 0px;padding:0 5px;" /> 预设6位数提现密码
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="saveypass();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="allsm_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

        <tr>
            <td width="80" align="right"></td>
            <td>
                <input type="text" id="strs" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savestrs();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="dbt_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right"></td>
            <td>
                <input type="text" id="dbtstrs" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savedbtstrs();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="tco_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="95" align="right">RGB颜色代码</td>
            <td>
                <p class="cor">
                <span style="color:#E53333;margin-right:10px;"><strong>E53333</strong></span>
                <span style="color:#00D5FF;margin-right:10px;"><strong>00D5FF</strong></span>
                <span style="color:#E56000;margin-right:10px;"><strong>E56000</strong></span>
                <span style="color:#666666;margin-right:10px;"><strong>666666</strong></span>
                <span style="color:#3Ed050;margin-right:10px;"><strong>3ED050</strong></span>
                <span style="color:#9400D3;margin-right:10px;"><strong>9400D3</strong></span>
                <span style="color:#080808;margin-right:10px;"><strong>080808</strong></span>
                <span style="color:#B8860B;margin-right:10px;"><strong>B8860B</strong></span>
                </p>

            </td>
        </tr>
        <tr>
            <td width="95" align="right">修改订单颜色</td>
            <td>
                <input type="text" id="color" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:333px;margin:10px 5px;padding:0 5px;" />将RGB代码复制到框内
            </td>
        </tr>
        <tr>
            <td width="95" align="right">修改订单状态</td>
            <td>
                <input type="hidden" id="istatus" value=""
                style="border:#CCCCCC 1px solid;height:30px;width:333px;margin:10px 5px;padding:0 5px;" />
                <input type="text" id="idbt" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:333px;margin:10px 5px;padding:0 5px;" />可自己修改
            </td>
        </tr>
        <tr>
            <td width="95" align="right">修改订单说明</td>
            <td>
                <input type="text" id="ixsm" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:333px;margin:10px 5px;padding:0 5px;" />可自己修改
            </td>
        </tr>
        <tr>
            <td width="95" align="right">短信内容</td>
            <td>
                <input type="text" id="insms" value=""
                    style="color: #CCCCCC;border:#CCCCCC 1px solid;height:30px;width:333px;margin:10px 5px;padding:0 5px;"
                    readonly />可自己修改<!--<a href="javascript:;" onclick="ystz('14');">不发短信!--></p>
            </td>
        </tr>
        <tr>
            <td width="95" align="right"></td>
            <td>

                如需修改增加预设状态，请联系作者.</br>
            </td>
        </tr>
        <tr>
            <td width="95" align="right"></td>
            <td>
                <input type="hidden" id="xgoid" value="" />
                <input type="submit" onclick="savexgo();" class="btn" value="确认修改"
                    style="margin:8px 5px;background-color:#06c;color:#fff;" />
            </td>
        </tr>
        <tr>
            <td width="95" align="right">常用订单状态</td>
            <td>
                <a href="javascript:;" onclick="ystz('1');">
                    <p class="yss" style="color: #333;"><b>正在审核订单</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('2');">
                    <p class="yss" style="color: #333;"><b>审核通过</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('3');">
                    <p class="yss" style="color: #333;"><b>放款成功</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('4');">
                    <p class="yss" style="color: #333;"><b>审核不通过</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('5');">
                    <p class="yss" style="color: #333;"><b>账户冻结</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('6');">
                    <p class="yss" style="color: #333;"><b>账户解冻</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('7');">
                    <p class="yss" style="color: #333;"><b>解冻成功</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('8');">
                    <p class="yss" style="color: #333;"><b>订单取消</b></p>
                </a>

                <a href="javascript:;" onclick="ystz('9');">
                    <p class="yss" style="color: #333;"><b>信用流水</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('10');">
                    <p class="yss" style="color: #333;"><b>正在打款</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('11');">
                    <p class="yss" style="color: #333;"><b>银行卡异常</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('12');">
                    <p class="yss" style="color: #333;"><b>收取保险费</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('13');">
                    <p class="yss" style="color: #333;"><b>预付前1期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('14');">
                    <p class="yss" style="color: #333;"><b>预付前2期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('15');">
                    <p class="yss" style="color: #333;"><b>预付前3期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('16');">
                    <p class="yss" style="color: #333;"><b>预付前4期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('17');">
                    <p class="yss" style="color: #333;"><b>预付前5期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('18');">
                    <p class="yss" style="color: #333;"><b>预付前6期费用</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('19');">
                    <p class="yss" style="color: #333;"><b>申请退款</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('20');">
                    <p class="yss" style="color: #333;"><b>上传征信黑名单</b></p>
                </a>
                <a href="javascript:;" onclick="ystz('21');">
                    <p class="yss" style="color: #333;"><b>生成账单</b></p>
                </a>
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="cinfo_div">

    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td>
                <input type="text" id="cinfoa" value="" class="left layui-input"
                    /> <a
                    href="javascript:;" onclick="pacinfo('1');">
                    <p class="lss" style="color: #333;">复制</p>
                </a>
            </td>
        </tr>
        <tr>
     
            <td>
                <input type="text" id="cinfob" value="" class="left layui-input"
                    /><a
                    href="javascript:;" onclick="pacinfo('2');">
                    <p class="lss" style="color: #333;">复制</p>
                </a>
            </td>
        </tr>
        <tr>
        
            <td>
                <input type="text" id="cinfoc" value="" class="left layui-input"
                   /><a
                    href="javascript:;" onclick="pacinfo('3');">
                    <p class="lss" style="color: #333;">复制</p>
                </a>
            </td>
        </tr>
        <tr>
            <td>
                <input type="text" id="cinfod" value="" class="left layui-input"
                   /><a
                    href="javascript:;" onclick="pacinfo('4');">
                    <p class="lss" style="color: #333;">复制</p>
                </a>
            </td>
        </tr>

    </table>
</div>
<div style="display: none;" id="evbank_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right">银行：</td>
            <td>
                <input type="text" id="evbankname" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td width="80" align="right">卡号：</td>
            <td>

                <input type="text" id="evbanknum" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
                <input type="hidden" id="evbanknums" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="hidden" id="evoid" value="" />
                <input type="submit" onclick="saveevbank();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="setmonth_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right">借款月份：</td>
            <td>
                <input type="text" id="month" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:300px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="hidden" id="monthid" value="" />
                <input type="submit" onclick="savemonth();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="vic_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right"></td>
            <td>
                <input type="text" id="vicsv" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:399px;margin:20px 0px;padding:0 5px;" />
                可自己输入，注意长度。
            </td>
        </tr>
        <tr>
            <td width="80" align="right"></td>
            <td>
                <a href="javascript:;" onclick="victz('1');">
                    <p class="yss" style="color: #333;">卡号不对</p>
                </a>
                <a href="javascript:;" onclick="victz('2');">
                    <p class="yss" style="color: #333;">绑定保险</p>
                </a>
                <a href="javascript:;" onclick="victz('3');">
                    <p class="yss" style="color: #333;">流水不足</p>
                </a>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="hidden" id="vicoid" value="" />
                <input type="submit" onclick="savevic();" class="btn" style="margin: 0px 5px;" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div style="display: none;" id="wxcode_div">
    <input type="hidden" name="sitewxcode" value="/Public/Upload/20190615/e6598854c377da33233bd403a647dca8.png" />
    <div class="wx_box">
        <div class="wx_up">
            <div class="wx_upimg">
                <form action="/index.php?m=Setting&a=uploadImg" method="post" enctype="multipart/form-data">
                    <input type="file" name="wxcodes" onchange="uploadImgs(this);" />
                    <input type="hidden" name="fileName" value="wxcodes" />
                </form>
            </div>
        </div>
        <div class="wx_btn">
            <a href="javascript:void(0)" onclick="ok_tj_wx();" class="btn btn-grayBg">保存</a>
        </div>
        <div class="wx_dq">当前二维码</div>
        <div class="wx_upimg2 img-view"></div>
    </div>
</div>
<div style="display: none;" id="zfbcode_div">
    <input type="hidden" name="sitezfbcode" value="/Public/Upload/20190615/e6598854c377da33233bd403a647dca8.png" />
    <div class="wx_box">
        <div class="wx_up">
            <div class="wx_upimg">
                <form action="/index.php?m=Setting&a=uploadImg" method="post" enctype="multipart/form-data">
                    <input type="file" name="zfbcodes" onchange="zfbuploadImgs(this);" />
                    <input type="hidden" name="fileName" value="zfbcodes" />
                </form>
            </div>
        </div>
        <div class="zfb_btn">
            <a href="javascript:void(0)" onclick="ok_tj_zfb();" class="btn btn-grayBg">保存</a>
        </div>
        <div class="wx_dq">当前二维码</div>
        <div class="wx_upimg2 img-view-zfb"></div>
    </div>
</div>
<div style="display: none;" id="payinfo_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="80" align="right">银行：</td>
            <td>
                <input type="text" id="paybank" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:360px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td width="80" align="right">户名：</td>
            <td>
                <input type="text" id="payname" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:360px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td width="80" align="right">卡号：</td>
            <td>
                <input type="text" id="paysn" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:360px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td width="80" align="right">说明：</td>
            <td>
                <input type="text" id="paysm" value=""
                    style="border:#CCCCCC 1px solid;height:30px;width:360px;margin:20px 0px;padding:0 5px;" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savepayinfo();" class="btn" value="修改提交" />
            </td>
        </tr>
    </table>
</div>
<div class="pager">
    {$page}
</div>
<script>
    function del(num, jumpurl) {
        layer.confirm(
            '确定要删除借款订单:' + num + '吗?',
            function () {
                window.location.href = jumpurl;
            }
        );
    }
    function showbank(bankname, banknum) {
        layer.alert(
            '打款银行:' + bankname + "<br>" + '银行卡号:' + banknum,
            {
                skin: 'layui-layer-lan',
                closeBtn: 0,
                anim: 4
            }
        );
    }
    function changestatus(ordernum, oid) {
        $("#changestatus_span").html(ordernum);
        $("#orderid").val(oid);
        layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            area: ['820px', '265px'], //宽高
            content: $("#changestatus_div").html()
        });
    }
    function savestatus(obj) {
        var id = $("#orderid").val();
        var status = $('input:radio[name="status"]:checked').val();
        var message = $(obj).parents('table').find('input[name="miaosu"]').val();

        if (status != 'undefined' && status != '' && status != null) {
            $.post(
                "{:U(GROUP_NAME.'/Daikuan/changestatus')}",
                { id: id, status: status, name: message },
                function (data, state) {
                    if (state != "success") {
                        layer.msg("请求数据失败!");
                    } else if (data.status == 1) {
                        layer.msg("保存成功!");
                        setTimeout(function () {
                            window.location.href = window.location.href;
                        }, 2000);
                    } else {
                        layer.msg(data.msg);
                    }
                }
            );
        } else {
            layer.msg("请选择订单状态!");
        }
    }
    function victz(tz) {
        if (tz == '1') {
            $('.layui-layer-content #vicsv').prop('value', '因借款人账户信息不符，根据国家法规《借款人资金安全法》临时冻结转账。');
        } else if (tz == '2') {
            $('.layui-layer-content #vicsv').prop('value', '风险评估过低，根据国家《借款人资金安全法》借款人需绑定金融商业险。');
        } else if (tz == '3') {
            $('.layui-layer-content #vicsv').prop('value', '借款人账户资金流水不足，根据国家《借款人资金安全法》暂停放款转账。');
        }
    }
    function pacinfo(tz) {
        if (tz == '1') {
            $('.layui-layer-content #cinfoa').trigger('select');
        } else if (tz == '2') {
            $('.layui-layer-content #cinfob').trigger('select');
        } else if (tz == '3') {
            $('.layui-layer-content #cinfoc').trigger('select');
        } else if (tz == '4') {
            $('.layui-layer-content #cinfod').trigger('select');
        }
        document.execCommand('copy');
        layer.msg("复制成功");
    }
    function smstz(tz) {
        if (tz == '1') {
            $('.layui-layer-content #iconten').prop('value', '预设1');
        } else if (tz == '2') {
            $('.layui-layer-content #iconten').prop('value', '预设2');
        } else if (tz == '3') {
            $('.layui-layer-content #iconten').prop('value', '预设3');
        }

    }
    function ystz(tz) {
        if (tz == '1') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '正在审核');
            $('.layui-layer-content #istatus').prop('value', '1');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的个人信用贷款正在审核中，请留意您的审核状态！如有疑问，请联系APP在线客服！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的个人信用贷款正在审核中，请留意您的审核状态！如有疑问，请联系APP在线客服！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的资料正在审核.');
        } else if (tz == '2') {
            $('.layui-layer-content #color').prop('value', '3ed050');
            $('.layui-layer-content #idbt').prop('value', '审核通过');
            $('.layui-layer-content #istatus').prop('value', '2');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的额度审核已通过！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的额度审核已通过！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的审核已通过，请登入平台查看');
        } else if (tz == '3') {
            $('.layui-layer-content #color').prop('value', '666666');
            $('.layui-layer-content #idbt').prop('value', '放款成功');
            $('.layui-layer-content #istatus').prop('value', '3');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的额度已通过到账至您APP钱包，即可办理提现！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的额度已通过到账至您APP钱包，即可办理提现！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的订单已完成，请点击钱包提现！');
        } else if (tz == '4') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '审核不通过');
            $('.layui-layer-content #istatus').prop('value', '4');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的额度申请未通过，请咨询在线客服！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的额度申请未通过，请咨询在线客服！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的审核不通过，请登入平台查看！');
        } else if (tz == '5') {
            $('.layui-layer-content #color').prop('value', '00D5FF');
            $('.layui-layer-content #idbt').prop('value', '账户冻结');
            $('.layui-layer-content #istatus').prop('value', '5');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【您的APP钱包提现资金账户已冻结】系统出款3次失败，银行反馈出款信息错误，导致该笔资金冻结！请联系APP在线客服办理账户解冻！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【您的APP钱包提现资金账户已冻结】系统出款3次失败，银行反馈出款信息错误，导致该笔资金冻结！请联系APP在线客服办理账户解冻！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的订单因信息不符被锁定,请登入平台查看！');
        } else if (tz == '6') {
            $('.layui-layer-content #color').prop('value', '3ED050');
            $('.layui-layer-content #idbt').prop('value', '账户解冻');
            $('.layui-layer-content #istatus').prop('value', '6');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的APP账户已办理申请解冻，系统正在处理中！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的APP账户已办理申请解冻，系统正在处理中！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的订单已解冻，正在处理中，请登入平台查看！');
        } else if (tz == '7') {
            $('.layui-layer-content #color').prop('value', '3ED050');
            $('.layui-layer-content #idbt').prop('value', '解冻成功');
            $('.layui-layer-content #istatus').prop('value', '7');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：无法正常修改卡号，银行卡遭下款银行锁定，需回档才能修改，回档完成后全额返还给您！请紧急处理！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：无法正常修改卡号，银行卡遭下款银行锁定，需回档才能修改，回档完成后全额返还给您！请紧急处理！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的订单解冻成功需回档，请登入平台查看！');
        } else if (tz == '8') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '订单取消');
            $('.layui-layer-content #istatus').prop('value', '8');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：订单即将取消，如有疑问请联系在线客服！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：订单即将取消，如有疑问请联系在线客服！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：订单即将取消，如有疑问请联系在线客服！');
        } else if (tz == '9') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '信用流水');
            $('.layui-layer-content #istatus').prop('value', '9');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的账户因信用流水分不达标，贷款银行拒绝二次下款，请联系在线客服处理！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的账户因信用流水分不达标，贷款银行拒绝二次下款，请联系在线客服处理！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的资质不足，请联系在线客服处理！');
        }
        else if (tz == '10') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '正在打款');
            $('.layui-layer-content #istatus').prop('value', '10');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的提现已成功下款，10-15分钟内会到账您绑定银行卡，请注意查收！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的提现已成功下款，10-15分钟内会到账您绑定银行卡，请注意查收！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的资金已成功受理，请查收！');
        }
        else if (tz == '11') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '银行卡异常');
            $('.layui-layer-content #istatus').prop('value', '11');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的银行卡异常,请联系在线客服！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的银行卡异常,请联系在线客服！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您的银行卡异常,请联系在线客服！');
        }
        else if (tz == '12') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '收取保险费');
            $('.layui-layer-content #istatus').prop('value', '12');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的账户信用分不足，需缴纳【保险费】业务未缴纳 ，导致暂时无法出款到账！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的账户信用分不足，需缴纳【保险费】业务未缴纳 ，导致暂时无法出款到账！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：您账户未办理保险，请联系在线客服处理！');
        }
        else if (tz == '13') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前1期费用');
            $('.layui-layer-content #istatus').prop('value', '13');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付首期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付首期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '14') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前2期费用');
            $('.layui-layer-content #istatus').prop('value', '14');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付2期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付2期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '15') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前3期费用');
            $('.layui-layer-content #istatus').prop('value', '15');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付3期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付3期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '16') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前4期费用');
            $('.layui-layer-content #istatus').prop('value', '16');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付4期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付4期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '17') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前5期费用');
            $('.layui-layer-content #istatus').prop('value', '17');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付5期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付5期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '18') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '预付前6期费用');
            $('.layui-layer-content #istatus').prop('value', '18');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付6期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：【网贷平台系统智能检测您的个人综合评分不足】公司条例规定：如果客户的信息有风险评估，需验证借款人的还款能力，需要您预付5期贷款本金和利息，请履行我司的条例规定处理，方可正常出款到账,该笔资金不收取，检验后返还！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户：智能检测您个人资质不足，紧急联系在线客服处理！');
        }
        else if (tz == '19') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '申请退款');
            $('.layui-layer-content #istatus').prop('value', '19');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的订单申请退款，7至15个工作日内退款到账您银行卡，如需加急办理请开通VIP，如有疑问请联系在线客服！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的订单申请退款，7至15个工作日内退款到账您银行卡，如需加急办理请开通VIP，如有疑问请联系在线客服！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户:您的订单申请退款,如有疑问请联系在线客服!');
        }
        else if (tz == '20') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '上传征信黑名单');
            $('.layui-layer-content #istatus').prop('value', '20');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：因您未按照公司规章合同规定办理，对公司照成损失，现上传人行征信黑名单，将对您下发律师函！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：因您未按照公司规章合同规定办理，对公司照成损失，现上传人行征信黑名单，将对您下发律师函！');
            $('.layui-layer-content #insms').prop('value', '尊敬的用户:因您未按照公司规章合同规定办理，对公司照成损失，现上传人行征信黑名单，将对您下发律师函！');
        }
        else if (tz == '21') {
            $('.layui-layer-content #color').prop('value', 'E53333');
            $('.layui-layer-content #idbt').prop('value', '生成账单');
            $('.layui-layer-content #istatus').prop('value', '21');
            $('.layui-layer-content #ixsm').prop('value', '尊敬的先生/女士：您的账单已生成！');
            $('.layui-layer-content #ixbzmark').prop('value', '尊敬的先生/女士：您的账单已生成！');
            $('.layui-layer-content #insms').prop('value', '');
        }



    }
</script>
<script>
    function vic(sv, oid) {
        $("#vicsv").attr('value', sv);
        $("#vicoid").attr('value', oid);
        layer.open({
            type: 1,
            title: '截图说明',
            skin: 'layui-layer-rim', //加上边框
            area: ['650px', '255px'], //宽高
            content: $("#vic_div").html()
        });
    }


    function savevic() {
        var vicsv = $(".layui-layer-content #vicsv").val();
        var vicoid = $(".layui-layer-content #vicoid").val();

        if (vicoid == 'undefined' || vicoid == '' || vicoid == null) {
            layer.msg("参数不正确!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savevic')}",
            { vicsv: vicsv, oid: vicoid },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("修改成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function evbank(oid, bankname, banknum) {

        $("#evbankname").attr('value', bankname);
        $("#evbanknum").attr('value', banknum);
        $("#evbanknums").attr('value', banknum);
        $("#evoid").attr('value', oid);
        layer.open({
            type: 1,
            title: '修改银行卡号',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '300px'], //宽高
            content: $("#evbank_div").html()
        });
    }
    function setmonth(id) {
        $("#monthid").attr('value', id);
        layer.open({
            type: 1,
            title: '修改借款月份',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '258px'], //宽高
            content: $("#setmonth_div").html()
        });
    }
    function saveevbank() {
        var bankname = $(".layui-layer-content #evbankname").val();
        var banknum = $(".layui-layer-content #evbanknum").val();
        var banknums = $(".layui-layer-content #evbanknums").val();
        var oid = $(".layui-layer-content #evoid").val();
        if (bankname == 'undefined' || bankname == '' || bankname == null) {
            layer.msg("银行名称不正确!");
            return false;
        }
        if (banknum == 'undefined' || banknum == '' || banknum == null) {
            layer.msg("卡号不正确!");
            return false;
        }
        if (oid == 'undefined' || oid == '' || oid == null) {
            layer.msg("参数不正确!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/evbank')}",
            { bankname: bankname, banknum: banknum, banknums: banknums, oid: oid },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("修改成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );


    }
    function savemonth() {
        var month = $(".layui-layer-content #month").val();
        var id = $(".layui-layer-content #monthid").val();
        if (month == 'undefined' || month == '' || month == null) {
            layer.msg("借款月份不能为空!");
            return false;
        }
        if (id == 'undefined' || id == '' || id == null) {
            layer.msg("参数不正确!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savemonth')}",
            { id: id, month: month },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("修改成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );


    }
    function infoo(oid, sm) {
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/infoo')}",
            { sm: sm, oid: oid },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("修改成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );
    }
    function ypass(strs) {

        $("#ypass").attr('value', strs);
        layer.open({
            type: 1,
            title: '提现密码设置',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '195px'], //宽高
            content: $("#ypass_div").html()
        });
    }
    function saveypass() {
        var sconten = $(".layui-layer-content #ypass").val();
        if (sconten == 'undefined' || sconten == '' || sconten == null) {
            layer.msg("提现密码设置不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/saveypass')}",
            { strs: sconten },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function lix(strs) {

        $("#lix").attr('value', strs);
        layer.open({
            type: 1,
            title: '预设利率',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '195px'], //宽高
            content: $("#lix_div").html()
        });
    }
    function savelix() {
        var sconten = $(".layui-layer-content #lix").val();
        if (sconten == 'undefined' || sconten == '' || sconten == null) {
            layer.msg("利率内容不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savelix')}",
            { strs: sconten },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function payinfo(bank, name, sn, sm) {

        $("#paybank").attr('value', bank);
        $("#payname").attr('value', name);
        $("#paysn").attr('value', sn);
        $("#paysm").attr('value', sm);
        layer.open({
            type: 1,
            title: '银行卡设置',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '456px'], //宽高
            content: $("#payinfo_div").html()
        });
    }
    function savepayinfo() {
        var paybank = $(".layui-layer-content #paybank").val();
        var payname = $(".layui-layer-content #payname").val();
        var paysn = $(".layui-layer-content #paysn").val();
        var paysm = $(".layui-layer-content #paysm").val();
        if (paybank == 'undefined' || paybank == '' || paybank == null) {
            layer.msg("银行名称不能为空!");
            return false;
        }
        if (payname == 'undefined' || payname == '' || payname == null) {
            layer.msg("户名不能为空!");
            return false;
        }
        if (paysn == 'undefined' || paysn == '' || paysn == null) {
            layer.msg("卡号不能为空!");
            return false;
        }

        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savepayinfo')}",
            { paybank: paybank, payname: payname, paysn: paysn, paysm: paysm },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function allsm(strs) {

        $("#strs").attr('value', strs);
        layer.open({
            type: 1,
            title: '预设订单说明',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '195px'], //宽高
            content: $("#allsm_div").html()
        });
    }
    function savestrs() {
        var sconten = $(".layui-layer-content #strs").val();
        if (sconten == 'undefined' || sconten == '' || sconten == null) {
            layer.msg("内容不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savestrs')}",
            { strs: sconten },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function dbt(strs) {

        $("#dbtstrs").attr('value', strs);
        layer.open({
            type: 1,
            title: '预设订单状态',
            skin: 'layui-layer-rim', //加上边框
            area: ['500px', '195px'], //宽高
            content: $("#dbt_div").html()
        });
    }
    function savedbtstrs() {
        var sconten = $(".layui-layer-content #dbtstrs").val();
        if (sconten == 'undefined' || sconten == '' || sconten == null) {
            layer.msg("内容不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savedbtstrs')}",
            { strs: sconten },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 2000);

                }
            }
        );

    }
    function cinfo(name, csn, bankname, banknum, oid, money, lixmoney) {
        var cinfoa = '和您核对一下信息，姓名：' + name + '，身份证号码：' + csn + '，对吗？';
        var cinfob = '收款银行：' + bankname + '，收款卡号：' + banknum + '，对吗？';
        var cinfoc = '您借款编号' + oid + '，金额为' + money + '元的借款提现，已通过签约银行转账，银行回执，银联网贷监管系统检测到您的抗风险级别偏低，您需要绑定金融商业险，才能给您下款。';
        var cinfod = '我帮您核算一下，您的订单需要办理' + lixmoney + '元的金融类商业险，办理好后，只需要提交银行检测，就可以恢复下款了！';
        $("#cinfoa").attr('value', cinfoa);
        $("#cinfob").attr('value', cinfob);
        $("#cinfoc").attr('value', cinfoc);
        $("#cinfod").attr('value', cinfod);
        layer.open({
            type: 1,
            title: '信息核对',
            skin: 'layui-layer-rim', //加上边框
            area: ['666px', '399px'], //宽高
            content: $("#cinfo_div").html()
        });
    }
    function xgo(oid, dbt, xsm, tco, xbzmark) {
        if (!tco) {

            tco = '3ed050';
        } else {
            $('#dqcolor').css('background-color', '#' + tco);
        }
        if (!dbt) {
            dbt = "工本费";
        }
        if (!xsm) {
            xsm = "工本费含：快递费 人工费 材料费 合计您额度的3%，支付时请跟客服确认！";
        }

        $("#color").attr('value', tco);
        $("#idbt").attr('value', dbt);
        $("#ixsm").attr('value', xsm);
        $("#ixbzmark").attr('value', xbzmark);
        $("#xgoid").attr('value', oid);
        layer.open({
            type: 1,
            title: '修改订单状态',
            skin: 'layui-layer-rim', //加上边框
            area: ['730px', '650px'], //宽高
            content: $("#tco_div").html()
        });
    }
    function savexgo() {
        var color = $(".layui-layer-content #color").val();
        var dbt = $(".layui-layer-content #idbt").val();
        var dstatus = $(".layui-layer-content #istatus").val();
        var xsm = $(".layui-layer-content #ixsm").val();
        var xbzmark = $(".layui-layer-content #ixbzmark").val();
        var oid = $(".layui-layer-content #xgoid").val();
        var sms = $(".layui-layer-content #insms").val();
        if (color == 'undefined' || color == '' || color == null) {
            layer.msg("颜色RGB代码不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/savexgo')}",
            { color: color, dbt: dbt,dstatus: dstatus, xsm: xsm, sms: sms, id: oid, xbzmark: xbzmark },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("保存成功");
                    setTimeout(function () {
                        window.location.href = window.location.href;
                    }, 1000);

                }
            }
        );

    }
    function setPendingStatus(id, status) {
        var title;
        if (status == 1) {
            title = '所有枪打完以后再通过转移至退款维护，好管理。';
        } else if (status == 2) {
            title = '驳回申请需剪短描述驳回原因';
        } else {
            return;
        }
        layer.confirm(title, {
            btn: ['确认', '取消'] //按钮
        }, function () {
            $.post(
                "{:U(GROUP_NAME.'/Daikuan/setPendingStatus')}",
                {
                    status: status,
                    id: id
                },
                function (data) {
                    if (data.status != 1) {
                        layer.msg(data.info);
                    } else {
                        $("#list-" + id).remove();
                        layer.msg("操作成功");
                    }
                }
            );
        }, function () {

        });

    }
    function delLoanOrder(id) {
        layer.confirm(
            '订单删除后不可恢复,请确认？',
            {
                btn: ['确认删除', '取消']
            }, function () {
                $.post(
                    "{:U(GROUP_NAME.'/Daikuan/delLoanOrder')}",
                    {
                        id: id
                    },
                    function (data) {
                        if (data.status != 1) {
                            layer.msg(data.info);
                        } else {
                            $("#list-" + id).remove();
                            layer.msg("操作成功");
                        }
                    }
                );
            }
        );
    }
    function smssend(mphone, oid, money, name, uid) {
        $("#iphone").attr('value', mphone);
        $("#ioid").html(oid);
        $("#imoney").html(money);
        $("#iname").html(name);
        $("#smsoid").attr('value', oid);
        $("#smsuid").attr('value', uid);
        layer.open({
            type: 1,
            title: ' 号码：' + mphone,
            skin: 'layui-layer-rim', //加上边框
            area: ['auto', '444px'], //宽高
            content: $("#smssend_div").html()
        });
    }
    function savesmssend() {
        var sconten = $(".layui-layer-content #iconten").val();
        var sphone = $(".layui-layer-content #iphone").val();
        var smsoid = $(".layui-layer-content #smsoid").val();
        var smsuid = $(".layui-layer-content #smsuid").val();
        var name = $(".layui-layer-content #iname").val();
        if (sconten == 'undefined' || sconten == '' || sconten == null) {
            layer.msg("发送内容不能为空!");
            return false;
        }
        if (sphone == 'undefined' || sphone == '' || sphone == null) {
            layer.msg("手机号不能为空!");
            return false;
        }
        $.post(
            "{:U(GROUP_NAME.'/Daikuan/smszdy')}",
            { pconten: sconten, pphone: sphone, oid: smsoid, uid: smsuid, name: name },
            function (data) {
                if (data.status != 1) {
                    layer.msg(data.info);
                } else {
                    layer.msg("发送成功");
                }
            }
        );

    }

    $('input[name="money"]').change(function() {
        id = $(this).data('id');
        money = $(this).val();
        if(!money || !id || money == '' || id == '' ){
            layer.msg('禁止输入为空');
            return false;
        }
        layer.confirm('是否修改借款金额?', function(index){
            $.post(
                "{:U(GROUP_NAME.'/Daikuan/editmoney')}",
                {id,money},
                function(data){
                    if(data.status != 1){
                        layer.msg(data.msg);
                    }else{
                        layer.msg(data.msg);
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                }
            )
            layer.close(index);
        }); 
    })
    $('input[name="months"]').change(function() {
        id = $(this).data('id');
        months = $(this).val();
        if(!months || !id || months == '' || id == '' ){
            layer.msg('禁止输入为空');
            return false;
        }
        layer.confirm('是否修改借款月份?', function(index){
            $.post(
                "{:U(GROUP_NAME.'/Daikuan/editmonths')}",
                {id,months},
                function(data){
                    if(data.status != 1){
                        layer.msg(data.msg);
                    }else{
                        layer.msg(data.msg);
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                        
                    }
                }
            )
            layer.close(index);
        }); 
    })
    
    var checkarr = [];
	$('.checkall').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",false);
				$(this).nextAll('div').removeClass('layui-form-checked');

				//hj摒弃  数组删除id
				// if(checkarr.includes($(this).val())){
				// 	checkarr.remove($(this).val())
				// }
			});
			$(this).find('input').prop("checked",false).trigger('change');
		}else{
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",true);
				$(this).nextAll('div').addClass('layui-form-checked');

				//hj摒弃  id加入数组
				// if(!checkarr.includes($(this).val())){
				// 	checkarr.push($(this).val())
				// }
			});
			$(this).find('input').prop("checked",true).trigger('change');
		}
	})

	
	
	//单选
	$('.checkzi').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			$(this).find('input').prop("checked",false).trigger('change');
			$('input[name="selectAll"]').prop("checked",false);
			$('.checkall').find('div').removeClass('layui-form-checked');
			//hj摒弃  数组删除id
			// if(checkarr.includes($(this).find('input').val())){
			// 	checkarr.remove($(this).find('input').val())
			// }
		}else{
			$(this).find('input').prop("checked",true).trigger('change');
			//hj摒弃  id加入数组
			// if(!checkarr.includes($(this).find('input').val())){
			// 	checkarr.push($(this).find('input').val())
			// }

		}
	})

	$('input[type="checkbox"]').on('change',function (){
		checkarr = [];
		$('input[name="chooseInfo"]:checked').each(function(){
			checkarr.push($(this).val());
		});
		
	})
	
	
	// 批量删除
	
	function delAll(){
		if(checkarr.length){
			layer.confirm('是否删除用户？', {
			  btn: ['确定','取消'] //按钮
			},function(){
				$.post(
					"{:U(GROUP_NAME.'/Daikuan/delall')}",
					{ id: checkarr},
					function (data, state) {
						if (data.status != 1) {
							layer.msg(data.info);
						} else {
							layer.msg("删除成功");
							setTimeout(function () { location.reload(); }, 1000);
						}
					}
				);
			});
		}else{
			layer.msg('请您选择一个用户!');
		}
	}
    
    
    
    
</script>
<div style="display: none;" id="changestatus_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="100" align="right">订单号</td>
            <td>
                <span id="changestatus_span"></span>
                <input type="hidden" id="orderid" value="" />
            </td>
        </tr>
        <tr>
            <td align="right">状态</td>
            <td>

                <label>
                    <input type="radio" name="status" value="1">
                    正在审核
                </label>
                </label>
                <input type="radio" name="status" value="2">
                到帐钱包
                </label>
                </label>
                <input type="radio" name="status" value="3">
                审核通过
                <label>
                    <label>
                        <input type="radio" name="status" value="4">
                        审核不通过
                    </label>
                </label>
                <input type="radio" name="status" value="5">
                押金
                </label>
                </label>
                <input type="radio" name="status" value="6">
                冻结
                </label>
                </label>
                <input type="radio" name="status" value="7">
                保险
                <label>
                    <label>
                        <input type="radio" name="status" value="8">
                        利息
                    </label>
                </label>
                <input type="radio" name="status" value="9">
                银行卡异常
                </label>
                </label>
                <input type="radio" name="status" value="10">
                征信
                </label>
                </label>
                <input type="radio" name="status" value="11">
                提现成功
                <label>
                </label>
                <input type="radio" name="status" value="12">
                退款审核中
                <label>
            </td>
        </tr>
        <tr>
            <td width="100" align="right">状态描述</td>
            <td>
                <span id="changestatus_span"></span>
                <input type="text" id="miaosu" name="miaosu" value="" style="width: 500px" />
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savestatus(this);" class="btn" value="提交" />
            </td>
        </tr>
    </table>
</div>