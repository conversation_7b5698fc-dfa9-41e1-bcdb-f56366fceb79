html{

    font-size: 65%;

}

html,body{

    margin: 0;

    padding: 0;

    height: 100%;

    font-family: arial,\5FAE\8F6F\96C5\9ED1,sans-serif;

    -webkit-text-size-adjust: 100%;

    -ms-text-size-adjust: 100%;

}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, p, blockquote, table, th, td, embed{

    padding: 0;

    margin: 0;

    /*background-color: #f4f4f4;*/

    font-size: 1.5rem;

}

body{

    font-size: 1.4rem;

    background: #f4f4f4;

    color: #666;

}

a{

    color: #333;

    text-decoration: none;

}



.top{

    position: relative;

    height: 4.4rem;

    width: 100%;

    background-color: #0697da;

    text-align: center;

    color: #fff;

    line-height: 4.4rem;

    font-size: 2rem;

}

.top_backleft{

    left: 1.5rem;

    background: url(../../img/mobile/ico-top4.png) no-repeat left;

    background-size: contain;

}

.top_ico{

    width: 2.1rem;

    height: 4.4rem;

    position: absolute;

    cursor: pointer;

    z-index: 10;

    top: 0;

}

.top .r_off{

    right: 1.5rem;

    color: #fff;

    position: absolute;

    height: 4.4rem;

    top: 0;

    line-height: 4.4rem;

    font-size: 1.6rem;

}

.main_input{

    background-color: #fff;

    width: 100%;

    height: 4.4rem;

    line-height: 4.2rem;

    position: relative;

}

.main_input .margin_lr{

    position: relative;

    overflow: hidden;

}

.margin_lr{

    margin-left: 1.5rem;

    margin-right: 1.5rem;

}

.border_tb{

    border-top: 1px solid #e1e1e1;

    border-bottom: 1px solid #e1e1e1;

}

.main_input .hl{

    width: 8rem;

    position: absolute;

    display: block;

    top: 0;

    line-height: 4.4rem;

    left: 0;

    z-index: 9;

}

.main_input .input_box{

    width: 100%;

    position: relative;

    line-height: 4.2rem;

    background-color: #fff;

}

.main_input .input_box input{

    width: 100%;

    font-size: 1.6rem;

    line-height: normal;

    padding: 1rem 0;

    border: 0;

    background: none;

    color: #333;

    padding-left: 8rem;

    box-sizing: border-box;

}

.main_input .po_r{

    position: absolute;

    right: 0;

    top: 1rem;

    background-color: #fff;

    height: 2.4rem;

    line-height: 2.4rem;

    padding-left: 1rem;

}

.btn_orange{

    background: #f60;

    color: #fff;

}

.btn{

    height: 4.4rem;

    font-size: 1.8rem;

    border: none;

    cursor: pointer;

    border-radius: .8rem;

}

.footer_all{

    width: 100%;

    font-size: 1.6rem;

    line-height: 2.4rem;

    margin-top: 1rem;

}

.jk_cardIdUP{

    background-color: #fff;

}

.pdb_4{

    padding-bottom: 4rem;

}

.jk_cardIdUPCom{

    width: 92%;

    margin: 2rem auto;

    background-color: #f4f4f4;

    height: 15.5rem;

    position: relative;

    overflow: hidden;

}

.jk_cardIdUPCom input[type=file]{

    width: 100%;

    height: 100%;

    opacity: 0;

    z-index: 100;

    position: relative;

}

.jk_cardIdUPCom span:nth-of-type(1){

    position: absolute;

    left: 50%;

    top: 50%;

    background: url(../../img/mobile/addUp.png) no-repeat top left;

    width: 3.3rem;

    height: 3.2rem;

    background-size: cover;

    margin-left: -1.65rem;

    margin-top: -1.6rem;

}

.jk_cardIdUPCom span:nth-of-type(2){

    font-size: 1.3rem;

    color: #1b8eee;

    position: absolute;

    left: 50%;

    top: 65%;

    margin-left: -75px;

    width: 150px;

    text-align: center;

}

.jk_cardIdUPCom img{

    position: absolute;

    left: 0;

    top: 0;

    width: 100%;

    height: auto;

    border: none;

    vertical-align: middle;

}

.jk_closeP{

    display: none;

}

.jk_closeP, .jk_closeP1{

    position: absolute;

    right: 13px;

    top: 5px;

    color: #fff;

    font-size: 30px;

    z-index: 1000;

    width: 40px;

    height: 40px;

    background: #000;

    border-radius: 50%;

    text-align: center;

    line-height: 40px;

}

.jk_cardIdUPText{

    width: 92%;

    margin: 0.5rem auto;

    font-size: small;

    color: #ff0000;

    position: relative;

    overflow: hidden;

}

.jk_cardIdUP form{

    background-color: #fff;

}

.jk_cardIdImg{

    width: 92%;

    margin: 1rem auto;

}

.jk_cardIdInfo{

    padding-left: 1.6rem;

    box-sizing: border-box;

    margin-top: 3rem;

}

.jk_cardIdInfo p{

    background-color: #fff;

}

.jk_cardIdInfo p:nth-of-type(1){

    color: #999;

}

.jk_bindCreditInfo{

    color: #999;

    padding-left: 1.6rem;

    box-sizing: border-box;

}

.editor_msg li{

    border-bottom: .1rem solid #e1e1e1;

    position: relative;

    padding: 1.2rem 0 1.2rem 1.5rem;

    background-color: white;

}

.editor_msg .line1, .editor_msg .line2{

    padding-left: 0rem;

    word-wrap: break-word;

    overflow: hidden;

}

.editor_msg .line1 .bt{

    width: 60%;

}

.editor_msg .line1 .bt, .editor_msg .line1 .date{

    float: left;

    box-sizing: border-box;

    font-size: 1.4rem;

    height: 2.2rem;

    line-height: 2.2rem;

}

.elli{

    height: 2.2rem;

    overflow: hidden;

    text-overflow: ellipsis;

    white-space: nowrap;

}

.editor_msg .line1 .date{

    width: 40%;

    text-align: right;

    padding-right: 1.5rem;

}

.editor_ing .line2{

    line-height: 2.2rem;

}

.editor_ing .line1, .editor_ing .line2{

    padding-left: 0rem;

}

.editor_msg .line2{

    padding-right: 1.5rem;

}

input[type=range] {

    -webkit-appearance: none;

    width: 300px;

    border-radius: 10px; /*这个属性设置使填充进度条时的图形为圆角*/

    background: -webkit-linear-gradient(#059CFA, #059CFA) no-repeat;

    background-size: 0% 100%;

}

input[type=range]::-moz-range-progress {

    background: linear-gradient(#059CFA, #059CFA) no-repeat;

    height: 13px;    

    border-radius: 10px;

}

input[type=range]::-ms-track {

    height: 25px;

    border-radius: 10px;

    box-shadow: 0 1px 1px #def3f8, inset 0 .125em .125em #0d1112;

    border-color: transparent; /*去除原有边框*/

    color: transparent; /*去除轨道内的竖线*/

}

input[type=range]::-ms-thumb {

    border: solid 0.125em rgba(205, 224, 230, 0.5);

    height: 25px;

    width: 25px;

    border-radius: 50%;

    background: #ffffff;

    margin-top: -5px;

    box-shadow: 0 .125em .125em #3b4547;

}

input[type=range]::-ms-fill-lower {

    /*进度条已填充的部分*/

    height: 22px;

    border-radius: 10px;

    background: linear-gradient(#059CFA, #059CFA) no-repeat;

}



input[type=range]::-ms-fill-upper {

    /*进度条未填充的部分*/

    height: 22px;

    border-radius: 10px;

    background: #ffffff;

}



input[type=range]:focus::-ms-fill-lower {

    background: linear-gradient(#059CFA, #059CFA) no-repeat;

}



input[type=range]:focus::-ms-fill-upper {

    background: #ffffff;

}

input[type=range]::-webkit-slider-thumb {

    -webkit-appearance: none;

}

input[type=range]::-webkit-slider-runnable-track {

    height: 15px;

    border-radius: 10px; /*将轨道设为圆角的*/

    box-shadow: 0 1px 1px #def3f8, inset 0 .125em .125em #0d1112; /*轨道内置阴影效果*/

}

input[type=range]:focus {

    outline: none;

}

input[type=range]::-webkit-slider-thumb {

    -webkit-appearance: none;

    height: 25px;

    width: 25px;

    margin-top: -5px; /*使滑块超出轨道部分的偏移量相等*/

    background: #ffffff; 

    border-radius: 50%; /*外观设置为圆形*/

    border: solid 0.125em rgba(205, 224, 230, 0.5); /*设置边框*/

    box-shadow: 0 .125em .125em #3b4547; /*添加底部阴影*/

}

input[type=range]::-moz-range-progress {

    background: linear-gradient(to right, #059CFA, white 100%, white);

    height: 13px;    

    border-radius: 10px;

}















