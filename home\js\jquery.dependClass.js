!function(e){e.baseClass=function(s){return s=e(s),s.get(0).className.match(/([^ ]+)/)[1]},e.fn.addDependClass=function(s,i){var t={delimiter:i?i:"-"};return this.each(function(){var i=e.baseClass(this);i&&e(this).addClass(i+t.delimiter+s)})},e.fn.removeDependClass=function(s,i){var t={delimiter:i?i:"-"};return this.each(function(){var i=e.baseClass(this);i&&e(this).removeClass(i+t.delimiter+s)})},e.fn.toggleDependClass=function(s,i){var t={delimiter:i?i:"-"};return this.each(function(){var i=e.baseClass(this);i&&(e(this).is("."+i+t.delimiter+s)?e(this).removeClass(i+t.delimiter+s):e(this).addClass(i+t.delimiter+s))})}}(jQ<PERSON>y);