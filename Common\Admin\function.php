<?php
/**
 * Created by PhpStorm.
 * User: Somnus
 * Date: 2016/11/9
 * Time: 17:28
 */
/**
 * [writeArr 写入配置文件方法]
 * @param  [type] $arr      [要写入的数据]
 * @param  [type] $filename [文件路径]
 * @return [type]           [description]
 */
function writeArr($arr, $filename) {
    return file_put_contents($filename, "<?php\r\nreturn " . var_export($arr, true) . ";");
}

function delDir($path){
    if ( $handle = opendir( "$path" ) ){
        while ( false !== ( $item = readdir( $handle ) ) ) {
            if ( $item != "." && $item != ".." ) {
                if ( is_dir( "$path/$item" ) ) {
                    delDir( "$path/$item" );
                } else {
                    unlink( "$path/$item" );
                }
            }
        }
        closedir( $handle );
    }
}
function num2str($num = 0)
{
    $str = '';
    if ($num > 9999999 || strlen(intval($num)) > 7) {
        $num = toMoney($num / 100000);
        if (intval($num) > 9) {
            $num = round($num, 1);
        }
        if (intval($num) > 99) {
            $num = round($num);
        }
        if ($num >= 1000) {
            $num = toMoney($num / 1000);
            $arr = str_split($num);
            foreach ($arr as $val) {
                $str .= '<em>' . $val . '</em>' . '
';
            }
            $str .= '<em>亿</em>';
            return $str;
        }
        $arr = str_split($num);
        foreach ($arr as $val) {
            $str .= '<em>' . $val . '</em>' . '
';
        }
        $str .= '<em>千万</em>';
    } else {
        if ($num > 9999999 || strlen(intval($num)) > 7) {
            $num = toMoney($num / 10000);
            if (intval($num) > 9) {
                $num = round($num, 1);
            }
            if (intval($num) > 99) {
                $num = round($num);
            }
            $arr = str_split($num);
            foreach ($arr as $val) {
                $str .= '<em>' . $val . '</em>' . '
';
            }
            $str .= '<em>百万</em>';
        } else {
            if ($num > 99999 || strlen($num) > 4) {
                $num = toMoney($num / 10000);
                if (intval($num) > 9) {
                    $num = round($num, 1);
                }
                if (intval($num) > 99) {
                    $num = round($num);
                }
                $arr = str_split($num);
                foreach ($arr as $val) {
                    $str .= '<em>' . $val . '</em>' . '
';
                }
                $str .= '<em>万</em>';
            } else {
                $arr = str_split($num);
                foreach ($arr as $val) {
                    $str .= '<em>' . $val . '</em>' . '
';
                }
            }
        }
    }
    return $str;
}

#金额格式化
function toMoney($num){
    $num_tmp = number_format($num,2,'.','');
    //$num_tmp = floatval($num_tmp);
    if($num_tmp < $num) return $num_tmp + 0.01;
    return $num_tmp;
}

// 通知短信
function sendTsms($phone,$content){
    $smsapi = C('cfg_SMSTZ_API'); //短信网关
    $user = C('cfg_SMSTZ_USER'); //短信平台帐号
    $pass = C('cfg_smsTZ_pass'); //短信平台密码
    $uid = C('cfg_smsTZ_uid');
    $content = '【' . C('cfg_smsTZ_name') . '】'.$content;
    $sendurl = $smsapi."action=send&userid=$uid&account=$user&password=$pass&mobile=$phone&content=$content&sendTime=&extno=";
    $result =file_get_contents($sendurl);
    if($result){
		$xml = simplexml_load_string($result);
		$json = json_encode($xml);
		$array = json_decode($json,TRUE);
		return $array['returnstatus'];
	}else{
		return 'error';
	}
    
    
}


