@font-face
{
	font-family: 'DP';
	src:url('../font/DINPro-Medium.otf');
}

.f_number
{
	font-family: 'DP';
}

html,body
{
	height: 100%;
}

.am-popup-hd .am-close {
    
    color: #ffffff !important;
    background: #ff0000 !important;
}

.back
{
	position: absolute;
	top: 20px;
	left: 20px;
	z-index: 9;
	width: 90%;
	text-align: left;
	font-size: 2.4rem;
}

.head-bg
{
	width: 100%;
	height: 30%;
	
	background: rgb(255, 209, 27);
	
	color: #ffffff;
	text-align: center;
	position: relative;

}

.forget .websitename_box
{
	bottom: 0;
}

.websitename_box
{
	position: absolute;
	width: 100%;
	height: 90%;
	font-size: 30px;
	letter-spacing: 5px;
	font-weight: 600;
}

.websitename
{
	width: 100%;
	height: 100%;
	display: flex;
	display: -webkit-flex; 
	bottom: 0;
	align-items: center;
	align-content: center;
}

.websitename div
{
	width: 100%;
}

.head_bottom_box
{
	position: absolute;
	width: 100%;
	bottom: 0;
	padding: 15px 0;
	letter-spacing: 5px;
	font-size: 18px;
}
.head_bottom
{
	display: flex;
	display: -webkit-flex; 
	bottom: 0;
	align-items: center;
	align-content: center;
}

.head_login,.head_register
{
	width: 50%;
}

.white_q
{
	height: 15px;
	width: 15px;
	position: absolute;
	background: #ffffff;
	bottom: -7px;
	transform: rotate(45deg);
	left: 22%;
}

.register .white_q
{
	left: 72%;
}

.title
{
	font-size: 3rem;
	font-weight: 400;
	text-align: center;
	padding: 4rem 0;
}

.form-box label
{
	text-align: right;
	
}

input
{  
	background:none !important;  
	outline:none !important;  
	border:0px !important;	
	width: 100%;
	letter-spacing: 0.2rem;
	line-height: 2rem !important;
	font-size: 1.7rem !important;
}

input:focus,textarea:focus,button:focus {
    outline: none !important;
    box-shadow:none !important;
	
}

.am-field-error, .am-form-error .am-form-field {
    border-color: none !important;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.am-active{
	outline: none !important;
    box-shadow:none !important;
}

button
{
	width: 100% !important;
	line-height: 130% !important;
	border-radius: 1000px !important;
	background: #dddddd;
	letter-spacing: 10px;
	font-weight: bold;
}

button:hover,
.reg_link:hover
{
	background: #ffd11b;
	color: #000000 !important;
}

.reg_link
{
	border: solid 2px #ffd11b;
	border-radius: 1000px;
	text-align: center;
	line-height: 35px;
	letter-spacing: 10px;
	color: #ffd11b;
	font-weight: bold;

}


.input-box
{
	border-bottom:solid 1px #c1c1c1;
	
}

.bottom-box
{
	margin-bottom: 0;
    position: absolute;
    width: 100%;
}

.menu-icon
{
	float: left;
	width: 30px;
	margin: 5px 0 0 0;
}

.login
{
	margin-top: 20px;
}

.hr-box
{
	color: #c1c1c1;
}

#vld-tooltip {
	position: absolute;
	z-index: 1000;
	padding: 5px 10px;
	background: #F37B1D;
	min-width: 150px;
	color: #fff;
	transition: all 0.15s;
	box-shadow: 0 0 5px rgba(0,0,0,.15);
	display: none;
}

#vld-tooltip:before {
	position: absolute;
	top: -8px;
	left: 50%;
	width: 0;
	height: 0;
	margin-left: -8px;
	content: "";
	border-width: 0 8px 8px;
	border-color: transparent transparent #F37B1D;
	border-style: none inset solid;
}

.am-field-error:focus, .am-form-error .am-form-field:focus {
    background-color: #fefffe;
    border-color: none !important;
    -webkit-box-shadow: none !important;
	box-shadow: none !important;
}

.am-field-valid:focus, .am-form-success .am-form-field:focus {
    background-color: #fefffe;
    border-color: none !important;
    -webkit-box-shadow: none !important;
	box-shadow: none !important;
}

.message
{
	position: fixed;
	margin: auto;
	top: 300px;
	bottom: 0;
	left: 0;
	right: 0;
	text-align: center;
	
	display: none;
	max-width: 60%;
	transform: scale(0);
	opacity: 0;
}

.message p
{
	padding: 10px 25px;
	background: rgba(0, 0, 0, 0.75);
	color: #ffffff;
	border-radius: 5px;
}

.m-show
{
	animation: myfirst 0.6s;
	-webkit-animation: myfirst 0.6s; 
	animation-fill-mode:forwards;
	-webkit-animation-fill-mode:forwards; 
}

@keyframes myfirst
{
	
    50% {
		transform: scale(0);
		opacity: 0;
	}
    100% {
		transform: scale(1);
		opacity: 1;
	}
}

.m-hide
{
	animation: hide 0.4s;
	-webkit-animation: hide 0.4s; 
	animation-fill-mode:forwards;
	-webkit-animation-fill-mode:forwards; 
}

@keyframes hide
{
	
    0% {
		transform: scale(1);
		opacity: 1;
	}
    100% {
		transform: scale(0);
		opacity: 0;
	}
}