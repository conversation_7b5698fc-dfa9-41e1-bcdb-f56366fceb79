<?php
class IndexAction extends CommonAction{
	
    public function index(){
    	//随机生成一批借款成功的
    	$phonestr = "13,15,17,18";
    	$phonearr = explode(",",$phonestr);
    	$redaydata = array();
		for($i=0;$i<30;$i++){
			$tmp = rand(0,count($phonearr)-1);
			$phone = $phonearr[$tmp].rand(0,9)."****".rand(0,9).rand(0,9).rand(0,9).rand(0,9);
			$money = rand(5,300)*100;
			$redaydata[] = array(
				'phone' => $phone,
				'money' => $money
			);
		}
		$this->redaydata = $redaydata;
    	$user = $this->getLoginUser();
    	$Order = D('order');
    	$count = $Order->where(array('user'=>$this->getLoginUser()))->count();
        //获取用户总数
        $usernum = D('user')->count();
        $this->usernum = $usernum;

        //获取总的借款数
        $money = $Order->sum('money');
        $this->money = $money;

        //获取总的还款数
        $huankuan = $Order->field(' SUM(months*monthmoney) as sum')->find();
        $this->huankuan = $huankuan['sum'];

    	$this->count = $count;
    	if(!$user) $user = 0;
		$this->user = $user;
		$discount = D('user')->field('Discount')->where(array('phone'=>$user))->find();
		$this->discount = $discount;

   


        $this->display();
    }
    
    public function jiekuang(){
		if(!$this->getLoginUser()){
			$this->redirect('User/login');
		}
		$userp = $this->getLoginUser();
		$userinfo = D('userinfo')->where(array('user'=>$userp))->find();
		
		if(!$userinfo){
			$this->redirect('Info/index');
			
		}
		$arr = [];
		
		foreach($userinfo as $key => $value){
			if($value == ''){
				$this->redirect('Info/index');
			}else{
				$arr[$key] = $value;
			}
			
		}
		
		// var_dump($arr);
		// exit();
    	//随机生成一批借款成功的
    	$phonestr = "13,15,17,18";
    	$phonearr = explode(",",$phonestr);
    	$redaydata = array();
		for($i=0;$i<30;$i++){
			$tmp = rand(0,count($phonearr)-1);
			$phone = $phonearr[$tmp].rand(0,9)."****".rand(0,9).rand(0,9).rand(0,9).rand(0,9);
			$money = rand(5,300)*100;
			$redaydata[] = array(
				'phone' => $phone,
				'money' => $money
			);
		}
		$user = $user = D('user')->where(array('phone'=>$userp))->find();
		if($user['Discount'] == 1 && $user['Discount_date'] > date("Y/m/d")){
			$this->Discountmonth = $user['Discount_month'];
		}else{
			$this->Discountmonth = 0;
		}
		$monthsss = C('cfg_dkmonths');
		$montharr = explode(",",$monthsss);
		// var_dump($montharr);
		$this->montharr = $montharr;
		$this->redaydata = $redaydata;
		$this->userinfo = $arr;
		$this->display();
    }
    public function download(){
    	
        $this->display();
	}
	
	public function randoms(){
		var_dump('sss');
	}

	// 新人优惠券
	public function discount(){
		$data = array('status' => 0,'msg' => '未知错误');
		$user = $this->getLoginUser();
		if(!$user){
			$data['msg'] = "登录并领取优惠券!";
			$data['status'] = -1;
			$this->ajaxReturn($data);
			exit();
		}
		$month = I("month",'','trim');
		$day = I("day",'','string');
		$datetime = date('Y-m-d',strtotime(date('Y-m-d').'+'.$day.'day'));
		$result = D('user')->where(array('phone'=>$user))->save(array('Discount'=>'1','Discount_month'=>$month,'Discount_date'=>$datetime));
		if($result){
			$data['msg'] = "领取成功!";
			$data['status'] = 1;
			$this->ajaxReturn($data);
			exit();
		}
		$data['msg'] = "出现一个问题!";
		$data['status'] = 0;
		$this->ajaxReturn($data);
		exit();
	}
}