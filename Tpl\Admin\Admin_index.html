
<div class="layui-table-tool">
<!--<h3 class="layui-table-tool-self"><a href="{:U(GROUP_NAME.'/Admin/add')}" class="actionBtn layui-btn">添加管理员</a></h3>
-->
    
    <div class="filter">
    
        <form action="{:U(GROUP_NAME.'/Admin/index')}" method="post">
    
            <font class="seach_span">管理员名称:</font>
    
            <input name="username" type="text" class="inpMain" value="{$seach_name}" size="20" />
    
            <input name="submit" class="btnGray layui-btn " type="submit" value="筛选" />
    
        </form>
    
    </div>

</div>



<style>
    
    .seach_span{

        float: left;

        line-height: 38px;

        font-size: 16px;

    }

</style>



<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

    <tr>

        <th width="30" align="center">编号</th>

        <th align="left">登录名</th>

        <th align="center">添加时间</th>

        <th align="center">最后登录时间</th>

        <th align="center">状态</th>

        <th align="center">操作</th>

    </tr>

    <foreach name="data" item="vo">

        <tr>

            <td align="center">{$vo.id}</td>
            <td>{$vo.username}</td>
            <td align="center">{$vo.addtime|date="Y-m-d H:i:s",###}</td>

            <td align="center">{$vo.lastlogin|date="Y-m-d H:i:s",###}</td>

            <td align="center">

                <if condition="$vo['status'] eq 1">

                    正常

                    <else/>

                    禁止

                </if>

            </td>

            <td align="center">
                <if condition="$vo.ischannel eq 0">
                    无需操作
                    <else/>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="{:U(GROUP_NAME.'/Admin/edit',array('editid' => $vo['id']))}"><i class="layui-icon"></i>编辑</a> </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"> <a href="javascript:;" onclick="delAdmin('{$vo.username}','{:U(GROUP_NAME.'/Admin/del',array('id' => $vo['id'] ))}');"><i class="layui-icon"></i>删除</a></button>
                </if>


            </td>

        </tr>

    </foreach>

</table>

<div class="pager">{$page}</div>

<script>

    function delAdmin(username,jumpurl){

        layer.confirm(

            '确定要删除该渠道:'+username+'吗?',

            function (){

                window.location.href = jumpurl;

            }

        );

    }

</script>