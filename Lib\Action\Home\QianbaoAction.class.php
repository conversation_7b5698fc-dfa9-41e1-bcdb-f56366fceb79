<?php
class QianbaoAction extends CommonAction{
	public function index() {
        if($this->getLoginUser()){
            $userinfo = D('userinfo')->where(array('user'=>$this->getLoginUser()))->find();
            $user = D('user')->where(array('phone'=>$this->getLoginUser()))->find();
            $order = D('order')->where(array('user'=>$this->getLoginUser()))->find();
			$bankcard=substr($userinfo['bankcard'],-4);
			$bankcard1=substr($userinfo['bankcard'],0,4);
			$this->bankcard = $bankcard;
			$this->bankcard1 = $bankcard1;
            $this->bankcard = $bankcard;
            $this->user = $user;
            $this->order = $order;
            $this->userinfo = $userinfo;
            if (IS_POST) {

					$money = I("money", '', 'trim');

					$password = I("password", '', 'trim');
					
					//获取渠道设置的提现密码
					$domain = $_SERVER['HTTP_HOST'];
					$data = D("admin")->where(array('link'=>$domain))->find();
					$channel= D('config')->where(array('id'=>1))->find();
					// //如果渠道没有设置提现密码，则获取总后台设置的提现密码
					// if(!$channel){
					// 		$channel = D('config')->where(array('uid'=>3))->find();
					// }

					if($password == $channel['pass']){
							//获取用户钱包的金额
							$userInfo = D('user')->where(array('phone' => $this->getLoginUser()))->find();
						
							if($userInfo['zhanghuyue']< $money ||$money == 0){
									$data['msg'] = "钱包金额不足，请重新输入金额！";
							}else{
									$result = D('user')->where(array('phone' => $this->getLoginUser()))->save(array('daihuan_money'=>$userInfo['daihuan_money']+$money));
									$res = D('user')->where(array('phone' => $this->getLoginUser()))->setDec('zhanghuyue',$money);
									//插入数据库
									$add = array(
										'user'=>$this->getLoginUser(),
										'time'=>time(),
										'money'=>$money,
										'channel_id'=>$data['id']
									);
									D('tixian')->add($add);
									if (!$res) {

										$data['msg'] = "未知错误";

									} else {

										$data['msg'] = "提现成功";

									}
							}
					}else{
							$data['msg'] = "提现密码不正确，请联系客服获取！";
					}

					$this->ajaxReturn($data);

					exit();

				}
            $this->display();
        }else{
        	$this->redirect('User/login');
        }
        
    }
	
    public function pay(){
    	if(!$this->getLoginUser()){
    		$this->redirect('User/login');
    	}
        $order = D('order')->where(array('user'=>$this->getLoginUser()))->find();
        $this->order = $order;
		$this->display();
	}
}
