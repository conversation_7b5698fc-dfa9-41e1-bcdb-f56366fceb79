function getYuegong (str){
	// 重新获取money存储设置其他值
	var money = parseInt(str);
	var benjin=0;
	xianshi();
}
function xianshi () {
	//alert(123);
    $.each($(".timeBtn"), function(index, val) {
    	var spanval = $("#timeSpanVal_"+index).html();
    	if(spanval == definamonth){
    		$('#'+index+'m').addClass('act').siblings('.timeBtn').removeClass('act');
    	}
    });
}

function reset() {
	var money = $('#money_str').html();
	//alert(money);
	money = new Number(money).toFixed(2);
	var month = parseInt($('.timeBtn.act span').html());
	var fuwufei = money * feilv[month - 1] / 100;
	fuwufei = fuwufei.toFixed(2);
	$('#fuwufei').html(fuwufei);
	var tmpval = feilv[month - 1] / 30;
	tmpval = new Number(tmpval).toFixed(2);
	$("#rixi").html(tmpval);
	var benjin = money / month;
	benjin = benjin.toFixed(2);
	$('#benjin').html('¥' + benjin);
	var yuegong = new Number(benjin) + new Number(fuwufei);
	yuegong = yuegong.toFixed(2);
	$('#yuegong').html(yuegong);
    $('#total').html('¥' + yuegong);
    nowmoney['money'] = money;
    $("#order_money").val(money);
    nowmoney['month'] = month;
    $("#order_month").val(month);
    nowmoney['fuwufei'] = fuwufei;
    nowmoney['benjin'] = benjin;
    nowmoney['yuegong'] = yuegong;
    nowmoney['total'] = parseFloat(benjin) + parseFloat(fuwufei);
}
$(function () {
			getYuegong();
	        var Num_1 = (MAXMONEY - MINMONEY) / 100;

			$('.single-slider').jRange({
				from: parseInt(MINMONEY),//滑动范围的最小值，数字，如0
				to: parseInt(MAXMONEY),//滑动范围的最大值，数字，如100
				step: parseInt(100),//步长值，每次滑动大小
				scale: [0*Num_1 + MINMONEY, 25*Num_1 + MINMONEY, 50*Num_1 + MINMONEY, 75*Num_1 + MINMONEY, 100*Num_1 + MINMONEY],//滑动条下方的尺度标签，数组类型，如[0,50,100]
				format: '%s',//数值格式
				width: 100+"%",//滑动条宽度签
				onstatechange: function(){
					var Money = $(".single-slider").val();
					$(".new-money").html(parseInt(Money).toFixed(2));
                    reset();
					
				}
			});
			//金额减按钮
			$("#subtract").on('click',function(){                
                var Money = $(".single-slider").val();                			
				if(Money >= (MINMONEY + 100)){
					Money = parseInt(Money) - 100;
				}else if(Money > MINMONEY && Money <= MINMONEY + 100){
					Money = parseInt(MINMONEY);
				}
				$(".single-slider").val(Money);
				$('.single-slider').jRange('setValue', Money);
			    $(".new-money").html(parseInt(Money).toFixed(2));
                reset();
			
			});

			//金额加按钮
			$("#plus").on('click',function(){
				var Money = $(".single-slider").val();
				if(Money == 0){
					Money = MINMONEY + 100;
				}else{
					if(Money < MAXMONEY - 100){
						Money = parseInt(Money) + 100;
					}else if(Money < MAXMONEY && Money >= MAXMONEY - 100){
						Money = parseInt(MAXMONEY);
					}
				}
				$(".single-slider").val(Money);
				$('.single-slider').jRange('setValue', Money);
				$(".new-money").html(parseInt(Money).toFixed(2));
                reset();
			});
		



        //随机生成 jslider-pointer 样式
        function pointer() {
            var random = Math.ceil(Math.random() * 3);
            switch (random) {
                case 1:
                    $(".high").css('background-image', "url("+PublicUrl+"/home/<USER>/coin.png)");
                    break;
                case 2:
                    $(".high").css('background-image', "url("+PublicUrl+"/home/<USER>/pig.png)");
                    break;
            }
        }
        pointer();
        //借款期限
        $('.timeBtn').click(function () {
            $(this).addClass('act').siblings('.timeBtn').removeClass('act');
            reset()
            return false
        });
        

    	middle33();
	    function middle33(){
	        var h = $('#deowin33').height();
	        var t = -h/2 + "px";
	        $('#deowin33').css('marginTop',t);
	    }
    	$('#winbtn4').click(function(){
        	$('#deowin4').hide();
        	$('#mask3').hide();
        	$('#deowin4 iframe').attr('src',''); 
      	});
    	middle1();
    	function middle1(){
        	var h = $('#deowin4').height();
        	var t = -h/2 + "px";
        	$('#deowin4').css('marginTop',t);
    	}
    	$('#winbtn5').click(function(){
        	$('#deowin5').hide();
        	$('#mask3').hide();
        	$('#deowin5 iframe').attr('src',''); 
    	});
    	middle2();
    	function middle2(){
        	var h = $('#deowin5').height();
        	var t = -h/2 + "px";
        	$('#deowin5').css('marginTop',t);
    	}
    	//提示关闭
        $("#winbtn3").click(function() {
            $('#deowin31').hide();
            $('#mask3').hide();
        });
        middle();
        function middle() {
            var w = $('#deowin3').width();
            var h = w / 0.64;
            $('.deocon11').css('height', h);
            var t = -h / 2 + "px";
            $('#deowin3').css('marginTop', t);
        }
});