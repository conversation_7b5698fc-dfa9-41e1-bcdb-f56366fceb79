<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="">
  <meta name="keywords" content="">
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">

  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
<title>收款银行卡 - 站长源码库（zzmaku.com） </title>

<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">


<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/iosSelect.css">
</head>
<body>


	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">收款银行卡</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
		
	</div>

	<div class="bank">
		<form action="" enctype="multipart/form-data">

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">持卡人姓名</div>
						<div class="am-u-sm-8">
							<input readonly="" type="text" value="{$userinfo.name}" placeholder="请输入">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">身份证号</div>
						<div class="am-u-sm-8">
							<input readonly="" type="text" value="{$userinfo.usercard}" placeholder="请输入">
						</div>
					</div>
				</div>
			</div>

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">开户银行</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="bank_id" id="bankId" value="">
							<input readonly="" type="text" value="{$userinfo.bankname}" id="showBank" placeholder="请选择银行">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">银行卡号</div>
						<div class="am-u-sm-8">
							<input type="number" value="{$userinfo.bankcard}" id="banknumber" placeholder="请输入">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">是否开通手机银行</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="shoubank" id="shoubank" value="">
							<input readonly="" type="text" value="<?php
							 switch($userinfo['shoubank']){
								case 1:echo '是';break;
								case 2:echo '否';break;
							 }
							?>" id="shoubankla" data-id="" data-value="" placeholder="">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">银手机预留号码</div>
						<div class="am-u-sm-8">
							<input type="text" value="{$userinfo.yuphone}" id="yuphone" placeholder="">
						</div>
					</div>
				</div>
			<div style="height: 70px;"><font color="red"><b>温馨提示：银行卡须是本人名下的借记卡(储蓄卡)</b></font></div>

			<div class="fix_bottom">
				<div class="am-g">
					<if condition="$info['bankinfo'] eq 0 ">
					<button type="button" class="am-btn am-btn-block" id="bank-button"  onclick="saveInfo();">确认提交</button>
					<else />
					<button type="button" class="am-btn am-btn-block" id="bank-button" disabled="disabled">确认提交</button>
                    </if>
				</div>
			</div>
		</form>
	</div>

	<div class="container"></div>
	
	<div class="message">
		<p></p>
	</div>
	
	

<script type="text/javascript">
    document.documentElement.addEventListener('touchmove', function(event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    }, false);
</script>

<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>

<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/bank.js"></script>
<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/iosSelect.js"></script>

<script type="text/javascript">
    var showBankDom = document.querySelector('#showBank');
    var bankIdDom = document.querySelector('#bankId');
    showBankDom.addEventListener('click', function () {
        var bankId = showBankDom.dataset['id'];
        var bankName = showBankDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [data],
            {
                container: '.container',
                title: '银行卡选择',
                itemHeight: 50,
                itemShowCount: 5,
                showAnimate:true,
                oneLevelId: bankId,
                callback: function (selectOneObj) {
                    bankIdDom.value = selectOneObj.id;
                    showBankDom.value = selectOneObj.value;
                    showBankDom.dataset['id'] = selectOneObj.id;
                    showBankDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	
	
	
	var yu = [
		{'id': '1', 'value': '是'},
		{'id': '2', 'value': '否'}
		
	];

	// 其他联系人关系
	var shoubanklaDom = document.querySelector('#shoubankla');
    var shoubankDom = document.querySelector('#shoubank');
    shoubanklaDom.addEventListener('click', function () {
        var yuId = shoubanklaDom.dataset['id'];
        var yuName = shoubanklaDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [yu],
            {
                container: '.container',
                title: '是否开通',
                itemHeight: 50,
                itemShowCount: 2,
                showAnimate:true,
                oneLevelId: yuId,
                callback: function (selectOneObj) {
                    shoubankDom.value = selectOneObj.id;
                    shoubanklaDom.value = selectOneObj.value;
                    shoubanklaDom.dataset['id'] = selectOneObj.id;
                    shoubanklaDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	//   // 弹窗
	//   function tijiaoguo(){
	// 	message("已经提交过了!");
	// }

    // 倒计时
    function myTimer(){
		var sec = 3;
		var timer;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }	
    
    // 银行卡正则
    function checkyhk(val_) {
		var yhk_zz = /\d{15}|\d{19}/;
		if (yhk_zz.test(val_)) {
			return true;
		} else {
			return false;
		}
	}
    
    
	function saveInfo(){
		var bankname = $("#showBank").val();
		var bankcard = $("#banknumber").val();
		var yuphone = $("#yuphone").val();
		var shoubank = $("#shoubank").val();
		
		mesg_default();
		if(bankname != '' && bankname != null && bankcard != '' && bankcard != null && checkyhk(bankcard)){
			$.post(
				"{:U('Info/bankinfo')}",
				{
					bankname:bankname,
					bankcard:bankcard,
					yuphone:yuphone,
					shoubank:shoubank
				},
				function (data,state){
					if(state != "success"){
						message("请求数据失败,请重试!");
					}else if(data.status == 1){
						message("保存成功!");
						window.location.href = "{:U('Info/qm')}";
					}else{
						message(data.msg);
					}
				}
			);
		}else{
			message("银行卡资料填写不完整!");
		}
	}
</script>
</body>
</html>