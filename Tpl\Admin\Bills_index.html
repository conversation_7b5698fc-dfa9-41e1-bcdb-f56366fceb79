<div class="layui-table-tool">
<div class="filter">
    <form action="{:U(GROUP_NAME.'/Bills/index')}" method="post">
        <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" placeholder="手机号"/>
        <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
    </form>
</div>
</div>
<div id="list">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <th width="50" align="center">ID</th>
            <th width="150" align="center">订单号</th>
            <th width="100" align="center">客户</th>
            <th width="150">客户姓名</th>
            <th width="150">借款总额</th>
            <th width="80">总期数</th>
            <th width="150" align="center">生成日期</th>
            <th align="center">操作</th>
        </tr>
        <volist name="list" id="vo">
            <tr>
                <td align="center">{$vo.id}</td>
                <td align="center">{$vo.ordernum}</td>
                <td align="center">{$vo.user}</td>
                <td align="center">{$data[$vo['user']]}</td>
                <td align="center" style="color:red;">{$vo.money}</td>
                <td align="center">{$vo.months}</td>
                <td align="center">{$vo.addtime|date='Y-m-d H:i:s',###}</td>
                <td align="center">
                    <if condition="$vouche[$vo['ordernum']] neq ''">
                        <button class="layui-btn layui-btn-sm"><a href="javascript:changecard('{$vo.ordernum}');"  class="sw">支付确认</a></button>
                        <else/>
                        <button class="layui-btn layui-btn-sm"><a href="javascript:changecard('{$vo.ordernum}');"  class="sw">查看期数详情</a></button>
                    </if>
                    <!--  <a href="javascript:del('{$vo.ordernum}','{:U(GROUP_NAME.'/Payorder/del',array('id'=>$vo['id']))}');">删除</a> -->
                </td>
            </tr>
        </volist>
    </table>
</div>
<div class="clear"></div>
<div class="pager">
    {$page}
</div>
<script>
    function del(num,jumpurl){
        layer.confirm(
            '确定要删除:'+num+'吗?',
            function (){
                window.location.href = jumpurl;
            }
        );
    }

    /**
     * 查看详情
     */
    function changecard(ordernum,type){
        var url = "{:U(GROUP_NAME.'/Bills/vodetail')}&ordernum="+ordernum+"&type="+type;
        //     alert(url);exit;
        $.get(url,function(res){

            layer.open({
                type: 2,
                title:'还款详情',
                skin: 'layui-layer-lan', //加上边框
                content:url,
                btn: ['关闭'],
                area: ['1000px', '650px'],
                end:function(){
                    window.location.reload()
                }
            })

        })

    }

</script>