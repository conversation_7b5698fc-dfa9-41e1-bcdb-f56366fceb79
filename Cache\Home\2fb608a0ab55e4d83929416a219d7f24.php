<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en" class="no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>我的资料 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">
</head>
<body>
	<div class="comm_top_nav">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href = '<?php echo U('User/index');?>';"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">我的资料</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
	<div
		style="background-color: rgb(255, 245, 203); width: 100%; margin: 5px auto; padding: 5px 0;text-align: center;">
		只需完成资料验证，即可申请提交借款！</div>

	<div class="am-g jbzl j_bt">
		<a href="<?php echo U('Info/baseinfo');?>">
			<div class="am-u-sm-7 jbzl_title">
				<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/image/myinfo/sfxx.png" />
				身份信息
			</div>
			<div class="am-u-sm-3 state">
			<?php if($info['baseinfo'] == 1): ?>完整
			<?php else: ?>
				不完整<?php endif; ?>
			

			</div>
			<div class="am-u-sm-2" style="text-align: center;"><i class="am-icon-angle-right am-icon-fw"></i></div>
		</a>
	</div>

	<div class="am-g jbzl j_bt">
		<a href="<?php echo U('Info/unitinfo');?>">
			<div class="am-u-sm-7 jbzl_title">
				<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/image/myinfo/zlxx.png" />
				资料信息
			</div>
			<div class="am-u-sm-3 state">
			<?php if($info['unitinfo'] == 1): ?>完整
			<?php else: ?>
				不完整<?php endif; ?>
			</div>
			<div class="am-u-sm-2" style="text-align: center;"><i class="am-icon-angle-right am-icon-fw"></i></div>
		</a>
	</div>

	<div class="am-g jbzl j_bt">
		<a href="<?php echo U('Info/bankinfo');?>">
			<div class="am-u-sm-7 jbzl_title">
				<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/image/myinfo/skyhk.png" />
				收款银行卡
			</div>
			<div class="am-u-sm-3 state">
					<?php if($info['bankinfo'] == 1): ?>完整
						<?php else: ?>
							不完整<?php endif; ?>
			</div>
			<div class="am-u-sm-2" style="text-align: center;"><i class="am-icon-angle-right am-icon-fw"></i></div>
		</a>
	</div>

	<div class="am-g jbzl j_bt">
		<a href="<?php echo U('Info/qm');?>">
			<div class="am-u-sm-7 jbzl_title">
				<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/image/myinfo/zm.png" />
				手写签名
			</div>
			<div class="am-u-sm-3 state">
				<?php if($info['sign'] == 1): ?>完整
				<?php else: ?>
					不完整<?php endif; ?>
			</div>
			<div class="am-u-sm-2" style="text-align: center;"><i class="am-icon-angle-right am-icon-fw"></i></div>
		</a>
	</div>

	<div
		style="background-color: rgb(255, 245, 203); width: 100%; margin: 5px auto; padding: 5px 0;text-align: center;">
		【例不动产证/车辆行驶证等】此项可填可不填</div>

	<div class="am-g jbzl j_bt">
		<a href="<?php echo U('Info/otherinfo');?>">
			<div class="am-u-sm-7 jbzl_title">
				<img class="am-circle am-img-responsive menu-icon" src="__PUBLIC__/home/<USER>/image/myinfo/add.png" />
				补充资料
			</div>
			<div class="am-u-sm-3 state">
					<?php if(!empty($otherinfo)): ?>完整
						<?php else: ?>
							不完整<?php endif; ?>
			</div>
			<div class="am-u-sm-2" style="text-align: center;"><i class="am-icon-angle-right am-icon-fw"></i></div>
		</a>
	</div>
	<div style="height: 50px;"></div>
	<div class="am-g">
		<div class="am-u-sm-11 am-u-sm-centered">
			<button type="button" class="am-btn" id="index-button">立即借款</button>

		</div>
	</div>
	<div class="message">
		<p></p>
	</div>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script type="text/javascript">
	$('#index-button').click(function(){
		window.location.href = "<?php echo U('Index/jiekuang');?>";
	})
	</script>
</body>

</html>