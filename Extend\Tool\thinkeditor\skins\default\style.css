.te_main{
	border:#b1bab7 1px solid;
	background:#FFF;
}
.te_toolbar_box {
	padding-bottom:5px;
	border-bottom:#b1bab7 1px solid;
	background:#f5f5f5;
}
.te_toolbar{
	margin-left:10px;
	margin-right:8px;
	overflow:hidden;
	_zoom: 1;
}

.te_toolbar:after {
	content: "\0020";
	display: block;
	height: 0;
	clear: both;
}
*+html .te_toolbar{
	overflow:hidden;
}

.te_main iframe{
}

/*textarea样式， 注意设置resize：none，不然火狐能拖动*/

.te_main textarea{
	border:none;
	resize:none;
	overflow-y:scroll;
}
.te_dialog{
	position:absolute;
	display:none;
}

.te_group{
	float:left;
	margin:1px;
	height:17px;
	overflow-y:hidden;
	margin-top:5px;
	margin-left:-2px;
	margin-right:2px;
}

.te_btn{
	float:left;
	margin-right:5px;
	width:27px;
	height:17px;
	cursor:pointer;
	font-size:8px;
	background-image:url(img/bg_img.png);
	background-repeat:no-repeat;
}

.te_line{
	width:0;
	height:17px;
	border-left:1px solid #fff;
	border-right:1px solid #b0baba;
	float:left;
	overflow:hidden;
	zoom:1;
	margin-right:5px;
}

.te_btn_source{
	background-position: 2px -4px ;
}
.te_btn_undo{
	background-position: 6px -29px;
}
.te_btn_redo{
	background-position: 6px -54px;
}
.te_btn_cut{
	background-position: 6px -79px;
}
.te_btn_copy{
	background-position: 6px -104px;
}
.te_btn_paste{
	background-position: 6px -129px;
}
.te_btn_pastetext{
	background-position: 6px -154px;
}
.te_btn_pastefromword{
	background-position: 6px -179px;
}
.te_btn_selectAll{
	background-position: 6px -204px;
}
.te_btn_blockquote{
	background-position: 6px -229px;
}
.te_btn_find{
	background-position: 6px -254px;
}
.te_btn_image{
	background-position: 6px -279px;
}
.te_btn_flash{
	background-position: 6px -304px;
}
.te_btn_media{
	background-position: 6px -329px;
}
.te_btn_table{
	background-position: 6px -354px;
}
.te_btn_hr{
	background-position: 6px -379px;
}
.te_btn_pagebreak{
	background-position: 6px -404px;
}
.te_btn_face{
	background-position: 6px -429px;
}
.te_btn_code{
	background-position: 6px -454px;
}
.te_btn_link{
	background-position: 6px -479px;
}
.te_btn_unlink{
	background-position: 6px -504px;
}
.te_btn_print{
	background-position: 6px -529px;
}
.te_btn_fullscreen{
	background-position: 6px -554px;
}
.te_btn_style{
	background-position: 6px -579px;
}
.te_btn_font{
	background-position: 6px -604px;
}
.te_btn_fontsize{
	background-position: 6px -629px;
}
.te_btn_fontcolor{
	background-position: 6px -654px;
}
.te_btn_backcolor{
	background-position: 6px -679px;
}
.te_btn_bold{
	background-position: 6px -704px;
}
.te_btn_italic{
	background-position: 6px -729px;
}
.te_btn_underline{
	background-position: 6px -754px;
}
.te_btn_strikethrough{
	background-position: 6px -779px;
}
.te_btn_unformat{
	background-position: 6px -804px;
}
.te_btn_leftalign{
	background-position: 6px -829px;
}
.te_btn_centeralign{
	background-position: 6px -854px;
}
.te_btn_rightalign{
	background-position: 6px -879px;
}
.te_btn_blockjustify{
	background-position: 6px -904px;
}
.te_btn_orderedlist{
	background-position: 6px -929px;
}
.te_btn_unorderedlist{
	background-position: 6px -954px;
}
.te_btn_indent{
	background-position: 6px -979px;
}
.te_btn_outdent{
	background-position: 6px -1004px;
}
.te_btn_subscript{
	background-position: 6px -1029px;
}
.te_btn_superscript{
	background-position: 6px -1054px;
}
.te_btn_about{
	background-position: 6px -1079px;
}

.te_bottom{
	border-top:#b1bab7 1px solid;
	position:relative;
	top:-1px;
	height:12px;
	overflow:hidden;
	zoom:1;
}
.te_resize_center{
	height:12px;
	overflow:hidden;
    cursor:n-resize;
	zoom:1;
	background:#f5f5f5 url(img/resize_center.jpg) center center no-repeat;
}
.te_resize_left{
	width:10px;
	height:10px;
    cursor:nw-resize;
	background:url(img/resize_leftjpg.jpg) no-repeat;
	position:absolute;
	bottom:0;
	right:0;
	overflow:hidden;
	zoom:1;
}

