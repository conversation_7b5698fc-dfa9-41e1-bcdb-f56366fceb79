<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>我的还款 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myrepayment.css">
	<style>
		.coupon_zero {
			width: 100%;
			height: 100%;
			text-align: center;
			margin: 70px 0 0;
			color: #a8a6a7;
		}
	</style>
</head>

<body>
	<div class="comm_top_nav">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('User/index')}'"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">我的还款</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
		<empty name="data">
		<div class="coupon_zero">
			暂无还款
			<img src="__PUBLIC__/home/<USER>/image/norepay.png" alt="" width="100%">
		</div>
		<else />
		<foreach name="data" item="vo"></foreach>
		<div id="repayment_overview" data-am-sticky>
			<div class="am-g">
				<div class="am-u-sm-8">
					借款编号:<span class="f_number">{$vo.ordernum}</span>
				</div>
				<div class="am-u-sm-4 jk_time">
					<span class="f_number">	
						<a href="{:U('Order/billinfo',array('ordernum' => $vo['ordernum']))}" style="color: #fff;">
						<php>
						if($vo['status'] == 50)echo "前往还款";
						if($vo['status'] == 51)echo "已结清-查看详情";
					</php>
				</a>
				</span>
				</div>
			</div>
			<div class="am-g">
				<div class="am-u-sm-12">
					借款金额<span class="f_number">￥<span class="jk_money">{$vo.money}</span></span>
				</div>
			</div>
			<div class="am-g">
				<div class="am-u-sm-12">
					总计<span class="f_number">{$vo.months}</span>期，
					每月还款金额￥<span class="f_number">{$vo.monthmoney}</span>
				</div>
			</div>
		</div>
	</empty>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
</body>

</html>