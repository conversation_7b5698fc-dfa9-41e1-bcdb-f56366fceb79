<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>常见问题 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<style>
		#question {
			background: #ffffff;
		}
	</style>

</head>

<body id="question">
	<div class="comm_top_nav">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('User/index')}'"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">常见问题</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
	<div class="am-panel-group" id="accordion">
       <foreach name="article" item="vo">
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">
				<h4 class="am-panel-title" data-am-collapse="{parent: '#accordion', target: '#do-not-say-<?php echo $vo['id'];?>'}">
					{$vo.title}</h4>
			</div>
			<div id="do-not-say-<?php echo $vo['id'];?>" class="am-panel-collapse am-collapse">
				<div class="am-panel-bd">
					{$vo.description} </div>
			</div>
		</div>
	</foreach>
	</div>


	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
</body>

</html>