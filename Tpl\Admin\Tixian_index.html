<style>
	.tableBasic input[type=checkbox]{
		display: none;
	}
</style>
<div class="layui-table-tool">
<div class="filter">

    <form action="{:U(GROUP_NAME.'/Bills/index')}" method="post">

        <input name="keyword" type="text" class="inpMain" placeholder="用户名"  value="{$keyword}" size="20" />
           <input name="sday" id="sday" type="text" placeholder="开始时间" class="inpMain  day1" value="{$sday}" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="16" />



        <input name="eday" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="eday" type="text" placeholder="结束时间" class="inpMain  day1" value="{$eday}" size="16" />

        <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
        <input type="button" onclick="delAll()" class="layui-btn-normal layui-btn" value="删除" />
        <span>订单总计：{$sumcont}笔&nbsp;/&nbsp;金额总计：{$sumamount}元</span>

    </form>
</div>
</div>
<div id="list">

        <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

            <tr>
				<th width="30" align="center">
					<div class="checkall checkboxs">
						<input type="checkbox" name="selectAll" lay-skin="primary">
						<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
					</div>
				</th>
                <th width="50" align="center">ID</th>

                <th width="100" align="center">用户名</th>

                <th width="150">提现金额</th>

                <th width="150" align="center">申请提现日期</th>

                <th align="50">状态</th>

                <th align="center">操作</th>

            </tr>

            <volist name="list" id="vo">

                <tr>
					<td align="center">
						<div class="checkzi checkboxs">
							<input type="checkbox" name="chooseInfo" lay-skin="primary" value="{$vo.id}">
							<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div>
						</div>
					</td >
                    <td align="center">{$vo.id}</td>

                    <td align="center">{$vo.user}</td>

					<td align="center">{$vo.money}</td>

                          <td align="center">{$vo.time|date='Y-m-d  H:i:s',###}</td>

                    	<td align="center" <eq name="mod" value="1"> style="background-color:#e4e4e4"</eq>>	<php>

							if($vo['zhuangtai'] == 0)echo "<font style='color:#F00;font-size:12px'>未确认</font>";

							if($vo['zhuangtai'] == 1)echo "<font style='color:#0fa100;font-size:12px; font-weight:bold'>已确认</font>";

                            if($vo['zhuangtai'] == 2)echo "<font style='color:#C00;font-size:12px; font-weight:bold'>已拒绝</font>";



						</php></td>

              <style>

			  </style>

                    <td align="center">

                       <div class="layui-btn layui-btn-sm layui-btn-danger"> <a  href="javascript:del('{$vo.ordernum}','{:U(GROUP_NAME.'/Tixian/del',array('id'=>$vo['id']))}');"><i class="layui-icon"></i>订单</a> </div>

                       <div class="layui-btn layui-btn-sm layui-btn-normal"> 

                        <a   href="javascript:queren('{$vo.ordernum}','{:U(GROUP_NAME.'/Tixian/queren',array('id'=>$vo['id']))}');">确认提现</a> </div>

                        <div class="layui-btn layui-btn-sm layui-btn-warm"> 

                                       <a  href="javascript:bohui('{$vo.ordernum}','{:U(GROUP_NAME.'/Tixian/bohui',array('id'=>$vo['id']))}');">驳回提现</a></div>

                                       
<!--
                        <div class="duanxin"> 
						

                         <a  href="{:U(GROUP_NAME.'/Tixian/duanxinqueren',array('id'=>$vo['id']))}">发送确认短信</a>

                        </div>               

 <div class="duanxin"> 

                         <a  href="{:U(GROUP_NAME.'/Tixian/duanxinbohui',array('id'=>$vo['id']))}">发送驳回短信</a>

                        </div> 
-->
                    </td>

                </tr>

            </volist>

        </table>

</div>

<div class="clear"></div>

<div class="pager">

    {$page}

</div>

<script>
	var checkarr = [];
	$('.checkall').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",false);
				$(this).nextAll('div').removeClass('layui-form-checked');

				//hj摒弃  数组删除id
				// if(checkarr.includes($(this).val())){
				// 	checkarr.remove($(this).val())
				// }
			});
			$(this).find('input').prop("checked",false).trigger('change');
		}else{
			
			$('input[name="chooseInfo"]').each(function(){
				$(this).prop("checked",true);
				$(this).nextAll('div').addClass('layui-form-checked');

				//hj摒弃  id加入数组
				// if(!checkarr.includes($(this).val())){
				// 	checkarr.push($(this).val())
				// }
			});
			$(this).find('input').prop("checked",true).trigger('change');
		}
	})

	
	
	//单选
	$('.checkzi').on('click',function () {
		$(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
		if($(this).find('input').is(':checked')){
			$(this).find('input').prop("checked",false).trigger('change');
			$('input[name="selectAll"]').prop("checked",false);
			$('.checkall').find('div').removeClass('layui-form-checked');
			//hj摒弃  数组删除id
			// if(checkarr.includes($(this).find('input').val())){
			// 	checkarr.remove($(this).find('input').val())
			// }
		}else{
			$(this).find('input').prop("checked",true).trigger('change');
			//hj摒弃  id加入数组
			// if(!checkarr.includes($(this).find('input').val())){
			// 	checkarr.push($(this).find('input').val())
			// }

		}
	})

	$('input[type="checkbox"]').on('change',function (){
		checkarr = [];
		$('input[name="chooseInfo"]:checked').each(function(){
			checkarr.push($(this).val());
		});
		
	})
	
	
	// 批量删除
	
	function delAll(){
		// console.log(checkarr);
		
		if(checkarr.length){
			layer.confirm('是否删除用户？', {
			  btn: ['确定','取消'] //按钮
			},function(){
				$.post(
					"{:U(GROUP_NAME.'/Tixian/delall')}",
					{ id: checkarr},
					function (data, state) {
						if (data.status != 1) {
							layer.msg(data.info);
						} else {
							layer.msg("删除成功");
							setTimeout(function () { location.reload(); }, 1000);
						}
					}
				);
			});
		}else{
			layer.msg('请您选择一个用户!');
		}
	}
	
	
	
    function del(num,jumpurl){

        layer.confirm(

                '确定要删除还款订单吗?',

                function (){

                    window.location.href = jumpurl;

                }

        );

    }

	

	

    function queren(num,jumpurl){

        layer.confirm(

                '确认提现订单吗?',

                function (){

                    window.location.href = jumpurl;

                }

        );

    }

	

	   function bohui(num,jumpurl){

        layer.confirm(

                '驳回提现订单吗?',

                function (){

                    window.location.href = jumpurl;

                }

        );

    }

</script>