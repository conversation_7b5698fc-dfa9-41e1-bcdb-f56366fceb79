;define("hiloan:app/component/amount-list/amount-list.vue",function(e,i,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var a=e("hiloan:node_modules/vue-touch/vue-touch"),o=(n(a),e("hiloan:components/fin-fg/util/index"));
e("hiloan:components/fin-fg/filter/index"),e("hiloan:components/fin-fg/directive/log/index");var s=e("hiloan:components/fin-ui/ui-dialog/index"),l=n(s);i.default={props:{dueInfo:{type:Object,required:!0}},data:function(){return{is:"amount-list",currentTotalPrinmat:0,overfeePrinamt:0,paidPrincipleSum:0,overfeerepaydate:"",repaydate:"",isDueDate:!1,billsLen:0,overDueDialog:!1,onDueDialog:!1}
},computed:{},created:function(){this.buildList()},methods:{buildList:function(){var e=this.dueInfo||{},i=e.bills||[];this.billsLen=i.length||0,this.billsLen&&(this.currentTotalPrinmat=parseInt(e.currentTotalMoney,10),this.overfeePrinamt=parseInt(e.overFeeTotalMoney,10),this.paidPrincipleSum=parseInt(e.paidMoneySum,10),this.overfeerepaydate=e.orderFirstOverdueDate,this.repaydate=e.recentPayDate,this.tid=i[0].tid,this.canRepay=i[0].canRepay,this.isDueDate=e.isDueDate)
},gotoRepay:function(e){return this.overfeePrinamt&&2===e?void(this.overDueDialog=!0):0===+this.isDueDate&&2===+e?void(this.onDueDialog=!0):void o.redirect("/hiloan/trans/tplConfirmRepay",{tid:this.tid,feeStatus:e,repayType:2===e?"current":"overdue"})
},gotoDetail:function(){o.redirect("/hiloan/trans/tplRepaymentDetails",{tid:this.tid})},gotoLoanRecord:function(){o.redirect("/hiloan/trans/tplHisBorrow")}},watch:{dueInfo:function(){this.buildList()}},components:{uiDialog:l.default}};
var r='<section class="fin-{{ is }}">\n    <!-- 逾期应还 -->\n    <section class="list-cell-item overdue-total" v-if="overfeePrinamt">\n        <i class="overdue-icon"></i>\n        <div class="left">\n            <div class="cell-title">逾期应还金额</div>\n            <div class="cell-money"><span class="num">{{overfeePrinamt | formatMoney}}</span>元</div>\n            <div class="cell-tip">逾期起始日 {{overfeerepaydate | formatDateTime}}</div>\n        </div>\n        <span class="right">\n            <div class="fin-ui-button x-small" @click="gotoRepay(3)">去还款</div>\n        </span>\n    </section>\n\n    <!-- 本期应还 -->\n    <section class="list-cell-item current-total" v-if="currentTotalPrinmat">\n        <div class="left">\n            <div class="cell-title">本期应还金额</div>\n            <div class="cell-money"><span class="num">{{currentTotalPrinmat | formatMoney}}</span>元</div>\n            <div class="cell-tip">最后还款日 {{repaydate | formatDateTime}}</div>\n        </div>\n\n        <span class="right">\n            <div class="fin-ui-button x-small" @click="gotoRepay(2)" v-log="\'gotoRepay\'">去还款</div>\n        </span>\n    </section>\n\n    <!-- 本期已还 -->\n    <section class="list-cell-item current-total" v-if="paidPrincipleSum" v-touch:tap="gotoDetail">\n        <div class="left">\n            <div class="cell-title">本期已还金额</div>\n            <div class="cell-money"><span class="num">{{paidPrincipleSum | formatMoney}}</span>元</div>\n            <div class="cell-tip">还款日 {{repaydate | formatDateTime}}</div>\n        </div>\n\n        <span class="right">\n            <i class="icon icon-arrow-right"></i>\n        </span>\n    </section>\n\n    <section v-if="billsLen" class="list-cell-item topay-order" v-touch:tap="gotoDetail" v-log="\'toBePaid\'">\n        <div class="left">待还借款</div>\n        <div class="right"><i class="icon icon-arrow-right"></i></div>\n    </section>\n\n    <section class="list-cell-item history-order" v-touch:tap="gotoLoanRecord" v-log="\'loanRecord\'">\n        <div class="left">借款记录</div>\n        <div class="right"><i class="icon icon-arrow-right"></i></div>        \n    </section>\n\n    <ui-dialog :show.sync="overDueDialog" type="alert" :head="false" ok="知道了" content="请先还清逾期部分欠款再还当期" @ok="this.overDueDialog = fasle"></ui-dialog>\n\n    <ui-dialog prefix="onDueDlg" :show.sync="onDueDialog" type="alert" title="还款提示" ok="我知道了" @ok="this.onDueDialog = fasle">\n        <div slot="content">\n            <div v-if="1 === +dueInfo.repayMode">\n                大额尊享不支持借款日当天还款\n            </div>\n            <div v-else="">\n                <div>大额尊享暂不支持提前还款</div>\n                <div>请于{{repaydate | formatDateTime}}还款日当天还款</div>\n            </div>\n        <div>\n    \n\n</div></div></ui-dialog></section>';
t&&t.exports&&(t.exports.template=r),i&&i.default&&(i.default.template=r),t.exports=i["default"]});
;define("hiloan:app/component/amount-list/index",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:app/component/amount-list/amount-list.vue"),a=o(u);
t.default=a.default,n.exports=t["default"]});
;define("hiloan:app/static/config/api-biz",function(a,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={activityinwhiterecord:"/hiloan/operation/activityinwhiterecord",repayResultInfo:"/hiloan/trans/repayresultinfo",applybasic:"/hiloan/api/bff?method=applybasic",applyresult:"/hiloan/api/bff?method=applyresult",loanRecord:"/hiloan/apply/loanRecord",genContract:"/hiloan/contract/genContract",bankcardlist:"/hiloan/approve/bankcardlist",loanCalculate:"/hiloan/approve/loanCalculate",getLoanFeatures:"/hiloan/approve/getLoanFeatures",preCalculate:"/hiloan/trans/preCalculate",repay:"/hiloan/trans/repay",prepay:"/hiloan/trans/prepay",hisBillList:"/hiloan/trans/hisBillList",repaymentPlan:"/hiloan/trans/repaymentPlan",setcache:"/hiloan/api/bff?method=setcache",activatebasic:"/hiloan/api/bff?method=activatebasic",activateresult:"/hiloan/api/bff?method=activateresult",activateverification:"/hiloan/api/bff?method=activateverification",getCurrentDueInfo:"/hiloan/trans/getCurrentDueInfo",homeStatus:"/hiloan/open/homestatus",checkRepayOrder:"/hiloan/trans/checkRepayOrder",firstLoan:"/hiloan/trans/firstloan",secondLoan:"/hiloan/trans/secondloan",queryLoan:"/hiloan/trans/queryloan",cancelOrder:"/hiloan/trans/cancelorder",getunionloaninfo:"/hiloan/trans/getunionloaninfo",joinwhite:"/hiloan/smartgate/joinwhite",applyGuideInfo:"/hiloan/apply/applyGuideInfo",openLog:"/hiloan/open/log"},n.exports=e["default"]
});
;define("hiloan:app/static/config/api",function(e,i,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var a=e("hiloan:node_modules/object-assign/index"),o=t(a),f=e("hiloan:components/fin-fg/util/request/apify"),l=t(f),u=e("hiloan:components/fin-fg/config/api-sdk"),d=t(u),s=e("hiloan:app/static/config/api-biz"),c=t(s),p=o.default({},d.default,c.default);
i.default=l.default(p),n.exports=i["default"]});
;define("hiloan:app/component/announce/announce.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});{var a=e("hiloan:components/fin-ui/ui-dialog/index"),o=i(a),s=e("hiloan:app/static/config/api"),l=i(s),c=(e("hiloan:components/fin-fg/util/index"),e("hiloan:node_modules/vue-touch/vue-touch"));
i(c)}t.default={props:{},data:function(){return{is:"announce",data:[],index:null,announceLen:null,actionClass:"",expireStartDialogTxt:"额度有效期为7天，额度失效后您需要重新申请<br/>借款成功后，剩余额度（如有）将会失效，结清借款后可再次申请额度",expireStartDialogShow:!1,msg:""}
},created:function(){var e=this;l.default.homeStatus({},{"x-silent":!0,"x-message":!1}).then(function(t){e.announceFilter(t)})},methods:{announceFilter:function(e){if(e.data&&e.data.length){for(var t=e.data.length-1;t>=0;t--){var n=e.data[t];
(""===n.msg||null==n.msg||null==n.opcode||""===n.opcode)&&e.data.splice(t,1)}if(!e.data.length)return;this.announceLen=e.data.length,this.data=e.data.sort(function(e,t){return t.type-e.type}),this.index=0,this.showAnnounce()
}},showAnnounce:function(){var e=this,t=this.data[this.index];this.msg=this.processMsg(t.msg),this.getActionClass(t),this.$nextTick(function(){var t=getComputedStyle(e.$els.inner).height,n=e.$els.outer;
n.style.height=t,n.style.opacity=1})},processMsg:function(e){if(e=e.replace(/{{blue}}/g,'<span class="blue">').replace(/{{\/blue}}/g,"</span>").replace(/{{br}}/g,"<br/>"),e.indexOf("{{p}}")>0){var t=e.split("{{p}}");
e={title:t[0]||"",content:t[1]||""}}return e},getActionClass:function(e){var t=+e.type,n=e.opcode+"";1===t&&(-1!==["202","203","501","502","702"].indexOf(n)?this.actionClass="close":-1!==["101","OP002","204"].indexOf(n)&&(this.actionClass="enter"))
},closeAnnouce:function(){var e=this;this.deleteNofity(this.data[this.index].type);var t=this.$els.outer;t.style.height=0,t.style.opacity=0,setTimeout(function(){e.index<e.announceLen-1&&(e.index+=1,e.showAnnounce(e.data[e.index]))
},1e3)},enterAnnounce:function(){var e=parseInt(this.data[this.index].type,10),t=this.data[this.index].opcode+"";if(1===e)switch(t){case"101":this.expireStartDialogShow=!0;break;case"204":this.$dispatch("goToVerify");
break;default:return}},deleteNofity:function(e){l.default.homeStatus({type:e,del:1},{"x-silent":!0,"x-message":!1})}},components:{uiDialog:o.default}};var u='<section class="fin-{{ is }} {{actionClass}}" v-el:outer="">\n    <div v-el:inner="" v-touch:tap="enterAnnounce">\n        <div class="announce-info">{{{msg}}}</div>\n        <i class="icon icon-arrow-right"></i>\n        <i class="icon icon-close" v-touch:tap="closeAnnouce"></i>\n    </div>\n</section>\n<ui-dialog :show.sync="expireStartDialogShow" type="alert" title="额度有效期" :content="expireStartDialogTxt" ok="知道了" @ok="this.expireStartDialogShow = false"></ui-dialog>';
n&&n.exports&&(n.exports.template=u),t&&t.default&&(t.default.template=u),n.exports=t["default"]});
;define("hiloan:app/component/announce/index",function(e,n,o){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var t=e("hiloan:app/component/announce/announce.vue"),a=u(t);
n.default=a.default,o.exports=n["default"]});
;define("hiloan:app/static/config/constant",function(n,e){"use strict";function t(n){return n&&n.__esModule?n:{"default":n}}function a(){var n={channel:"QBAO01",loginType:window.Agent.OS.wallet?window.Agent.OS.ios?"IOS001":"AND001":"H5",scene:"MAIN",productId:"JXJDD001",projectId:"JXJDD",bizline:"JXJDD",app:"hiloan",version:"1.0"};
G.constants.token&&(n.token=G.constants.token);var e=i.default.parse(location.search).transactionId||G.constants.transactionId;e&&(n.transactionId=e);var t=i.default.parse(location.search);return Object.keys(t).forEach(function(e){0===e.indexOf("md_")&&(n[e]=t[e])
}),n}Object.defineProperty(e,"__esModule",{value:!0}),e.defaultQuery=e.defaultArgs=void 0;{var o=n("hiloan:node_modules/query-string/index"),i=t(o),r=a();e.defaultArgs=r,e.defaultQuery=i.default.stringify(r)
}});
;define("hiloan:app/component/app-util/buildEntireUrl",function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:!0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=a.default.extend({},l.defaultArgs,n),u="";
return u=/^(http|https)/i.test(t)?t:location.protocol+"//"+location.host+t,e&&(u+="?"+i.default(o)),u};var u=t("hiloan:node_modules/underscore/underscore"),a=o(u),r=t("hiloan:components/fin-fg/util/request/util/serialize"),i=o(r),l=t("hiloan:app/static/config/constant");
n.exports=e["default"]});
;define("hiloan:app/component/app-util/index",function(n,t,e){"use strict";function o(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(t,"__esModule",{value:!0});var a=n("hiloan:app/static/config/api"),i=(o(a),n("hiloan:components/fin-fg/util/index")),r={};
t.default={isFromBack:function(){return"1"===document.querySelector("#is-from-back").value},isSelfLoan:function(n){return n&&n.unionloanMode&&"S"!==n.unionloanMode.toUpperCase()?!1:!0},getSelfInstitution:function(n){var t={};
return n.unionloanInstitutionList.forEach(function(n){"0001"===n.unionloanInstitutionCode&&(t=n)}),t},getUnionInstitution:function(n){var t={};return n.unionloanInstitutionList.forEach(function(n){"0001"!==n.unionloanInstitutionCode&&(t=n)
}),t},saveLoanCalcu:function(n,t,e,o){var a="t_"+n+"r_"+t+"p_"+e;r[a]=o},getLoanCalcu:function(n,t,e){var o="t_"+n+"r_"+t+"p_"+e;return r[o]},cleanLoanCalcu:function(){r={}},compareDate:function(n,t){if(n&&t){var e=i.format.datetime(n),o=new Date(Date.parse(e)),a=new Date(Date.parse(t));
return o>=a}return!1},systemVersion:function(){var n="";if(window.Agent.OS.android){var t=navigator.userAgent.toLowerCase(),e=t.match(/android\s([0-9\.]*)/);n=e?e[1]:""}else if(window.Agent.OS.ios){var o=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);
n=""+parseInt(o[1],10)+"."+parseInt(o[2],10)+"."+parseInt(o[3]||0,10)}return n}},e.exports=t["default"]});
;define("hiloan:app/component/app-util/openlogAutoSend",function(e,n,o){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}function i(e){var n=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),o=window.location.search.substr(1).match(n);
return null!=o?unescape(o[2]):null}Object.defineProperty(n,"__esModule",{value:!0});var r=e("hiloan:app/static/config/api"),d=t(r),a=function(){var e=i("productId")||"common",n=location.pathname.split("/").filter(function(e){return""!==e
});n.unshift(e);var o=n.join("_").toLowerCase();return function(){return o}}();n.default={sendOpenLogAction:function(e){if(!e)throw new Error("缺少事件描述参数");var n=[a(),e].join("_"),o={btnkey:n,queryword:i("queryword")},t=i("CH")||"";
t&&t.indexOf("dsp_")>-1&&(o.sid=i("sid"),o.media=i("media"),o.ideaid=i("ideaid"),o.keyword=i("keyword")),t&&t.indexOf("fengchao")>-1&&(o.md_queryword=i("md_queryword"),o.md_createid=i("md_createid")),d.default.openLog({CH:t,logData:o},{"x-silent":!0,"x-defaultDialog":!1})
}},o.exports=n["default"]});
;define("hiloan:app/static/config/env-conf",function(a,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});var J=document.querySelector("input#env"),X="online",D=J&&J.value||X,n={online:{WALLET_AUTH:"https://baifubao.baidu.com/jump?domain=https://www.baifubao.com/&uri=",LOAN_COUPON:"https://front.baidu.com/fms/tpl/coupon/couponChoose?type=loan&",REPAY_COUPON:"https://front.baidu.com/fms/tpl/coupon/couponChoose?type=repay&",SPNO:**********,FINANCE_SHOP:"https://jin.baidu.com/loan/apply/quick?page=myaccount&channel=shoubaicashloan",WALLET_COUPON_LIST:"https://wallet.baidu.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html",LARK_SYSTEM:"https://lark.baidu.com",FRONT_SYSTEM:"https://front.baidu.com",HILOAN_INDEX:"https://icash.baidu.com/hiloan/index",CLOAN_INDEX:"https://icash.baidu.com/cloan/index",switchMC:!0,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}},sandbox:{WALLET_AUTH:"https://baifubao.baidu.com/jump?domain=https://alltest.baifubao.com/&uri=",SPNO:**********,LOAN_COUPON:"https://qyfront.baidu.com/fms/tpl/coupon/couponChoose?type=loan&",REPAY_COUPON:"https://qyfront.baidu.com/fms/tpl/coupon/couponChoose?type=repay&",FINANCE_SHOP:"https://jin.baidu.com/loan/apply/quick?page=myaccount&channel=shoubaicashloan&orp_preview=1",WALLET_COUPON_LIST:"https://alltest.baifubao.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html?type=h5qianbao&",LARK_SYSTEM:"https://qylark.baidu.com",FRONT_SYSTEM:"https://qyfront.baidu.com",HILOAN_INDEX:"https://qyicash.baidu.com/hiloan/index",CLOAN_INDEX:"https://qyicash.baidu.com/cloan/index",switchMC:!0,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}},rd:{SPNO:**********,LOAN_COUPON:"http://cp01-front.epc.baidu.com:8099/fms/tpl/coupon/couponChoose?type=loan&",REPAY_COUPON:"http://cp01-front.epc.baidu.com:8099/fms/tpl/coupon/couponChoose?type=repay&",WALLET_COUPON_LIST:"https://m.baifubao.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html?type=h5qianbao&",FINANCE_SHOP:"http://yf-fso-rdtest-00.yf01.baidu.com:8082/loan/apply/quick?page=myaccount&channel=shoubaicashloan",LARK_SYSTEM:"http://gzns-nbg-fpu-aug-c02xi2-49.gzns.baidu.com:8081",FRONT_SYSTEM:"http://cp01-front.epc.baidu.com:8082",HILOAN_INDEX:"https://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8877/hiloan/index",CLOAN_INDEX:"https://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8077/cloan/index",switchMC:!1,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}},qa:{SPNO:**********,WALLET_AUTH:"https://baifubao.baidu.com/jump?domain=https://www.baifubao.com/&uri=",LOAN_COUPON:"http://smoke2.fbu_node.otp.baidu.com/fms/tpl/coupon/couponChoose?type=loan&",REPAY_COUPON:"http://smoke2.fbu_node.otp.baidu.com/fms/tpl/coupon/couponChoose?type=repay&",FINANCE_SHOP:"http://cp01-yinping.epc.baidu.com:8000/loan/apply/quick?page=myaccount&channel=shoubaicashloan",WALLET_COUPON_LIST:"https://m.baifubao.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html?type=h5qianbao&",LARK_SYSTEM:"http://smoke.fpd_lark.otp.baidu.com",FRONT_SYSTEM:"http://testqa.fbu_node.otp.baidu.com",HILOAN_INDEX:"https://cp01-ra09-jueheng2qa063.cp01.baidu.com:8604/hiloan/index",CLOAN_INDEX:"https://cp01-ra09-jueheng2qa063.cp01.baidu.com:8606/cloan/index",switchMC:!0,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}},debug:{LOAN_COUPON:"http://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8979/lark/coupon/choose?type=loan&",REPAY_COUPON:"http://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8979/lark/coupon/choose?type=repay&",FINANCE_SHOP:"http://yf-fso-rdtest-00.yf01.baidu.com:8082/loan/apply/quick?page=myaccount&channel=shoubaicashloan",WALLET_COUPON_LIST:"https://m.baifubao.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html?type=h5qianbao&",FRONT_SYSTEM:"http://cp01-front.epc.baidu.com:8099",HILOAN_INDEX:"https://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8877/hiloan/index",CLOAN_INDEX:"https://gzns-nbg-fpu-aug-c02xi2-87.gzns.baidu.com:8877/cloan/index",switchMC:!1,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}},qy:{WALLET_AUTH:"https://baifubao.baidu.com/jump?domain=https://alltest.baifubao.com/&uri=",LOAN_COUPON:"https://qylark.baidu.com/lark/coupon/choose?type=loan&",REPAY_COUPON:"https://qylark.baidu.com/lark/coupon/choose?type=repay&",SPNO:**********,FINANCE_SHOP:"http://cp01-ra09-jueheng2qa063.cp01.baidu.com:8140/loan/apply/quick?page=myaccount&channel=shoubaicashloan",WALLET_COUPON_LIST:"https://alltest.baifubao.com/content/mywallet/h5/sdk_page/sdk_quan_manager.html?type=h5qianbao&",LARK_SYSTEM:"https://qylark.baidu.com",FRONT_SYSTEM:"https://qyfront.baidu.com",HILOAN_INDEX:"https://qyicash.baidu.com/hiloan/index",switchMC:!0,PNO:{ZXSQ:"JXJDD0011000ZXSQ201711021123",JKXY:"JXJDD0012000JKXY201712010108",DFJKXY:"JXJDD0012000DFJKXY201712010108",YRDDQXY:"JXJDD0017000YRDDQXY201711011123",YRDJGXY:"JXJDD0013000YRDJGXY201711011123",YRDJKXY:"JXJDD0012000YRDJKXY201801020124",FOTICJGXY:"JXJDD001FOTICJGXY201801030124",FOTICJKXY:"JXJDD0012000FOTICJKXY201801060207",BXJKXY:"JXJDD0012000BXJKXY201802030207"}}};
!D in n&&(D=X);var p=n[D];if(D!==X){var c=n[X];for(var Y in c)c.hasOwnProperty(Y)&&void 0===p[Y]&&(p[Y]=c[Y])}o.default=p,t.exports=o["default"]});
;define("hiloan:app/static/config/front-system-path",function(t,e,p){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={pwdCheck:"/fms/tpl/identification/paymentpassword",livingCheck:"/fms/tpl/identification/livenessverify",smsCheck:"/fms/tpl/identification/smspassword",contact:"/fms/tpl/activate/contactinfo",helplist:"/fms/tpl/help/helplist",helpcontent:"/fms/tpl/help/helpcontent",applyAdd:"/fms/tpl/infoAggregation?stage=applySupply",autoRepay:"/lark/autorepay/protocol",approveInfo:"/fms/tpl/infoAggregation?stage=activateVerification",supplyInfo:"/fms/tpl/infoAggregation?stage=supplySubmit",approveResult:"/fms/tpl/result",protocolDetail:"/fms/tpl/protocol/detail",finAccount:"/fms/tpl/finAccount/account"},p.exports=e["default"]
});
;define("hiloan:app/static/config/service-type",function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={auth:1057,approve1:1058,approve2:1059},a.exports=t["default"]});
;define("hiloan:app/component/wallet/buildAuthPath",function(e,t,n){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:app/static/config/env-conf"),o=u(a);
t.default={buildAuthPath:function(e,t,n,u,a){var l=o.default.WALLET_AUTH+encodeURIComponent("auth/0/wap_auth?service_type="+e+"&ru="+t+"&sp_no="+o.default.SPNO+"&bu="+encodeURIComponent(n));return l+=u?"&stepbar="+(u||""):"&show_step_bar=0",a&&(l+="&title="+a),l
}},n.exports=t["default"]});
;define("hiloan:app/component/wallet/auth",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var u=e("hiloan:components/fin-fg/util/index"),i=e("hiloan:app/component/wallet/buildAuthPath"),a=o(i);
t.default={auth:function(e,t,n,o,i){if(!t)throw new Error("缺少参数: 授信成功后回跳url");n=n||location.href,-1===n.indexOf("https://")&&(n=u.query.withMergeQuery(n));var l=encodeURIComponent(t);setTimeout(function(){location.href=a.default.buildAuthPath(e,l,n,o,i)
},300)}},n.exports=t["default"]});
;define("hiloan:app/component/wallet/checkIdCard",function(e,a,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(a,"__esModule",{value:!0});var o=e("hiloan:app/component/wallet/auth"),l=n(o),c=e("hiloan:app/static/config/service-type"),p=n(c);
a.default={checkIdcard:function(e,a,t,n,o){var c=0===parseInt(a,10)?p.default.approve1:p.default.approve2;l.default(c,e,o||location.href,t,n||"上传照片")}},t.exports=a["default"]});
;define("hiloan:app/component/front-sys/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}function i(e,n,t){t=t||location.href;var o=u.default.FRONT_SYSTEM,i=c.default.extend({},l.defaultArgs,n);
return o+p.default[e]+"?"+d.default.stringify(i)+"&returnUrl="+encodeURIComponent(t)}Object.defineProperty(n,"__esModule",{value:!0});var a=e("hiloan:node_modules/underscore/underscore"),c=o(a),l=e("hiloan:app/static/config/constant"),r=e("hiloan:node_modules/query-string/index"),d=o(r),f=e("hiloan:app/static/config/env-conf"),u=o(f),s=e("hiloan:app/static/config/front-system-path"),p=o(s),h=e("hiloan:app/static/config/service-type"),v=o(h),g=e("hiloan:app/component/wallet/checkIdCard"),m=o(g);
n.default={contact:function(e){location.href=i("contact",{title:"完善信息"},e)},idCard:function(e,n){var t=0===parseInt(e,10)?0:1;m.default(n||location.href,t,null)},pwdCheck:function(e){location.href=i("pwdCheck",{title:"校验密码"},e)
},livingCheck:function(e,n){var t=0===parseInt(e,10)?0:1,o=0===t?v.default.auth:v.default.approve2,a={productId:"jxjdd",serviceType:o};location.href=i("livingCheck",{title:"人脸识别",from_living_checked:1,liveness:encodeURIComponent(JSON.stringify(a))},n)
},smsCheck:function(e){location.href=i("smsCheck",{title:"短信验证"},e)}},t.exports=n["default"]});
;define("hiloan:app/component/riskControl/riskControl",function(e,o,t){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}function n(e,o,t){return o in e?Object.defineProperty(e,o,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[o]=t,e
}Object.defineProperty(o,"__esModule",{value:!0});var s,r=e("hiloan:node_modules/underscore/underscore"),i=a(r),l=e("hiloan:node_modules/es6-promise/dist/es6-promise"),c=e("hiloan:node_modules/query-string/index"),d=a(c),u=e("hiloan:components/fin-fg/util/index"),f=e("hiloan:app/static/config/constant"),p=e("hiloan:app/component/front-sys/index"),h=a(p);
o.default=(s={backUrl:"/hiloan/trans/tplTransit",analyses:function(e,o){var t=this;return new l.Promise(function(a,n){var s=t.getLinkFlow();if(!e)if(-1!==s.indexOf("Y"))e=s;else{e=e||"";var r=i.default.extend({data_loan_riskcontrol_code:e},d.default.parse(location.search));
history.replaceState(null,document.title,"?"+d.default.stringify(r))}o?t.stashParams(o):o=t.getParams();var l=parseInt(o.data_approve_status,10),c=[];if(-1!==e.indexOf("Y105")&&(c.push("Y105"),e=e.replace(/Y105/g,"")),-1!==e.indexOf("Y106")&&(c.push("Y106"),e=e.replace(/Y106/g,"")),-1!==e.indexOf("Y107")&&(c.push("Y107"),e=e.replace(/Y107/g,"")),c.length>0){{({addinfocode:c.join("|"),jumpbackurl:encodeURIComponent(t.linkFlow(e))})
}t.stashFlow(e),n(),(-1!==c.indexOf("Y107")||-1!==c.indexOf("Y106"))&&h.default.contact(location.protocol+"//"+location.host+t.linkFlow(e))}else if(-1!==e.indexOf("Y108")){if(e=e.replace(/Y108/g,""),t.stashFlow(e),n(),1===parseInt(o.data_idcard_verify,10))return t.processFlowOn(e);
h.default.idCard(0===l?0:1,location.protocol+"//"+location.host+t.linkFlow(e))}else if(-1!==e.indexOf("Y112")){if(t.stashFlow(e),e=e.replace(/Y112/g,""),1===parseInt(o.data_living_verify,10))return t.processFlowOn(e);
var u=t.linkFlow(e);n(),h.default.livingCheck(l,encodeURIComponent(location.protocol+"//"+location.host+u))}else if(-1!==e.indexOf("Y113")){if(e=e.replace(/Y113/g,""),t.stashFlow(e),n(),1===parseInt(o.data_sms_verify,10))return t.processFlowOn(e);
var f=encodeURIComponent(location.protocol+"//"+location.host+t.linkFlow(e));h.default.smsCheck(f)}else if(-1!==e.indexOf("Y109")){e=e.replace(/Y109/g,""),t.stashFlow(e),n();var p={transactionId:d.default.parse(location.search).transactionId},_=location.protocol+"//"+location.host+t.backUrl+"?"+d.default.stringify(p);
h.default.pwdCheck(encodeURIComponent(_))}else a(o)})},getParams:function(){return JSON.parse(window.sessionStorage.getItem("data_loan_backurl_paras")||"{}")},addParams:function(e){return this.stashParams(i.default.extend(this.getParams(),e)),this
},processFlowOn:function(e){return this.analyses(e)},jumpToAnalyses:function(e,o){o&&this.stashParams(o),u.redirect(this.backUrl,{data_loan_riskcontrol_code:encodeURIComponent(e)})},linkFlow:function(e){var o=i.default.extend({},f.defaultArgs,{data_loan_riskcontrol_code:encodeURIComponent(e)}),t=d.default.parse(location.search).data_approve_status;
return t&&(o.data_approve_status=t),this.backUrl+"?"+d.default.stringify(o)},getLinkFlow:function(){var e=d.default.parse(location.search);return decodeURIComponent(decodeURIComponent(e.data_loan_riskcontrol_code||""))
}},n(s,"getParams",function(){return JSON.parse(window.sessionStorage.getItem("data_loan_backurl_paras")||"{}")}),n(s,"stashFlow",function(e){return window.sessionStorage.setItem("data_loan_riskcontrol_code",e),this
}),n(s,"stashParams",function(e){return window.sessionStorage.setItem("data_loan_backurl_paras",JSON.stringify(e)),this}),s),t.exports=o["default"]});
;define("hiloan:app/component/approve/complete",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:node_modules/underscore/underscore"),o=i(a),u=e("hiloan:app/static/config/api"),s=i(u),r=e("hiloan:components/fin-fg/util/index"),c=e("hiloan:node_modules/query-string/index"),l=i(c),d=e("hiloan:components/fin-fg/util/native/getNativeFields"),f=i(d),p=(e("hiloan:app/static/config/constant"),e("hiloan:app/static/config/env-conf")),v=i(p),m=e("hiloan:app/static/config/front-system-path"),h=i(m),g=e("hiloan:app/component/riskControl/riskControl"),y=i(g);
t.default={stageData:{},timeout:!1,qureyParams:l.default.parse(location.search),nativeFields:function(){return f.default({sourceFlag:"",explorerUserAgent:"",deviceSource:"",walletUserAgent:"",cuid:"",location:"",imei:"",imsi:"",isBreak:"",simSerialNum:"",localIp:"",clientIP:"",safeSdk:"",wifi:"",BAIDUCUID:""})
},firstLoan:function(e){var t=this;return this.stageData=e,r.native.get("position").then(function(n){var i={};return n&&n.data&&(i={longitude:n.data.longitude,latitude:n.data.latitude}),t.nativeFields().then(function(t){var n=o.default.extend({},t,e,i);
return s.default.firstLoan(n,{"x-message":!1,"x-silent":!0})}).then(function(){t.countTime(30,!1),t.queryLoan()}).catch(function(e){r.eventBus.$emit("close-tip-dlg"),70013===parseInt(e.errno,10)?r.eventBus.$emit("back-index-dlg"):r.eventBus.$emit("toast-msg",e.errmsg)
})}).catch(function(){})},secondLoan:function(e){var t=this;e.verifytype&&(-1!==e.verifytype.indexOf("Y109")&&!e.verify_token||-1!==e.verifytype.indexOf("Y111")&&!e.voicetoken)&&r.redirect("/hiloan/error"),setTimeout(function(){t.nativeFields().then(function(n){var i=o.default.extend({},n,{verifytoken:e.verify_token});
s.default.secondLoan(i,{"x-silent":!0}).then(function(){t.countTime(30,!0),t.queryLoan()}).catch(function(){r.redirect("/hiloan/index")})})},500)},queryLoan:function(){var e=this;return this.timeout?void this.jumpToResult():void s.default.queryLoan({},{"x-silent":!0}).then(function(t){switch(parseInt(t.data.orderStatus,10)){case 0:setTimeout(function(){e.queryLoan()
},1500);break;case 1:if(t.data.apsRiskCode){var n=o.default.extend({},e.stageData,{data_approve_status:2,verifytype:t.data.apsRiskCode});y.default.jumpToAnalyses(t.data.apsRiskCode,n)}else e.jumpToResult();
break;case 2:default:e.jumpToResult()}}).catch(function(){e.jumpToResult()})},jumpToResult:function(){var e=v.default.FRONT_SYSTEM+h.default.approveResult;r.redirect(e)},countTime:function(e,t){var n=this;
this.timeout=!1,t&&r.eventBus.$emit("show-count-time");var i=setInterval(function(){e--,0===e&&(n.timeout=!0,clearInterval(i))},1e3)}},n.exports=t["default"]});
;define("hiloan:app/component/arrow-select/arrow-select.vue",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{label:{type:String,"default":""},content:{type:String,"default":""},clickFunc:{type:Function},border:{type:String,"default":""},loading:{type:Boolean,"default":!1}},data:function(){return{is:"arrow-select"}
},computed:{borderStyle:function(){switch(this.border){case"offsetTop":return"offset border-top";case"offsetBottom":return"offset border-bottom";default:return""}}},methods:{selectClick:function(){"function"==typeof this.clickFunc?this.clickFunc():console.error('属性"click-func"必须是一个函数')
}},components:{}};var o='<section class="fin-{{ is }}">\n    <div class="select clearfix {{borderStyle}}" @click="selectClick">\n        <span class="label">\n            {{label}}\n        </span>\n        <p v-if="!loading" class="con" v-html="content"></p>\n        <p v-if="loading" class="con"><i class="loading"></i></p>\n        <i class="icon icon-arrow-right"></i>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=o),t&&t.default&&(t.default.template=o),n.exports=t["default"]});
;define("hiloan:app/component/arrow-select/index",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:app/component/arrow-select/arrow-select.vue"),r=n(a);
t.default=r.default,o.exports=t["default"]});
;define("hiloan:app/static/config/union-conf",function(e,a,i){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default={"0008":{key:"yrd",alias:"宜人恒业"},"0014":{key:"fotic",alias:"外贸信托"},"0017":{key:"bx",alias:"百信银行"}},i.exports=a["default"]
});
;define("hiloan:app/component/bottom-banner/bottom-banner.vue",function(n,t,e){"use strict";function o(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(t,"__esModule",{value:!0});var i=(n("hiloan:components/fin-fg/util/index"),n("hiloan:app/static/config/union-conf")),s=o(i);
t.default={props:{type:{type:String,"default":"no"},copyrightText:{type:String,"default":"本服务由百度金融旗下百度有钱花提供"},unionloanMode:{type:String,"default":""},unionloanCode:{type:String,"default":""},skin:{type:String,"default":""}},data:function(){return{is:"bottom-banner"}
},computed:{unionCls:function(){return s.default[this.unionloanCode]?"fotic"===s.default[this.unionloanCode].key?"yrd":s.default[this.unionloanCode].key:""},entrustCls:function(){return"F"===this.unionloanMode&&s.default[this.unionloanCode]&&"yrd"!==s.default[this.unionloanCode].key?s.default[this.unionloanCode].key:""
}},methods:{},components:{}};var a='<section class="fin-{{ is }}">\n    <div class="copyright" v-if="type === \'no\'" :class="skin">\n        <span>本服务由百度金融旗下百度有钱花提供</span>\n    </div>\n    <div class="copyright-word" v-if="type === \'word\'" :class="skin">\n        <span v-text="copyrightText"></span>\n    </div>\n    <div v-if="type === \'logo\'" class="bottom-border" :class="skin">\n        <div class="bottom-copyright">\n            <span v-text="copyrightText"></span>\n        </div>\n        <div class="banner-wrapper">\n            <i class="money-icon"></i>\n            <i class="bank-icon" :class="unionCls"></i>\n            <i class="bank-icon" v-if="entrustCls" :class="entrustCls"></i>\n        </div>\n    </div>\n</section>';
e&&e.exports&&(e.exports.template=a),t&&t.default&&(t.default.template=a),e.exports=t["default"]});
;define("hiloan:app/component/bottom-banner/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var a=e("hiloan:app/component/bottom-banner/bottom-banner.vue"),u=o(a);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:app/component/c-hello.vue",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});{var l=e("hiloan:node_modules/vue/dist/vue.common");
n(l)}t.default={data:function(){return{message:"chellow Hello world!"}},methods:{notifyMessage:function(){}}};var a='<div class="c-index" :click="notifyMessage">\n    {{message}}\n</div>';o&&o.exports&&(o.exports.template=a),t&&t.default&&(t.default.template=a),o.exports=t["default"]
});
;define("hiloan:app/component/gps-dialog/gps-dialog.vue",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:components/fin-ui/ui-dialog/index"),s=o(i);
n.default={props:{show:{type:Boolean,"default":!1}},data:function(){return{is:"gps-dialog"}},methods:{okHandle:function(){}},components:{uiDialog:s.default}};var a='<section class="fin-{{ is }}">\n    <ui-dialog prefix="gps-dlg" type="alert" title="" :show.sync="show" @ok="okHandle">\n        <div slot="content">\n            <div class="gps-bg"></div>\n            <div class="gps-text">\n                <h4>获取定位信息失败</h4>\n                <p>请检查是否开启</p>\n            </div>\n        <div>\n    \n</div></div></ui-dialog></section>';
t&&t.exports&&(t.exports.template=a),n&&n.default&&(n.default.template=a),t.exports=n["default"]});
;define("hiloan:app/component/gps-dialog/index",function(e,o,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var a=e("hiloan:app/component/gps-dialog/gps-dialog.vue"),d=t(a);
o.default=d.default,n.exports=o["default"]});
;define("hiloan:app/component/hiloan-help-service/hiloan-help-service.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var r=e("hiloan:components/fin-cs/help-service/index"),l=i(r),a=e("hiloan:app/static/config/constant"),o=e("hiloan:app/static/config/env-conf"),p=i(o),s=e("hiloan:app/static/config/front-system-path"),u=i(s);
t.default={props:{textArr:{type:Array,required:!1,"default":function(){return[]}}},data:function(){return{is:"hiloan-help-service",jumpUrl:""}},methods:{getJumpUrl:function(){this.jumpUrl=p.default.FRONT_SYSTEM+u.default.helplist+"?"+a.defaultQuery
}},created:function(){this.getJumpUrl()},components:{helpService:l.default}};var c='<section class="fin-{{ is }}">\n    <help-service :text-arr="textArr" :jump-url="jumpUrl"></help-service>\n</section>';
n&&n.exports&&(n.exports.template=c),t&&t.default&&(t.default.template=c),n.exports=t["default"]});
;define("hiloan:app/component/hiloan-help-service/index",function(e,n,l){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:app/component/hiloan-help-service/hiloan-help-service.vue"),t=o(i);
n.default=t.default,l.exports=n["default"]});
;define("hiloan:app/component/img-slider/img-slider.vue",function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});{var s=t("hiloan:node_modules/vue-touch/vue-touch");
n(s)}e.default={data:function(){return{is:"img-slider",current:0,count:this.items.length,autoPlayFlag:null}},props:{items:{type:Array,required:!0},dots:{type:Boolean,required:!1,"default":!0},autoplay:{type:Boolean,required:!1,"default":!0},delay:{type:Number,required:!1,"default":2},speed:{type:Number,required:!1,"default":1.5},pause:{type:Boolean,required:!1,"default":!0}},methods:{turn:function(t){var e=this.current+t;
0>e&&(e+=this.count),e>=this.count&&(e-=this.count),this.current=e},goPlay:function(){var t=this;this.autoplay&&(this.autoPlayFlag=setInterval(function(){t.turn(1)},1e3*this.delay))},pausePlay:function(){clearInterval(this.autoPlayFlag)
},onSwipeLeft:function(){this.pausePlay(),this.turn(1),this.goPlay()},onSwipeRight:function(){this.pausePlay(),this.turn(-1),this.goPlay()}},ready:function(){this.goPlay()},components:{}};var a='<section class="fin-{{ is }}">\n\n<div class="slider" v-touch:swipeleft="onSwipeLeft" v-touch:swiperight="onSwipeRight">\n    <ul v-bind:style="{ width: count * 100 + \'%\', left: -100 * current + \'%\', transitionDuration: speed + \'s\'}">\n        <li v-for="item in items" class="slider-item" v-bind:style="{width: 100 / count + \'%\'}">\n            <a href="{{item.link}}"><img v-bind:src="item.src" :alt="item.name"></a>\n        </li>\n    </ul>\n</div>\n<div class="slider-dots-wrap" v-if="items &amp;&amp; items.length > 1">\n    <span class="slider-dot" v-for="i in count" :key="\'dot\' + i" :class="{ \'slider-dot-selected\': current === i }"></span>\n</div>\n</section>';
i&&i.exports&&(i.exports.template=a),e&&e.default&&(e.default.template=a),i.exports=e["default"]});
;define("hiloan:app/component/img-slider/index",function(e,i,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var d=e("hiloan:app/component/img-slider/img-slider.vue"),l=t(d);
i.default=l.default,n.exports=i["default"]});
;define("hiloan:app/component/input-money/input-money.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=e("hiloan:components/fin-fg/util/index"),r=e("hiloan:components/fin-rm/rm-input/index"),u=i(r);
t.default={props:{money:{type:String,"default":""},skin:{type:String,"default":""},title:{type:String,"default":""},placeholder:{type:String,"default":""},tip:{type:String,"default":""},rules:{type:Object,"default":function(){}},topError:{type:String,"default":"true"}},data:function(){return{is:"input-money",readonly:!1}
},methods:{freeze:function(e){var t=this;this.readonly=!0,setTimeout(function(){t.readonly=!1},e)}},components:{rmInput:u.default},watch:{money:function(e){o.eventBus.$emit("on-input-money",e)}},created:function(){var e=this;
o.eventBus.$on("freeze-input",function(t){e.freeze(t||500)})}};var l='<section class="fin-{{ is }} {{skin}}">\n    <div class="title">{{title}}</div>\n    <rm-input fieldname="inputValue" fieldset="inputValue" input-type="tel" :value.sync="money" :record-input="false" :placeholder="placeholder" title="¥" :tip="tip" :tip-border="true" :rules="rules" :maxlength="9" :top-error="topError" :readonly="readonly">\n    </rm-input>\n</section>';
n&&n.exports&&(n.exports.template=l),t&&t.default&&(t.default.template=l),n.exports=t["default"]});
;define("hiloan:app/component/input-money/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var u=e("hiloan:app/component/input-money/input-money.vue"),i=o(u);
n.default=i.default,t.exports=n["default"]});
;define("hiloan:app/component/invoke-wallet-jxj/index",function(e,a,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}function o(){var e=r.default.parse(location.search),a={},n=["fr"];
return n.forEach(function(n){var t=e[n];t&&(a[n]=t)}),a}function i(e){e=e||"HILOAN_INDEX";var a=(navigator.userAgent,o()),n=r.default.stringify(a);if(/ua/i.test("baiduwallet"))location.href=f.default[e]+"?"+n;
else{var t=f.default[e]+"?"+n,i="";switch(e){case"CLOAN_INDEX":i="https://app.baifubao.com/index.html?channel=1019132i&from=xianjindai&bdwallet_type=1&bdwallet_url_ios="+encodeURIComponent(t);break;case"HILOAN_INDEX":i="https://app.baifubao.com/index.html?channel=1019132i&from=hiloan&bdwallet_type=1&bdwallet_url_ios="+encodeURIComponent(t)
}location.href=i}}Object.defineProperty(a,"__esModule",{value:!0}),a.default=i;var l=e("hiloan:node_modules/query-string/index"),r=t(l),u=e("hiloan:app/static/config/env-conf"),f=t(u);n.exports=a["default"]
});
;define("hiloan:app/component/ui-popup/ui-popup.vue",function(e,t,n){"use strict";function s(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:node_modules/vue/dist/vue.common"),o=s(i),l=e("hiloan:node_modules/vue-touch/vue-touch"),c=s(l),u=e("hiloan:components/fin-ui/ui-mask/index"),a=s(u);
o.default.use(c.default),t.default={props:{show:{type:Boolean,"default":!1},subtitle:{type:String,"default":""},skin:{type:String,"default":""},needBtn:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!0},scrollTag:{type:Boolean,"default":!0},btn:{type:String,"default":"我已阅读并同意本协议"}},data:function(){return{is:"ui-popup",iHander:-1}
},methods:{closeUi:function(){this.show=!1},confirm:function(){this.disabled||(this.closeUi(),this.checked=!0,this.value="1",this.$dispatch("confirm",this))},showUi:function(){this.show=!0},bindScroll:function(){var e=this.$els.content,t=this;
e.onscroll=function(){var n=e.scrollHeight,s=e.scrollTop,i=e.offsetHeight;i+s>=n-10&&(t.disabled=!1)}},preventTouchMove:function(e,t){return e?!1:t.preventDefault()}},watch:{show:function(){var e=this;
this.show===!0?document.body.classList.add(this.is+"-fixed"):document.body.classList.remove(this.is+"-fixed"),clearTimeout(this.iHander),this.iHander=setTimeout(function(){e.disabled=!1},12e3)}},ready:function(){this.bindScroll()
},components:{uiMask:a.default}};var d='<section class="fin-{{ is }} {{ skin }}">\n    <section v-show.sync="show">\n        <ui-mask @click="closeUi" :show.sync="show">\n        </ui-mask>\n        <div class="pop" :class="{\'show-select\':show}" @touchmove="preventTouchMove(false, $event)">\n            <div class="close">\n                <i class="icon icon-close" @click="closeUi">\n                </i>\n            </div>\n            <h2 class="title" v-html="subtitle">\n                <!-- {{ subtitle }} -->\n            </h2>\n            <div class="content" :class="{\'with-btn\':needBtn}" id="content" @touchmove.stop="preventTouchMove(scrollTag, $event)" v-el:content="">\n                 <slot>\n                 </slot>\n            </div>\n            <div class="fixed-button" v-if="needBtn">\n                <input class="fin-ui-button full" type="button" :disabled.sync="disabled" value="{{btn}}" @click="confirm">\n            </div>\n        </div>\n    </section>\n</section>';
n&&n.exports&&(n.exports.template=d),t&&t.default&&(t.default.template=d),n.exports=t["default"]});
;define("hiloan:app/component/ui-popup/index",function(e,u,p){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(u,"__esModule",{value:!0});var n=e("hiloan:app/component/ui-popup/ui-popup.vue"),t=o(n);
u.default=t.default,p.exports=u["default"]});
;define("hiloan:app/component/select-popup/select-popup.vue",function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});{var i=e("hiloan:node_modules/vue/dist/vue.common"),l=o(i),p=e("hiloan:app/component/arrow-select/index"),a=o(p),u=e("hiloan:app/component/ui-popup/index"),r=o(u);
e("hiloan:components/fin-fg/util/index")}t.default={props:{skin:{type:String,"default":"default"},canScroll:{type:Boolean,"default":!0},subtitle:{type:String,"default":""},leftContent:{type:String,"default":"您要借多久"},rightContent:{type:String,"default":"个月"},needArrowSelect:{type:Boolean,"default":!0},triggerArea:{type:String,"default":""},showPopup:{type:Boolean,"default":!1},valid:{type:Boolean,"default":!0},tipText:{type:String,"default":""},beforePopup:{type:Function},value:{type:Number},needBtn:{type:Boolean,"default":!1},btnText:{type:String,"default":"确定"},loading:{type:Boolean,"default":!1}},data:function(){return{is:"select-popup"}
},methods:{arrowClick:function(){var e=this,t=function(){l.default.nextTick(function(){{var t=e.$el.querySelectorAll(".fix-height")[0];t.clientHeight}e.showPopup=!1;var n=function o(){t.clientHeight<603&&document.body.clientHeight>=603?setTimeout(function(){o()
},50):e.showPopup=!0};setTimeout(function(){n()},50)})};if("function"==typeof this.beforePopup){var n=this.beforePopup();"function"==typeof n.then?n.then(function(){t()}):t()}else t()}},components:{arrowSelect:a.default,uiPopup:r.default}};
var s='<section class="fin-{{ is }} select-popup-{{ skin }}">\n    <div v-if="needArrowSelect">\n        <arrow-select :label="leftContent" :content="rightContent" :click-func="arrowClick" :loading="loading" border="offsetBottom">\n        </arrow-select>\n    </div>\n    <div v-else="" class="trigger-area" @click="arrowClick" v-html="triggerArea">\n    </div>\n    <div class="error-tip" v-if="!valid">\n        <slot name="errorTip">\n            <span>{{tipText}}</span>\n        </slot>\n    </div>\n    <ui-popup :skin="skin" :show.sync="showPopup" :subtitle="subtitle" :scroll-tag="canScroll" :need-btn="needBtn" :disabled="false" :btn="btnText">\n        <slot name="popup">\n        </slot>\n    </ui-popup>\n    <div class="fix-height" style="position: fixed;bottom:0;top:0"></div>\n</section>';
n&&n.exports&&(n.exports.template=s),t&&t.default&&(t.default.template=s),n.exports=t["default"]});
;define("hiloan:app/component/select-popup/index",function(e,p,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(p,"__esModule",{value:!0});var u=e("hiloan:app/component/select-popup/select-popup.vue"),n=o(u);
p.default=n.default,t.exports=p["default"]});
;define("hiloan:app/component/repay-list/repay-list.vue",function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),e("hiloan:components/fin-fg/filter/index");var i=e("hiloan:components/fin-fg/util/index");
t.default={props:{bills:{type:Array,"default":function(){return[]}},showRate:{type:Boolean,"default":!1}},data:function(){return{is:"repay-list"}},computed:{},methods:{moneyDetail:function(e){var t="本金"+i.format.money(e.prinamt,"",2);
return 1===e.period&&(t+=" + 借款服务费"+i.format.money(e.management,"",2)),t+=" + 分期手续费"+i.format.money(e.fee,"",2),this.showRate&&(t+=" + 利息"+i.format.money(e.intamt,"",2)),e.penalty&&(t+=" + 罚息"+i.format.money(e.penalty,"",2)),e.discount&&(t+=" - 优惠券抵扣"+i.format.money(e.discount,"",2)),t
}},components:{}};var o='<section class="fin-{{ is }}">\n    <div class="bar" v-for="bill in bills" :key="bill.period">\n        <div class="row1">\n            <div><h2><strong>{{bill.period}}</strong>期</h2></div>\n            <div><h1>￥{{bill.money | formatMoney}}</h1></div>\n        </div>\n        <div class="row2">\n            <div>{{bill.dueDate | formatDateTime \'yy/mm/dd\'}}</div>\n            <div>{{moneyDetail(bill)}}</div>\n        </div>\n    </div>\n</section>';
n&&n.exports&&(n.exports.template=o),t&&t.default&&(t.default.template=o),n.exports=t["default"]});
;define("hiloan:app/component/repay-list/index",function(e,t,n){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:app/component/repay-list/repay-list.vue"),o=a(l);
t.default=o.default,n.exports=t["default"]});
;define("hiloan:app/component/loan-calcu/loan-calcu.vue",function(e,t,a){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var l=e("hiloan:node_modules/underscore/underscore"),n=i(l),s=e("hiloan:app/static/config/api"),c=i(s),o=e("hiloan:node_modules/query-string/index"),u=i(o),r=e("hiloan:app/static/config/constant"),d=e("hiloan:app/static/config/env-conf"),h=i(d),p=e("hiloan:app/static/config/front-system-path"),f=(i(p),e("hiloan:components/fin-fg/util/index")),v=e("hiloan:app/component/select-popup/index"),m=i(v),g=e("hiloan:app/component/repay-list/index"),y=i(g),R=e("hiloan:app/component/hiloan-help-service/hiloan-help-service.vue"),C=i(R),b=1;
t.default={props:{showCalculat:{type:Boolean,"default":!1},urlParams:{type:Object,"default":function(){return{}}},useCoupon:{type:Number,"default":1},priceMultiplier:{type:String,"default":""},currentCalculateId:{type:Number,"default":0},lender:{type:String,"default":""},billList:{type:Array,"default":[]},showRate:{type:Boolean,"default":!1},calculateRes:{type:Object,"default":function(){return{}
}},couponIds:{type:Array,"default":function(){return[]}}},data:function(){return{is:"loan-calcu",calculating:!1,trxnamount:0,payperiods:0,repayMode:0,noDelay:!0,timerCalculate:null,calculateNum:0,bills:{},dueDayText:"",firstDueText:"",defServiceRate:1,defChargeRate:1.5,defLoanRate:.05}
},computed:{serviceRate:function(){return this.calculateRes&&this.calculateRes.serviceRate?this.calculateRes.serviceRate:""},chargeRate:function(){return this.calculateRes&&this.calculateRes.chargeRate?this.calculateRes.chargeRate:""
},loanRate:function(){return this.calculateRes&&this.calculateRes.rate?this.calculateRes.rate:""},discountamtSum:function(){return this.calculateRes&&this.calculateRes.discountamtSum?this.calculateRes.discountamtSum:0
},couponNum:function(){return this.calculateRes&&this.calculateRes.couponNum?this.calculateRes.couponNum:0},couponText:function(){return this.couponNum>0?0===+this.useCoupon?"不使用券":this.discountamtSum?"-￥"+f.format.money(this.discountamtSum):"无可用券":"无可用券"
},firstDueDate:function(){return this.bills.firstDueDate?new Date(this.bills.firstDueDate):null},firstDueMoney:function(){return this.billList.length>0?this.billList[0].money:0},showDiscoServiceRate:function(){return this.serviceRate&&+this.serviceRate<this.defServiceRate?!0:!1
},showDiscoChargeRate:function(){return this.chargeRate&&+this.chargeRate<this.defChargeRate&&b===+this.repayMode?!0:!1},showDiscoLoanRate:function(){return this.loanRate&&+this.loanRate<this.defLoanRate?!0:!1
},lenderHTML:function(){return this.lender.replace(/、/g,"</br>")}},methods:{clearCalculate:function(){f.eventBus.$emit("calcu-complete",null),this.showCalculat=!1},loanCalculate:function(e){var t=this;
if(this.clearCalculate(),this.trxnamount&&this.payperiods){var a=this.noDelay?0:this.showCalculat?900:550;this.timerCalculate=setTimeout(function(){t.apiCalculate(e)},a)}},apiCalculate:function(e){var t=this;
if(this.calculateNum++,this.calculateNum>1){this.useCoupon=1,this.couponIds=[],this.urlParams.coutPack="";var a=f.query.withQuery(location.href,this.urlParams);history.replaceState({},document.title,a)
}this.showCalculat=!0,this.calculating=!0,c.default.loanCalculate({useCoupon:this.useCoupon,couponIds:JSON.stringify(this.couponIds),repayMode:this.repayMode,payperiods:this.payperiods,trxnamount:this.trxnamount}).then(function(a){return t.currentCalculateId>e?void(t.showCalculat=!1):(t.showCalculat=!0,t.calculating=!1,t.calculateRes=a.data,t.bills=a.data.bills,void t.handleCalculateRes())
}).catch(function(e){f.eventBus.$emit("freeze-input"),t.showCalculat=!1,e.errmsg||alert("服务异常，稍后再试")})},handleCalculateRes:function(){this.calculateRes.couponIds=this.parsCalCouponIds(this.calculateRes.coupons),this.priceMultiplier=this.calculateRes.priceMultiplier,this.dueDayText=this.getDueDayText(),this.firstDueText=this.getFirstDueText(),n.default.extend(this.calculateRes,{dueDayText:this.dueDayText,firstDueText:this.firstDueText}),f.eventBus.$emit("calcu-complete",this.calculateRes)
},selectCoupon:function(){var e=n.default.extend({pno:this.calculateRes.pno,payperiods:this.payperiods,trxnamount:this.trxnamount,couponIds:this.joinCouponIds(this.couponIds),priceMultiplier:this.priceMultiplier},r.defaultArgs),t={cinPack:encodeURIComponent(u.default.stringify(e)),backUrl:encodeURIComponent(location.href)};
location.href=h.default.LOAN_COUPON+u.default.stringify(t)},getDueDayText:function(){if(this.bills.firstDueDate){var e=f.format.datetime(this.bills.firstDueDate,"dd");return"每月"+e+"日"}return""},getFirstDueText:function(){if(this.bills.firstDueDate&&this.bills.list&&this.bills.list.length>0){var e=(new Date(this.bills.firstDueDate),f.format.datetime(this.firstDueDate," (mm月dd日)"));
return"￥"+f.format.money(this.bills.list[0].money)+e}return""},joinCouponIds:function(e){return e instanceof Array&&e.length>0?e.join("_"):""},parsCalCouponIds:function(e){var t=[];return e instanceof Array&&e.forEach(function(e){t.push(e.couponId)
}),t}},components:{selectPopup:m.default,repayList:y.default,hiloanHelpService:C.default},created:function(){var e=this;f.eventBus.$on("loan-calculate",function(t){var a=t.trxnamount,i=t.payperiods,l=t.repayMode,n=t.noDelay,s=t.calculateId;
e.trxnamount=a,e.payperiods=i,e.repayMode=l,e.noDelay=n,e.timerCalculate&&clearTimeout(e.timerCalculate),e.loanCalculate(s)}),f.eventBus.$on("on-method-change",function(t){e.repayMode=t}),f.eventBus.$on("clear-calculate",function(){e.clearCalculate()
})},compiled:function(){}};var D='<section class="fin-{{ is }}">\n    <section v-if="showCalculat" class="calculat-info">\n        <div class="bar">\n            <div class="label">借款服务费(<s class="discount-cls" v-if="showDiscoServiceRate">{{defServiceRate}}%</s>{{serviceRate}}%)</div>\n            <div class="main">\n                <div v-if="!calculating" class="con">￥{{bills.feeTotal | formatMoney}}</div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="bar">\n            <div class="label">分期手续费(<s class="discount-cls" v-if="showDiscoChargeRate">{{defChargeRate}}%</s>{{chargeRate}}%)</div>\n            <div class="main">\n                <div v-if="!calculating" class="con">￥{{(billList[0] ? billList[0].fee : 0) | formatMoney}} x {{payperiods}}</div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="bar" v-if="showRate">\n            <div class="label">总利息(<s class="discount-cls" v-if="showDiscoLoanRate">{{defLoanRate}}%</s>{{loanRate}}%)</div>\n            <div class="main">\n                <div v-if="!calculating" class="con">￥{{bills.intamtTotal | formatMoney}}</div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="bar highlight" @click="selectCoupon" v-log="\'coupon\'" v-if="couponText != \'\'">\n            <div class="label">优惠券</div>\n            <div class="main">\n                <div v-if="!calculating" class="con">{{couponText}}<i class="icon icon-arrow-right"></i></div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="row-line"></div>\n        <div class="bar" v-if="lender !== \'\'">\n            <div class="label">出借方</div>\n            <div class="main">\n                <div v-if="!calculating" class="con" v-html="lenderHTML"></div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="bar">\n            <div class="label">首次还款</div>\n            <div class="main">\n                <div v-if="!calculating" class="con">{{firstDueText}}</div>\n                <div v-if="calculating" class="cal-loading loading"></div>\n            </div>\n        </div>\n        <div class="bar">\n            <div class="label">还款时间</div>\n            <div class="main">\n                <select-popup :need-arrow-select="false" trigger-area="查看还款计划" subtitle="还款计划" :can-scroll="true" class="link" skin="bills-pop" v-log="\'repayPlan\'">\n\n                    <div slot="popup">\n                        <repay-list :bills="billList" :show-rate="showRate"></repay-list>\n                    </div>\n                </select-popup>\n            </div>\n        </div>\n    </section>\n\n    <div v-if="showCalculat" class="help-in-calcu">\n        <hiloan-help-service></hiloan-help-service>\n    </div>\n\n</section>';
a&&a.exports&&(a.exports.template=D),t&&t.default&&(t.default.template=D),a.exports=t["default"]});
;define("hiloan:app/component/loan-calcu/index",function(e,n,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:app/component/loan-calcu/loan-calcu.vue"),u=l(o);
n.default=u.default,a.exports=n["default"]});
;define("hiloan:app/component/menulist/menulist.vue",function(e,i,t){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=e("hiloan:components/fin-fg/util/index");i.default={props:{skin:{type:String,"default":""},keyVals:{type:Array}},data:function(){return{is:"menulist"}
},methods:{itemClick:function(e){e.arrow&&n.eventBus.$emit("menu-list-click",e.id)}},components:{},created:function(){}};var l='<section class="fin-{{ is }} {{skin}}">\n    <ul>\n        <li v-for="item in keyVals" :key="item.id" @click="itemClick(item)">\n            <div v-if="!item.hide" class="{{item.class}}">\n                <div class="label">{{item.label}}</div>\n                <div class="content">{{item.content}}<i v-if="item.arrow" class="icon icon-arrow-right"></i></div>\n            </div>\n        </li>\n    </ul>\n</section>';
t&&t.exports&&(t.exports.template=l),i&&i.default&&(i.default.template=l),t.exports=i["default"]});
;define("hiloan:app/component/menulist/index",function(e,n,t){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var l=e("hiloan:app/component/menulist/menulist.vue"),o=u(l);
n.default=o.default,t.exports=n["default"]});
;define("hiloan:app/component/no-invite/no-invite.vue",function(n,e,i){"use strict";function o(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(e,"__esModule",{value:!0});var t=n("hiloan:app/static/config/env-conf"),s=o(t);
e.default={props:{},data:function(){return{is:"no-invite"}},methods:{finshop:function(){location.href=s.default.FINANCE_SHOP}},components:{}};var a='<section class="fin-{{ is }}">\n    <div class="logo"></div>\n    <h1>您暂未收到邀请，敬请期待</h1>\n    <!--\n    <p class="sub-tip">您可以前往百度金融商城，查看其他贷款服务</p>\n    <a class="finshop" @click="finshop">查看其他贷款服务<i class="icon icon-arrow-right"></i></a>\n    -->\n\n    <section class="copyright">\n        <div class="logo"></div>\n        <p>本服务由百度有钱花及合作机构提供</p>\n    </section>\n</section>';
i&&i.exports&&(i.exports.template=a),e&&e.default&&(e.default.template=a),i.exports=e["default"]});
;define("hiloan:app/component/no-invite/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var i=e("hiloan:app/component/no-invite/no-invite.vue"),u=o(i);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:app/component/pc-repay-dlg/pc-repay-dlg.vue",function(t,n,e){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(n,"__esModule",{value:!0});var a=t("hiloan:node_modules/underscore/underscore"),i=o(a),l=t("hiloan:node_modules/query-string/index"),c=o(l),u=t("hiloan:components/fin-fg/util/index"),r=t("hiloan:app/static/config/constant"),s=t("hiloan:app/static/config/env-conf"),p=(o(s),t("hiloan:app/static/config/front-system-path")),d=(o(p),t("hiloan:components/fin-ui/ui-dialog/index")),f=o(d),h="https://www.baifubao.com/wallet-fe/0/large_payment/0",y="/hiloan/index",m="/hiloan/trans/tplBankQuota";
n.default={props:{show:{type:Boolean,"default":!1},repayApiData:{type:Object,"default":function(){return{}}},repayAmount:{type:Number,"default":0},repayType:{type:String,"default":""}},data:function(){return{is:"pc-repay-dlg"}
},computed:{returnUrl:function(){var t=i.default.extend({},r.defaultArgs,{status:"success",tid:this.repayApiData.newTid,amount:this.repayAmount,repayType:this.repayType});return location.protocol+"//"+location.host+"/hiloan/trans/tplRepayResult?"+c.default.stringify(t)
},indexUrl:function(){return location.protocol+"//"+location.host+y+"?"+r.defaultQuery},quataUrl:function(){return location.protocol+"//"+location.host+m+"?"+r.defaultQuery}},methods:{close:function(){u.eventBus.$emit("pc-dialog-close")
},submitPC:function(){location.href=h+"?page_title=大额支付&has_steps=1&cashdesk_params="+encodeURIComponent(this.repayApiData.orderInfo)+"&return_url="+encodeURIComponent(this.returnUrl)+"&page_url="+encodeURIComponent(this.indexUrl)+"&quata_url="+encodeURIComponent(this.quataUrl)
},changeCard:function(){u.eventBus.$emit("change-card-for-repay")},toBankQuota:function(){u.redirect(m)}},components:{uiDialog:f.default}};var g='<section class="fin-{{ is }}">\n    <ui-dialog type="lightbox" :show.sync="show">\n        <div slot="content">\n            <i class="icon icon-close" @click="close"></i>\n            <div class="repay-icon"></div>\n            <h1>还款金额超过银行卡支付限额</h1>\n            <p>请更换还款银行卡或前往百度钱包</p>\n            <p>电脑版，完成支付</p>\n            <div class="bankquota-link" @click="toBankQuota"><i class="icon icon-info"></i>查看银行卡限额</div>\n            <div class="fin-ui-button full" @click="submitPC">电脑网银支付</div>\n            <div class="fin-ui-button full primary" @click="changeCard">更换银行卡</div>\n        </div>\n    </ui-dialog>\n</section>';
e&&e.exports&&(e.exports.template=g),n&&n.default&&(n.default.template=g),e.exports=n["default"]});
;define("hiloan:app/component/pc-repay-dlg/index",function(e,n,p){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var a=e("hiloan:app/component/pc-repay-dlg/pc-repay-dlg.vue"),d=t(a);
n.default=d.default,p.exports=n["default"]});
;define("hiloan:app/static/config/repay-modes",function(i,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={22:{id:22,name:"等额本息",hint:"除首尾两期外，每期还款金额相同",hintMulit:"按日计息，分期还款",periods:[]},25:{id:25,name:"等额本息",hint:"除首尾两期外，每期还款金额相同",hintMulit:"按日计息，分期还款",periods:[]},1:{id:1,name:"等本等费",hint:"借款免息，按期收费",hintMulit:"借款免息，按期收费",periods:[]}},t.exports=e["default"]
});
;define("hiloan:app/component/protocols/protocols.vue",function(t,o,e){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(o,"__esModule",{value:!0});var n=t("hiloan:app/static/config/env-conf"),l=i(n),r=t("hiloan:app/static/config/front-system-path"),u=i(r),p=t("hiloan:app/static/config/union-conf"),c=i(p),d=t("hiloan:node_modules/query-string/index"),a=i(d),s=t("hiloan:components/fin-fg/util/index"),f=t("hiloan:app/static/config/repay-modes"),y=i(f),h=t("hiloan:app/component/app-util/index"),D=i(h);
o.default={props:{mode:{type:Number,"default":1},unionInfo:{type:Object,"default":function(){return null}},autoPay:{type:Number,"default":0},itemClick:{type:Function},repayMode:{type:Number,"default":0}},data:function(){return{is:"protocols"}
},computed:{isSelfLoan:function(){return this.unionInfo&&this.unionInfo.unionloanMode&&"S"!==this.unionInfo.unionloanMode.toUpperCase()?!1:!0},unionKey:function(){var t=D.default.getUnionInstitution(this.unionInfo);
return c.default[t.unionloanInstitutionCode]?c.default[t.unionloanInstitutionCode].key||"":""},dfModeId:function(){var t=void 0;return Object.keys(y.default).forEach(function(o){"等本等费"===y.default[o].name&&(t=y.default[o].id)
}),t},creditProtocol:function(){return{pType:"ZXSQ",title:"《个人征信授权书》",url:this.getProtocolUrl({pType:"ZXSQ",pNo:l.default.PNO.ZXSQ,pCode:"1000",productId:"JXJDD001"})}},autoPayProtocol:function(){return{pType:"",title:"《百度有钱花用户还款代扣协议》",url:l.default.LARK_SYSTEM+u.default.autoRepay}
},bdLoanProtocols:function(){return[{pType:"JKXY",title:"《百度有钱花·大额尊享服务借款协议》",url:this.getProtocolUrl({pType:"JKXY",pNo:l.default.PNO.JKXY,pCode:"2000",productId:"JXJDD001"})}]},dfLoanProtocols:function(){return[{pType:"DFJKXY",title:"《百度有钱花·大额尊享服务借款协议》",url:this.getProtocolUrl({pType:"DFJKXY",pNo:l.default.PNO.DFJKXY,pCode:"2000",productId:"JXJDD001"})}]
},yiRenDaiProtocols:function(){return[{pType:"YRDJKXY",title:"《借款协议》（宜人贷）",url:this.getProtocolUrl({pType:"YRDJKXY",pNo:l.default.PNO.YRDJKXY,pCode:"2000",productId:"JXJDD001"})},{pType:"YRDJGXY",title:"《借款咨询服务协议》（宜人贷）",url:this.getProtocolUrl({pType:"YRDJGXY",pNo:l.default.PNO.YRDJGXY,pCode:"3000",productId:"JXJDD001"})},{pType:"YRDDQXY",title:"《电子签名数字证书用户使用须知》(宜人贷)",url:this.getProtocolUrl({pType:"YRDDQXY",pNo:l.default.PNO.YRDDQXY,pCode:"7000",productId:"JXJDD001"})}]
},foticProtocols:function(){return[{pType:"FOTICJKXY",title:"《外贸信托借款合同》",url:this.getProtocolUrl({pType:"FOTICJKXY",pNo:l.default.PNO.FOTICJKXY,pCode:"2000",productId:"JXJDD001"})},{pType:"FOTICJGXY",title:"《借款咨询服务协议》",url:this.getProtocolUrl({pType:"FOTICJGXY",pNo:l.default.PNO.FOTICJGXY,pCode:"3000",productId:"JXJDD001"})},{pType:"YRDDQXY",title:"《电子签名数字证书用户使用须知》",url:this.getProtocolUrl({pType:"YRDDQXY",pNo:l.default.PNO.YRDDQXY,pCode:"7000",productId:"JXJDD001"})}]
},bxProtocols:function(){return[{pType:"BXJKXY",title:"《百度有钱花·大额尊享服务借款协议》",url:this.getProtocolUrl({pType:"BXJKXY",pNo:l.default.PNO.BXJKXY,pCode:"2000",productId:"JXJDD001"})}]},protocols:function X(){var X=[];
if(null===this.unionInfo)return X;if(2===this.mode&&X.push(this.creditProtocol),this.isSelfLoan)X=X.concat(this.dfModeId===this.repayMode?this.dfLoanProtocols:this.bdLoanProtocols);else switch(this.unionKey){case"yrd":X=X.concat(this.yiRenDaiProtocols);
break;case"fotic":X=X.concat(this.foticProtocols);break;case"bx":X=X.concat(this.bxProtocols)}return(1===this.mode&&0===this.autoPay||2===this.mode&&1===this.autoPay)&&X.push(this.autoPayProtocol),X}},methods:{getProtocolUrl:function(t){return l.default.FRONT_SYSTEM+u.default.protocolDetail+"?"+a.default.stringify(t)
},goProtocol:function(t){"function"==typeof this.itemClick?this.itemClick(t):s.redirect(t.url)}},components:{}};var P='<section class="fin-{{ is }}">\n    <div class="mode1" v-if="1 === mode">\n        <div class="prot-item" v-for="item in protocols" @click="goProtocol(item)" :key="item.$index">\n            {{item.title}}\n        </div>\n    </div>\n    <div class="mode2" v-if="2 === mode">\n        <div class="prot-item" v-for="item in protocols" @click="goProtocol(item)" :key="item.$index">\n            {{item.title}}\n            <i class="icon icon-arrow-right"></i>\n        </div>\n    </div>\n</section>';
e&&e.exports&&(e.exports.template=P),o&&o.default&&(o.default.template=P),e.exports=o["default"]});
;define("hiloan:app/component/protocols/index",function(e,o,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var l=e("hiloan:app/component/protocols/protocols.vue"),u=n(l);
o.default=u.default,t.exports=o["default"]});
;define("hiloan:app/component/pop-protocols/pop-protocols.vue",function(e,t,o){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:app/component/select-popup/index"),p=a(n),r=e("hiloan:app/component/protocols/index"),i=a(r),l=e("hiloan:app/static/config/env-conf"),s=a(l),u=e("hiloan:app/static/config/front-system-path"),c=a(u),d=e("hiloan:node_modules/query-string/index"),f=a(d),y=e("hiloan:components/fin-fg/util/index");
t.default={props:{repayMode:{type:Number,"default":0},repayType:{type:String,"default":""},money:{type:String,"default":""},dueDate:{type:String,"default":""},payperiods:{type:Number,"default":0},feeTotal:{type:Number,"default":0},chargeRate:{type:String,"default":""},chargeTotal:{type:Number,"default":0},chargePeriod:{type:Number,"default":0},autoPay:{type:Number,"default":0},unionInfo:{type:Object},oneMoney:{type:String,"default":""},lastMoney:{type:String,"default":""},bankName:{type:String,"default":""}},data:function(){return{is:"pop-protocols"}
},methods:{getProtocolUrl:function(e){return s.default.FRONT_SYSTEM+c.default.protocolDetail+"?"+f.default.stringify(e)},jumpProtocol:function(e){var t="--";this.chargeTotal&&(t=y.format.money(this.chargeTotal));
var o="--";this.chargePeriod&&(o=y.format.money(this.chargePeriod));var a="--";this.feeTotal&&(a=y.format.money(this.feeTotal)),y.redirect(e.url,{periods:this.payperiods||"--",dueDate:this.dueDate||"--",repayMode:this.repayMode,repayModeText:this.repayType||"--",money:this.money||"--",feeTotal:a,chargeRate:this.chargeRate||"--",chargeTotal:t,chargePeriod:o,oneMoney:this.oneMoney||"--",lastMoney:this.lastMoney||"--",bankName:this.bankName||"--"})
}},components:{selectPopup:p.default,protocols:i.default}};var m='<section class="fin-{{ is }}">\n    <div class="sub-intro">点击下一步即表示您已同意\n        <select-popup :need-arrow-select="false" trigger-area="借款等相关协议" subtitle="查看相关协议" class="protocols auto-popup" skin="popup-protocol" v-log="\'agreement\'">\n            <div slot="popup">\n                <protocols :mode="1" :union-info="unionInfo" :auto-pay="autoPay" :item-click="jumpProtocol" :repay-mode="repayMode">\n                </protocols>\n                <!--\n                <div class="protocol-item"\n                    v-for="item in protocolList"\n                    :key="item.$index"\n                    @click="jumpProtocol(item)">\n                    {{item.title}}\n                </div>\n                -->\n            </div>\n        </select-popup>\n    </div>\n</section>';
o&&o.exports&&(o.exports.template=m),t&&t.default&&(t.default.template=m),o.exports=t["default"]});
;define("hiloan:app/component/pop-protocols/index",function(o,e,p){"use strict";function t(o){return o&&o.__esModule?o:{"default":o}}Object.defineProperty(e,"__esModule",{value:!0});var n=o("hiloan:app/component/pop-protocols/pop-protocols.vue"),l=t(n);
e.default=l.default,p.exports=e["default"]});
;define("hiloan:app/component/protocol/protocol.vue",function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=t("hiloan:components/fin-fg/util/index");e.default={props:{skin:{type:String,"default":""},preText:{type:String,"default":""},protocolText:{type:String,"default":""},protocolAddr:{type:String,"default":""},params:{type:Object,"default":function(){return{}
}}},data:function(){return{is:"protocol"}},methods:{goProtocal:function(){n.redirect(this.protocolAddr,this.params)}},components:{}};var r='<section class="fin-{{ is }} {{skin}}">\n    <span>{{preText}}</span>\n    <a href="javascript:void(0);" @click="goProtocal">{{protocolText}}</a>\n</section>';
o&&o.exports&&(o.exports.template=r),e&&e.default&&(e.default.template=r),o.exports=e["default"]});
;define("hiloan:app/component/protocol/index",function(e,o,t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var l=e("hiloan:app/component/protocol/protocol.vue"),u=n(l);
o.default=u.default,t.exports=o["default"]});
;define("hiloan:app/component/provider-logo/provider-logo.vue",function(n,e,o){"use strict";function t(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(e,"__esModule",{value:!0});var i=(n("hiloan:components/fin-fg/util/index"),n("hiloan:app/static/config/env-conf")),a=(t(i),n("hiloan:app/static/config/union-conf")),s=t(a);
e.default={props:{unionloanMode:{type:String,"default":""},unionloanCode:{type:String,"default":""}},data:function(){return{is:"provider-logo"}},computed:{unionCls:function(){switch(this.unionloanMode.toUpperCase()){case"S":return"yqh";
case"F":case"C":return s.default[this.unionloanCode].key||"";default:return""}}},methods:{},components:{}};var l='<section class="fin-{{ is }}">\n    <p class="logo-title">\n        额度提供方\n    </p>\n    <div class="banner-wrapper">\n        <i class="bank-icon yqh border" v-if="\'C\' === unionloanMode"></i>\n        <i class="bank-icon" :class="unionCls"></i>\n    </div>\n</section>';
o&&o.exports&&(o.exports.template=l),e&&e.default&&(e.default.template=l),o.exports=e["default"]});
;define("hiloan:app/component/provider-logo/index",function(e,o,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var r=e("hiloan:app/component/provider-logo/provider-logo.vue"),d=t(r);
o.default=d.default,n.exports=o["default"]});
;define("hiloan:app/component/wallet-pay/wallet-pay.vue",function(t,e,a){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(e,"__esModule",{value:!0});var n=t("hiloan:app/static/config/api"),s=o(n),i=t("hiloan:components/fin-ui/ui-toast/index"),u=o(i),l=t("hiloan:components/fin-ui/ui-dialog/index"),r=o(l),c=t("hiloan:components/fin-fg/util/index");
e.default={props:{showToast:{type:Boolean,"default":!1},toastMsg:{type:String,"default":""},showDialog:{type:Boolean,"default":!1},repayType:{type:String,"default":""}},data:function(){return{is:"wallet-pay",urlParams:null,newTid:"",amount:0,orderInfo:null,from:0,timeout:!1}
},methods:{doPay:function(){var t=this;Agent.pay(this.orderInfo,function(e){if(void 0!==e.statecode&&null!==e.statecode){var a=parseInt(e.statecode,10);switch(a){case 0:t.openToast("支付成功"),t.countDown(30),t.checkRepayOrder();
break;case 1:t.openToast("支付中"),t.countDown(30),t.checkRepayOrder();break;case 2:t.openToast("支付已经取消"),c.eventBus.$emit("unlock-pay-btn");break;case 3:t.openToast("很抱歉，不支持此种支付方式"),c.eventBus.$emit("unlock-pay-btn");
break;case 4:t.openToast("很抱歉，支付失败"),c.eventBus.$emit("unlock-pay-btn");break;case 5:t.openToast("登录失败"),c.eventBus.$emit("unlock-pay-btn");break;default:t.showDialog=!0}}})},checkRepayOrder:function(){var t=this;
s.default.checkRepayOrder({tid:this.newTid}).then(function(e){var a=e.data,o=parseInt(a.tstatus,10);5!==o||t.timeout?t.toRepayResult(2===o?"success":"process"):t.checkRepayOrder()})},toRepayResult:function(t){c.redirect("/hiloan/trans/tplRepayResult",{status:t,tid:this.newTid,amount:this.amount,repayType:this.repayType})
},countDown:function(t){var e=this;this.timeout=!1;var a=setInterval(function(){t--,0===t&&(e.timeout=!0,clearInterval(a))},1e3)},openToast:function(t){this.toastMsg=t,this.showToast=!0}},components:{uiToast:u.default,uiDialog:r.default},created:function(){var t=this;
c.eventBus.$on("on-wallet-repay",function(e,a,o,n){t.urlParams=e,t.newTid=a,t.amount=o,t.orderInfo=n,t.doPay()})}};var p='<section class="fin-{{ is }}">\n    <ui-toast :show.sync="showToast" :content="toastMsg"></ui-toast>\n    <ui-dialog :show.sync="showDialog" type="alert" title="还款失败" ok="知道啦">\n        <div slot="content">\n            "还款失败，请稍候再试"\n        </div>\n    </ui-dialog>\n</section>';
a&&a.exports&&(a.exports.template=p),e&&e.default&&(e.default.template=p),a.exports=e["default"]});
;define("hiloan:app/component/wallet-pay/index",function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:app/component/wallet-pay/wallet-pay.vue"),o=l(n);
t.default=o.default,a.exports=t["default"]});
;define("hiloan:app/component/repay-btn/repay-btn.vue",function(t,a,e){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(a,"__esModule",{value:!0});var o=t("hiloan:app/static/config/api"),p=n(o),i=t("hiloan:components/fin-fg/util/index"),u=t("hiloan:app/static/config/env-conf"),l=n(u),r=t("hiloan:app/static/config/front-system-path"),c=n(r),s=t("hiloan:app/static/config/constant"),d=t("hiloan:app/component/pc-repay-dlg/index"),y=n(d),f=t("hiloan:app/component/wallet-pay/index"),m=n(f),h=t("hiloan:components/fin-fg/track/log"),P=n(h);
a.default={data:function(){return{is:"repay-btn",showPCRepay:!1,repayApiData:{}}},props:{lockPayBtn:{type:Boolean,"default":!1},urlParams:{type:Object,"default":function(){return{}}},coupons:{type:Array,"default":function(){return[]
}},principal:{type:Number,"default":0},repayAmount:{type:Number,"default":0},discountMoney:{type:Number,"default":0},autoPay:{type:Boolean,"default":!0},fixBottom:{type:Boolean,"default":!1}},computed:{},methods:{jumpAutoProtocol:function(){location.href=l.default.LARK_SYSTEM+c.default.autoRepay+"?"+s.defaultQuery
},submit:function(){var t=this;P.default.sendBfbAction("dopay");var a=[];a.push({tid:this.urlParams.tid,money:this.repayAmount,coupons:this.coupons,discountmoney:this.discountMoney,principal:this.principal,periods:1,product:{productid:this.urlParams.productId,projectid:this.urlParams.projectId}}),p.default.repay({transactions:JSON.stringify(a)}).then(function(a){var e=a.data;
t.lockPayBtn=!0,t.repayApiData=e,1===parseInt(e.isNeedPCRepay,10)?t.showPCRepay=!0:t.doRepay()})},doRepay:function(){i.eventBus.$emit("on-wallet-repay",this.urlParams,this.repayApiData.newTid,this.repayAmount,this.repayApiData.orderInfo,0)
}},components:{pcRepayDlg:y.default,walletPay:m.default},created:function(){var t=this;i.eventBus.$on("pc-dialog-close",function(){t.showPCRepay=!1,t.lockPayBtn=!1}),i.eventBus.$on("unlock-pay-btn",function(){t.lockPayBtn=!1
}),i.eventBus.$on("change-card-for-repay",function(){t.doRepay()})}};var v='<section class="fin-{{ is }}">\n    <div class="repay-btn" :class="{\'bottom-btn\': fixBottom}">\n        <p class="protocal-tip" v-if="!autoPay">点击确定还款即表示您已同意<span @click="jumpAutoProtocol">《有钱花自动还款协议》</span></p>\n        <div class="fin-ui-button full" @click="submit" :disabled.sync="lockPayBtn">确认还款￥{{repayAmount | formatMoney}}</div>\n    </div>\n\n    <pc-repay-dlg :show="showPCRepay" :repay-api-data="repayApiData" :repay-amount="repayAmount">\n    </pc-repay-dlg>\n\n    <wallet-pay></wallet-pay>\n</section>';
e&&e.exports&&(e.exports.template=v),a&&a.default&&(a.default.template=v),e.exports=a["default"]});
;define("hiloan:app/component/repay-btn/index",function(e,n,t){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:app/component/repay-btn/repay-btn.vue"),u=a(o);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:app/component/repay-due/repay-due.vue",function(n,e,t){"use strict";function o(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(e,"__esModule",{value:!0});var a=n("hiloan:node_modules/underscore/underscore"),u=o(a),c=n("hiloan:components/fin-fg/util/index"),l=n("hiloan:app/static/config/env-conf"),i=o(l),r=n("hiloan:app/static/config/front-system-path"),s=(o(r),n("hiloan:app/static/config/constant")),p=n("hiloan:app/component/menulist/index"),d=o(p),f=n("hiloan:app/component/repay-btn/index"),y=o(f),m=n("hiloan:node_modules/query-string/index"),h=o(m);
e.default={props:{urlParams:{type:Object,"default":function(){return{}}},lockPayBtn:{type:Boolean,"default":!1},calcuInfo:{type:Object,"default":function(){return{}}},autoPay:{type:Boolean,"default":!1},bOct30:{type:Boolean,"default":!1},showRate:{type:Boolean,"default":!1},couponText:{type:String,"default":""}},data:function(){return{is:"repay-due"}
},computed:{billInfo:function(){var n=[];return n=n.concat([{id:"duerepayPrincipal",label:"应还本金",content:"￥"+c.format.money(this.calcuInfo.duerepayPrincipal)},{id:"duerepayManagement",label:"借款服务费",content:"￥"+c.format.money(this.calcuInfo.duerepayManagement)}]),this.bOct30&&n.push({id:"",label:"分期手续费",content:"￥"+c.format.money(this.calcuInfo.duerepayCharges)}),this.showRate&&n.push({id:"duerepayInterest",label:"总利息",content:"￥"+c.format.money(this.calcuInfo.duerepayInterest)}),n.push({id:"discountAmount",label:"优惠券",content:this.couponText,arrow:!0,"class":"highlight"}),n
},repayAmountInfo:function(){return[{id:"duerepayTotalMoney",label:"应还总额",content:"￥"+c.format.money(this.calcuInfo.duerepayTotalMoney),"class":"bigstrong"}]},couponIds:function b(){var b=[];return this.calcuInfo.coupons&&this.calcuInfo.coupons.forEach(function(n){b.push(n.couponid)
}),b}},methods:{selectCoupon:function(){var n=u.default.extend({tid:this.urlParams.tid,money:this.calcuInfo.duerepayTotalMoney,couponIds:this.joinCouponIds(this.couponIds),totalMoney:this.calcuInfo.noDiscountDueTotalMoney,trialAmountType:0},s.defaultArgs),e={cinPack:encodeURIComponent(h.default.stringify(n)),backUrl:encodeURIComponent(location.href)};
location.href=i.default.REPAY_COUPON+h.default.stringify(e)},joinCouponIds:function(n){return n instanceof Array&&n.length>0?n.join("_"):""}},components:{menulist:d.default,repayBtn:y.default},created:function(){var n=this;
c.eventBus.$on("menu-list-click",function(e){"discountAmount"===e&&n.selectCoupon()})}};var I='<section class="fin-{{ is }}">\n    <div class="info-title">\n        {{calcuInfo.loanDate | formatDateTime}}\n        借 ￥{{calcuInfo.totalMoney | formatMoney}} | \n        第{{calcuInfo.currentPeriod}}/{{calcuInfo.totalPeriods}}期\n    </div>\n    <menulist :key-vals="billInfo"></menulist>\n    <div class="row-line"></div>\n    <menulist :key-vals="repayAmountInfo"></menulist>\n    <repay-btn :url-params="urlParams" :coupons="calcuInfo.coupons" :principal="calcuInfo.duerepayPrincipal" :repay-amount="calcuInfo.duerepayTotalMoney" :discount-money="calcuInfo.totalDiscountAmount" :auto-pay="autoPay" :lock-pay-btn.sync="lockPayBtn">\n    </repay-btn>\n</section>';
t&&t.exports&&(t.exports.template=I),e&&e.default&&(e.default.template=I),t.exports=e["default"]});
;define("hiloan:app/component/repay-due/index",function(e,u,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(u,"__esModule",{value:!0});var a=e("hiloan:app/component/repay-due/repay-due.vue"),d=t(a);
u.default=d.default,n.exports=u["default"]});
;define("hiloan:app/component/repay-over/repay-over.vue",function(n,o,e){"use strict";function t(n){return n&&n.__esModule?n:{"default":n}}Object.defineProperty(o,"__esModule",{value:!0});var a=n("hiloan:node_modules/underscore/underscore"),i=t(a),l=n("hiloan:components/fin-fg/util/index"),r=n("hiloan:app/static/config/env-conf"),u=t(r),c=n("hiloan:app/static/config/front-system-path"),s=(t(c),n("hiloan:app/static/config/constant")),f=n("hiloan:app/component/menulist/index"),d=t(f),m=n("hiloan:app/component/repay-btn/index"),p=t(m),v=n("hiloan:node_modules/query-string/index"),y=t(v);
o.default={props:{urlParams:{type:Object,"default":function(){return{}}},lockPayBtn:{type:Boolean,"default":!1},calcuInfo:{type:Object,"default":function(){return{}}},autoPay:{type:Boolean,"default":!1},bOct30:{type:Boolean,"default":!1},showRate:{type:Boolean,"default":!1},couponText:{type:String,"default":""}},data:function(){return{is:"repay-over"}
},computed:{skin:function(){return this.multiDue?this.autoPay?"multi-due":"multi-due auto-proto":void 0},billInfo:function(){var n=this.calcuInfo.overFeeInfo[0],o=[];return o=o.concat([{id:"overPri",label:"应还本金",content:"￥"+l.format.money(n.overPri)},{id:"overManagement",label:"借款服务费",content:"￥"+l.format.money(n.overManagement)}]),this.bOct30&&o.splice(2,0,{id:"",label:"分期手续费",content:"￥"+l.format.money(n.overPeriodFee)}),this.showRate&&o.push({id:"overInte",label:"总利息",content:"￥"+l.format.money(n.overInte)}),o.push({id:"overPena",label:"罚息",content:"￥"+l.format.money(n.overPena),"class":"warn"}),o
},repayAmountInfo:function(){var n=[];return n.push({id:"discountAmount",label:"优惠券",content:this.couponText,arrow:!0,"class":"highlight"}),n.push({id:"duerepayTotalMoney",label:"应还总额",content:"￥"+l.format.money(this.calcuInfo.overTotalMoney),"class":"bigstrong"}),n
},multiDue:function(){return this.calcuInfo.overFeeInfo?this.calcuInfo.overFeeInfo.length>1:!1},overCoupons:function(){var n=[];return this.calcuInfo.overFeeInfo instanceof Array&&this.calcuInfo.overFeeInfo.length>0&&this.calcuInfo.overFeeInfo.forEach(function(o){n=n.concat(o.overCoupons)
}),n},couponIds:function I(){var I=[];return this.overCoupons instanceof Array&&this.overCoupons.length>0&&this.overCoupons.forEach(function(n){I.push(n.couponId)}),I}},methods:{getDueInfo:function(n){var o=[];
return o=o.concat([{id:"overMoney",label:"第"+n.overPeriod+"期 逾期应还总额",content:"￥"+l.format.money(n.overMoney),"class":"strong"},{id:"overPri",label:"应还本金",content:"￥"+l.format.money(n.overPri),"class":"small"},{id:"overManagement",label:"借款服务费",content:"￥"+l.format.money(n.overManagement),"class":"small"}]),this.bOct30&&o.push({id:"",label:"分期手续费",content:"￥"+l.format.money(n.overPeriodFee),"class":"small"}),this.showRate&&o.push({id:"overInte",label:"总利息",content:"￥"+l.format.money(n.overInte),"class":"small"}),o.push({id:"overPena",label:"罚息",content:"￥"+l.format.money(n.overPena),"class":"small warn"}),o
},selectCoupon:function(){var n=i.default.extend({tid:this.urlParams.tid,money:this.calcuInfo.overTotalMoney,couponIds:this.joinCouponIds(this.couponIds),totalMoney:this.calcuInfo.overNoDiscountTotalMoney,trialAmountType:2},s.defaultArgs),o={cinPack:encodeURIComponent(y.default.stringify(n)),backUrl:encodeURIComponent(location.href)};
location.href=u.default.REPAY_COUPON+y.default.stringify(o)},joinCouponIds:function(n){return n instanceof Array&&n.length>0?n.join("_"):""}},components:{menulist:d.default,repayBtn:p.default},created:function(){var n=this;
l.eventBus.$on("menu-list-click",function(o){"discountAmount"===o&&n.selectCoupon()})}};var h='<section class="fin-{{ is }}">\n    <div class="{{skin}}">\n        <div class="info-title">\n            {{calcuInfo.loanDate | formatDateTime}}\n            借￥{{calcuInfo.totalMoney | formatMoney}} | \n            共{{calcuInfo.totalPeriods}}期\n        </div>\n\n        <!-- 只有一期 -->\n        <menulist v-if="!multiDue" :key-vals="billInfo"></menulist>\n\n        <!-- 多期 -->\n        <div v-if="multiDue">\n            <div v-for="item in calcuInfo.overFeeInfo" :key="item.overPeriod">\n                <menulist :key-vals="getDueInfo(item)"></menulist>\n            </div>\n        </div>\n\n        <div class="row-line"></div>\n\n        <menulist :key-vals="repayAmountInfo"></menulist>\n        \n        <repay-btn :url-params="urlParams" :coupons="overCoupons" :principal="calcuInfo.overPriTotalMoney" :repay-amount="calcuInfo.overTotalMoney" :discount-money="calcuInfo.totalDiscountAmount" :auto-pay="autoPay" :fix-bottom="multiDue" :lock-pay-btn.sync="lockPayBtn">\n        </repay-btn>\n    </div>\n</section>';
e&&e.exports&&(e.exports.template=h),o&&o.default&&(o.default.template=h),e.exports=o["default"]});
;define("hiloan:app/component/repay-over/index",function(e,o,n){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var a=e("hiloan:app/component/repay-over/repay-over.vue"),r=t(a);
o.default=r.default,n.exports=o["default"]});
;define("hiloan:app/component/repaydetail/repaydetail-list/repaydetail-list.vue",function(e,t,a){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var n=e("hiloan:components/fin-ui/ui-tab/index"),s=i(n);e("hiloan:components/fin-fg/directive/log/index"),t.default={props:{stageList:{type:Array,required:!0},repayList:{type:Array,required:!1,"default":[]},tstatus:{type:Number,required:!0},currentperiod:{type:Number,required:!0},showPeriodFee:{type:Boolean,"default":!1},repayType:{type:Number,"default":0}},data:function(){return{is:"repaydetail-list",currentTabIndex:0,tabOption:[{name:"分期详情",index:0},{name:"还款记录",index:1}],hasPaidAll:!1}
},methods:{},components:{uiTab:s.default},created:function(){12===this.tstatus&&(this.hasPaidAll=!0,this.currentTabIndex=1)}};var r='<section class="fin-{{ is }}">\n    <ui-tab :index.sync="currentTabIndex" :items="tabOption" v-if="!hasPaidAll"></ui-tab>\n    <div v-else="" class="has-paid-all" v-log="\'repayRecord\'">还款记录</div>\n    <section v-if="currentTabIndex === 0" class="stage-list">\n        <div v-for="item in stageList" class="detail-item" :class="[{paid: +item.bStatus === 48}, {overdue: +item.bStatus === 144 || (+item.bStatus === 112 &amp;&amp; +item.period < currentperiod)}]">\n            <div class="date">\n                <div class="unit">\n                    <span class="count">{{item.period}}</span>期\n                    <div v-if="+item.bStatus === 144 || (+item.bStatus === 112 &amp;&amp; +item.period < currentperiod)" class="overdue-period"></div>\n                    <template v-else="">\n                        <div v-if="tstatus !== 12 &amp;&amp; currentperiod === +item.period" class="current-period"></div>\n                    </template>\n                </div>\n                <div class="date-detail">{{item.dueDate}}</div>\n            </div>\n            <div class="detail-info">\n                <p class="detail-total">￥{{item.money | formatMoney}}</p>\n                <p class="detail-details">\n                本金{{item.prinamt | formatMoney}}\n                <span v-if="1 === item.period">+ 借款服务费{{item.management | formatMoney}}</span>\n                <span v-if="showPeriodFee">+ 分期手续费{{item.fee | formatMoney}}</span>\n                <span v-if="1 !== repayType">+ 利息{{item.intamt | formatMoney}}</span>\n                <span v-if="item.interest">+ 罚息{{item.interest | formatMoney}}</span>\n                <span v-if="item.discount">- 优惠券抵扣{{item.discount | formatMoney}}</span>\n                <span v-if="item.paiddiscount &amp;&amp; +item.bStatus === 48">- 优惠券抵扣{{item.paiddiscount | formatMoney}}</span>\n                </p>\n            </div>\n        </div>\n    </section>\n    <section v-if="currentTabIndex === 1" class="repay-list">\n        <div class="no-record" v-if="!repayList || repayList.length === 0">暂无还款记录</div>\n        <div v-else="" v-for="(index, item) in repayList" class="detail-item">\n            <div class="detail-info">\n                <template v-if="!+item.orderStatus">\n                    <p class="detail-total">￥{{item.repayAmount | formatMoney}}</p>\n                    <p class="detail-details">\n                    本金{{item.repayPrincipal | formatMoney}}\n                    <span v-if="item.repayManagement">+ 借款服务费{{item.repayManagement | formatMoney}}</span>\n                    <span v-if="showPeriodFee">+ 分期手续费{{item.repayFee | formatMoney}}</span>\n                    <span v-if="1 !== repayType">+ 利息{{item.repayInterest | formatMoney}}</span>\n                    <span v-if="item.repayPenalty">+ 罚息{{item.repayPenalty | formatMoney}}</span>\n                    <span v-if="item.repayViolate">+ 提前还款违约金{{item.repayViolate | formatMoney}}</span>\n                    <span v-if="item.reducedTotalAmount">- 优惠券抵扣{{item.reducedTotalAmount | formatMoney}}</span>\n                    </p>\n                </template>\n                <p v-else="" class="recording">入账中</p>\n\n                <div v-if="repayList.length === 1" class="help-block-whole"></div>\n                <template v-else="">\n                    <div v-if="index === 0" class="help-block-top"></div>\n                    <div v-if="index === (repayList.length - 1)" class="help-block-bottom"></div>\n                </template>\n            </div>\n            <div class="date">\n                <div class="unit"><span class="count">{{item.repayOrderUpdateTime | formatDateTime}}</span></div>\n                <div class="dot"></div>\n            </div>\n        </div>\n    </section>\n</section>';
a&&a.exports&&(a.exports.template=r),t&&t.default&&(t.default.template=r),a.exports=t["default"]});
;define("hiloan:app/component/repaydetail/repaydetail-list/index",function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:app/component/repaydetail/repaydetail-list/repaydetail-list.vue"),d=l(i);
t.default=d.default,a.exports=t["default"]});
;define("hiloan:app/component/repaydetail/repaydetail-title/repaydetail-title.vue",function(i,e,n){"use strict";function o(i){return i&&i.__esModule?i:{"default":i}}Object.defineProperty(e,"__esModule",{value:!0}),i("hiloan:components/fin-fg/filter/index"),i("hiloan:components/fin-fg/directive/log/index");
var t=i("hiloan:components/fin-fg/util/index"),l=i("hiloan:components/fin-ui/ui-dialog/index"),a=o(l);e.default={props:{bill:{type:Object,required:!0},showProtocols:{type:Boolean,"default":!1},showPeriodFee:{type:Boolean,"default":!1}},data:function(){return{is:"repaydetail-title",overDueDialogShow:!1,dialogContent:""}
},components:{uiDialog:a.default},methods:{prepay:function(){return+this.bill.canRepay?14===+this.bill.tstatus?(this.dialogContent="请先还清逾期部分欠款再全部结清",void(this.overDueDialogShow=!0)):void t.redirect("/hiloan/trans/tplConfirmPrepay",{tid:this.bill.tid,feeStatus:1}):(this.dialogContent="当天的借款不能进行还款操作 <br/>请明天尝试",void(this.overDueDialogShow=!0))
},goProtocolList:function(){t.redirect("/hiloan/user/tplProtocolsList",{periods:this.bill.periods,repayType:this.bill.repayType})}}};var s='<section class="fin-{{ is }}">\n    <div class="card-top"></div>\n    <div class="details-wrap">\n        <div class="details-header">\n            <div class="loan-info">\n               <div>{{bill.paidtime | formatDateTime}}&nbsp;借&nbsp;￥{{bill.money | formatMoney}}&nbsp;|&nbsp;共{{bill.periods}}期</div>\n               <div class="reciver-card">收款卡: {{bill.loanBankName}} (尾号{{bill.loanBankCard}})</div>\n            </div>\n\n            <span v-if="showProtocols" class="protocols" @click="goProtocolList"><i class="icon_files"></i>相关协议</span>\n        </div>\n        <div class="details-content">\n            <div v-if="bill.tstatus === 12" class="clear">\n                已结清\n            </div>\n            <div v-else="">\n                <div class="info">\n                    应还总额 (元)\n                </div>\n                <div class="show-money-wrap">\n                    <div class="show-money dinalter">\n                        {{bill.totalPayMoney | formatMoney}}\n                    </div>\n                    <span class="right repay-btn-box current-repay-btn-box">\n                        <div class="fin-ui-button x-small" @click="prepay" :class="{gray: bill.canRepay === 0}" v-log="\'prepay\'">提前结清</div>\n                    </span>\n                </div>\n\n                <div class="show-details">\n                    包含本金{{bill.totalLeadPayprcp | formatMoney}}\n                    <span>+ 借款服务费{{bill.totalService | formatMoney}}</span>\n                    <span v-if="showPeriodFee">+ 分期手续费{{bill.totalPeriodFee | formatMoney}}</span>\n                    <span v-if="1 !== bill.repayType">+ 利息{{bill.totalInterest | formatMoney}}</span>\n                    <!-- <span v-if="bill.totalViolate">+ 提前还款违约金{{bill.totalViolate | formatMoney}}</span> -->\n                    <span v-if="bill.totalPenalty">+ 罚息{{bill.totalPenalty | formatMoney}}</span>\n                </div>\n            </div>\n        </div>\n    </div>\n</section>\n<ui-dialog :show.sync="overDueDialogShow" type="alert" :head="false" :content="dialogContent" ok="知道了" @ok="this.overDueDialogShow = false">\n</ui-dialog>';
n&&n.exports&&(n.exports.template=s),e&&e.default&&(e.default.template=s),n.exports=e["default"]});
;define("hiloan:app/component/repaydetail/repaydetail-title/index",function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=e("hiloan:app/component/repaydetail/repaydetail-title/repaydetail-title.vue"),d=l(i);
t.default=d.default,a.exports=t["default"]});
;define("hiloan:app/component/seam/seam.vue",function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={props:{},data:function(){return{is:"seam"}},methods:{},components:{}};
var n='<section class="fin-{{ is }}">\n    宣导页面\n</section>';s&&s.exports&&(s.exports.template=n),t&&t.default&&(t.default.template=n),s.exports=t["default"]});
;define("hiloan:app/component/seam/index",function(e,n,t){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var o=e("hiloan:app/component/seam/seam.vue"),u=a(o);
n.default=u.default,t.exports=n["default"]});
;define("hiloan:app/component/support-bank-dlg/support-bank-dlg.vue",function(t,n,e){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(n,"__esModule",{value:!0});var o=t("hiloan:components/fin-fg/util/index"),s=t("hiloan:components/fin-ui/ui-dialog/index"),a=i(s);
n.default={props:{show:{type:Boolean,"default":!1},supBankList:{type:Array,"default":[]}},data:function(){return{is:"support-bank-dlg"}},methods:{shortText:function(t){return/^-?[0-9]*$/.test(t)?+t>0?t/1e6+"万":"不限":t
},closeSupportBankDlg:function(){o.eventBus.$emit("close-supbank-dlg")}},components:{uiDialog:a.default},compiled:function(){}};var l='<section class="fin-{{ is }}">\n    <ui-dialog prefix="support-bank-dlg" :show.sync="show" type="lightbox">\n        <div slot="content">\n            <h1>银行卡收款限额表</h1>\n            <table>\n                <thead>\n                    <tr>\n                        <th>银行</th>\n                        <th>单笔限额</th>\n                        <th>日限额</th>\n                        <th>月限额</th>\n                    </tr>\n                </thead>\n                <tbody>\n                    <tr v-for="item in supBankList" :key="item">\n                        <td>{{item.bankName}}</td>\n                        <td>{{shortText(item.singleLimit)}}</td>\n                        <td>{{shortText(item.dayLimit)}}</td>\n                        <td>{{shortText(item.monthLimit)}}</td>\n                    </tr>\n                </tbody>\n            </table>\n            <div class="fin-ui-button full" @click="closeSupportBankDlg">知道了</div>\n        </div>\n    </ui-dialog>\n</section>';
e&&e.exports&&(e.exports.template=l),n&&n.default&&(n.default.template=l),e.exports=n["default"]});
;define("hiloan:app/component/support-bank-dlg/index",function(e,n,t){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(n,"__esModule",{value:!0});var u=e("hiloan:app/component/support-bank-dlg/support-bank-dlg.vue"),p=o(u);
n.default=p.default,t.exports=n["default"]});
;define("hiloan:app/component/select-card/select-card.vue",function(t,a,e){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(a,"__esModule",{value:!0}),t("hiloan:components/fin-fg/filter/index");
{var i=t("hiloan:app/static/config/api"),s=n(i),r=t("hiloan:app/component/arrow-select/index"),c=n(r),o=t("hiloan:app/component/support-bank-dlg/index"),d=n(o),l=t("hiloan:app/component/select-popup/index"),p=n(l),u=t("hiloan:components/fin-ui/ui-popup/index"),h=n(u),f=t("hiloan:components/fin-fg/util/index");
t("hiloan:node_modules/es6-promise/dist/es6-promise")}t("hiloan:components/fin-fg/directive/log/index"),a.default={props:{label:{type:String,"default":"收款银行卡"},popTitle:{type:String,"default":"请选择收款银行"},chooseCard:{type:Object},largeAmount:{type:Number,"default":0},inputMoney:{type:Number,"default":0},supBankList:{type:Array,"default":[]}},data:function(){return{is:"select-card",showPopup:!1,showSupBankDlg:!1,supBankBtn:!1,cardList:[],newCardUrl:"",cardValid:!0,tipText:"",theCard:"默认银行卡"}
},computed:{isLargeAmount:function(){return this.chooseCard&&this.inputMoney>this.largeAmount?!0:!1},cardInfoText:function(){if(this.chooseCard&&this.chooseCard.bankName){var t=(this.chooseCard.bankName||"")+this.parseCardType(this.chooseCard.cardType)+"("+(this.chooseCard.cardNo||"")+")";
return f.eventBus.$emit("card-info-text",{cardInfo:t,bankName:this.chooseCard.bankName}),t}return""},selectCon:function(){return this.chooseCard?this.chooseCard.logoUrl?'<img class="the-card-logo" src="'+this.chooseCard.logoUrl+'">'+this.cardInfoText:this.cardInfoText:"选择银行卡"
},camNotList:function(){var t=this;return this.cardList.filter(function(a){return 1===a.cardTypeDraw||2===a.cardTypeDraw||5===a.cardTypeDraw||6===a.cardTypeDraw?(a.tip="不支持收款",a):3===a.cardTypeDraw&&t.isLargeAmount?(a.tip="不支持大额收款",a):void 0
})},canList:function(){var t=this;return this.cardList.filter(function(a){return-1===t.camNotList.indexOf(a)?a:void 0})}},methods:{selectClick:function(){var t=this;return 0===this.cardList.length?s.default.bankcardlist().then(function(a){t.cardList=a.data&&a.data.cardList,t.newCardUrl=a.data.jumpUrl
}):!1},selectBkCard:function(t){this.theCard="此银行卡",this.chooseCard=t,f.eventBus.$emit("on-card-change",this.chooseCard),this.validateCard(),this.showPopup=!1},addNewCard:function(){location.href=this.newCardUrl
},parseCardType:function(t){var a=parseInt(t,10);switch(a){case 1:return"储蓄卡";case 2:return"信用卡";default:return"银行卡"}},validateCard:function(){if(this.cardValid=!1,this.chooseCard){var t=parseInt(this.chooseCard.cardTypeDraw,10);
switch(t){case 3:this.isLargeAmount?(this.tipText=this.theCard+"不支持大额收款，请更换",this.cardValid=!1,this.supBankBtn=!0):(this.cardValid=!0,this.supBankBtn=!1);break;case 1:case 2:case 5:case 6:this.tipText=this.theCard+"不支持收款，请更换",this.cardValid=!1,this.supBankBtn=!0;
break;default:this.cardValid=!0,this.supBankBtn=!1}}else this.tipText="请选择收款银行卡";f.eventBus.$emit("on-card-valid",this.cardValid)},showSupportBanks:function(){this.showSupBankDlg=!0}},components:{arrowSelect:c.default,uiPopup:h.default,selectPopup:p.default,supportBankDlg:d.default},filters:{cardType:function(t){return this.parseCardType(t)
}},watch:{inputMoney:function(){this.validateCard()},chooseCard:function(){this.validateCard()}},created:function(){var t=this;f.eventBus.$on("close-supbank-dlg",function(){t.showSupBankDlg=!1}),f.eventBus.$on("check-card-valid",function(){t.validateCard()
})},compiled:function(){f.eventBus.$emit("default-card-info",this.selectCon)}};var v='<section class="fin-{{ is }}">\n    <select-popup left-content="收款银行卡" :right-content="selectCon" :show-popup.sync="showPopup" :before-popup="selectClick" :can-scroll="true" skin="pop-bankcard" subtitle="请选择收款银行" :valid="cardValid" :tip-text="tipText">\n        <div slot="popup">\n            <ul>\n                <li class="card-item" v-for="card in canList" :key="card.cardNo" @click="selectBkCard(card)">\n                    <img class="card-logo" :src="card.logoUrl">\n                    <div class="main">\n                        <div class="con">\n                            <div class="bk-name">{{card.bankName}}</div>\n                            {{card.cardType | cardType}}<div class="bk-num">({{card.cardNo}})</div>\n                        </div>\n                    </div>\n                    <i v-if="chooseCard.accountNo === card.accountNo" class="selected"></i>\n                </li>\n                <li class="card-item" @click="addNewCard">\n                    <div class="add-card-btn"></div>\n                    <div class="main">\n                        <div class="con">使用新银行卡</div>\n                    </div>\n                    <i class="icon icon-arrow-right"></i>\n                </li>\n                <li class="card-item can-not" v-for="card in camNotList" :key="card.cardNo">\n                    <img class="card-logo" :src="card.logoUrl">\n                    <div class="main">\n                        <div class="con">{{card.bankName}}</div>\n                        <div class="tip">{{card.tip}}</div>\n                    </div>\n                </li>\n            </ul>\n        </div>\n        <div slot="errorTip">\n            <span class="error-left">{{tipText}}</span>\n            <span class="error-right" v-if="supBankBtn" @click="showSupportBanks">查看支持银行</span>\n        </div>\n    </select-popup>\n    <support-bank-dlg :show="showSupBankDlg" :sup-bank-list="supBankList"></support-bank-dlg>\n</section>';
e&&e.exports&&(e.exports.template=v),a&&a.default&&(a.default.template=v),e.exports=a["default"]});
;define("hiloan:app/component/select-card/index",function(e,t,n){"use strict";function a(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var c=e("hiloan:app/component/select-card/select-card.vue"),d=a(c);
t.default=d.default,n.exports=t["default"]});
;define("hiloan:app/component/select-period/select-period.vue",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var p=e("hiloan:app/component/arrow-select/index"),i=n(p),l=e("hiloan:app/component/select-popup/index"),s=n(l),u=e("hiloan:components/fin-ui/ui-popup/index"),a=n(u),c=e("hiloan:components/fin-fg/util/index");
e("hiloan:components/fin-fg/directive/log/index"),t.default={props:{skin:{type:String,"default":""},label:{type:String,"default":"您要借多久"},periods:{type:Array,"default":[]},value:{type:Number,"default":-1},loading:{type:Boolean,"default":!1}},data:function(){return{is:"select-period",showPopup:!1}
},computed:{selectCon:function(){return this.value>-1?this.value+"个月":'<span class="select-tip">选择分期</span>'}},methods:{arrowClick:function(){this.showPopup=!0},clickItem:function(e){this.value=e,this.showPopup=!1,c.eventBus.$emit("on-period-change",this.value)
},preventTouchMove:function(e){e.preventDefault()}},components:{arrowSelect:i.default,selectPopup:s.default,uiPopup:a.default}};var d='<section class="fin-{{ is }} {{skin}}">\n    <select-popup left-content="借多久" :right-content="selectCon" :show-popup.sync="showPopup" :can-scroll="false" :loading="loading" skin="pop-period" subtitle="选择分期" v-log="\'periodSelect\'">\n        <div slot="popup">\n            <div class="period-item" v-for="val in periods" @touchmove="preventTouchMove($event)" @click="clickItem(val)" :key="val">\n                {{val}}个月\n            </div>\n        </div>\n    </select-popup>\n</section>';
o&&o.exports&&(o.exports.template=d),t&&t.default&&(t.default.template=d),o.exports=t["default"]});
;define("hiloan:app/component/select-period/index",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var d=e("hiloan:app/component/select-period/select-period.vue"),l=n(d);
t.default=l.default,o.exports=t["default"]});
;define("hiloan:app/component/select-purpose/select-purpose.vue",function(e,o,t){"use strict";function p(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(o,"__esModule",{value:!0});var n=e("hiloan:app/component/select-popup/index"),s=p(n),u=e("hiloan:node_modules/underscore/underscore"),l=p(u),i=e("page/applyMoney"),c=p(i),a=(e("hiloan:components/fin-fg/util/index"),e("hiloan:components/fin-fg/track/log")),r=p(a);
e("hiloan:components/fin-fg/directive/log/index"),o.default={props:{skin:{type:String,"default":""},label:{type:String,"default":"您的借款用途"},purpose:{type:Number,"default":-1},loading:{type:Boolean,"default":!1}},data:function(){return l.default.extend({},c.default,{is:"select-purpose",showPopup:!1,purposesMap:{1:"个人消费",2:"房屋装修",3:"旅游出行",5:"在职深造",7:"其他消费"}})
},computed:{selectCon:function(){return this.purpose>-1?this.purposesMap[this.purpose]:'<span class="select-tip">选择用途</span>'},purposes:function(){return Object.keys(this.purposesMap)}},methods:{arrowClick:function(){this.showPopup=!0
},clickItem:function(e){this.purpose=e,this.showPopup=!1,r.default.sendBfbAction("loanUse")},preventTouchMove:function(e){e.preventDefault()}},components:{selectPopup:s.default}};var d='<section class="fin-{{ is }} {{skin}}">\n    <select-popup left-content="您的借款用途" :right-content="selectCon" :show-popup.sync="showPopup" :can-scroll="false" :loading="loading" skin="pop-purpose" subtitle="选择用途" v-log="\'purposeSelect\'">\n        <div slot="popup">\n            <div class="purpose-item" v-for="key in purposes" @touchmove="preventTouchMove($event)" @click="clickItem(+key)" :key="key">\n                {{purposesMap[+key]}}\n            </div>\n        </div>\n    </select-popup>\n    <p class="introduct">\n            借款提示：明确借款用途有助于您获得借款额度。借款可用于个人或者家庭消费用途，消费用途包括购买大额消费产品、外出旅游、房屋装修等个人或者家庭消费活动，不包括经营相关的购买企业设备、支付租金等与企业生产经营相关的用途。严禁用于股指、期货、债券、理财和房地产市场等与国家法律法规或者监管规范相违背的任何用途。\n        </p>\n</section>';
t&&t.exports&&(t.exports.template=d),o&&o.default&&(o.default.template=d),t.exports=o["default"]});
;define("hiloan:app/component/select-purpose/index",function(e,t,o){"use strict";function u(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:app/component/select-purpose/select-purpose.vue"),p=u(n);
t.default=p.default,o.exports=t["default"]});
;define("hiloan:app/component/select-repay-method/select-repay-method.vue",function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});
var o=e("hiloan:app/component/arrow-select/index"),l=i(o),s=e("hiloan:app/component/select-popup/index"),a=i(s),p=e("hiloan:components/fin-fg/util/index"),u=e("hiloan:components/fin-ui/ui-popup/index"),r=i(u),d=e("hiloan:app/component/repay-list/index"),c=i(d),f=e("hiloan:app/static/config/repay-modes"),h=i(f);
t.default={props:{repayModes:{type:Array,"default":function(){return[]}},repayMode:{type:Number,"default":0},repayText:{type:String,"default":""},bills:{type:Array,"default":function(){return[]}},loading:{type:Boolean,"default":!1},showRate:{type:Boolean,"default":!1}},data:function(){return{is:"select-repay-method",showPopup:!1}
},computed:{hasBills:function(){return this.bills.length>0},multiMode:function(){return this.repayModes.length>1},skin:function(){return this.hasBills?this.multiMode?"has-bills-multi":"has-bills":"no-bills"
},repayModeList:function(){return this.repayModes.map(function(e){return h.default[e]})},subtitle:function(){if(1===this.repayModes.length){var e=h.default[this.repayModes[0]];return e.name+'<p class="sub-title">'+e.hint+"</p>"
}return"怎么还"}},methods:{arrowClick:function(){this.showPopup=!0},preventTouchMove:function(e){e.preventDefault()},modeClick:function(e){this.repayMode=e,p.eventBus.$emit("on-method-change",e)}},components:{arrowSelect:l.default,uiPopup:r.default,selectPopup:a.default,repayList:c.default},created:function(){}};
var m='<section class="fin-{{ is }}">\n    <select-popup left-content="怎么还" :right-content="repayText" :subtitle="subtitle" :skin="skin" :can-scroll="true" :loading="loading" :need-btn="hasBills">\n        <div slot="popup">\n            <div>\n                <div v-if="multiMode" class="method-grp">\n                    <div class="method-btn" v-for="item in repayModeList" :key="item" track-by="$index" @click="modeClick(item.id)" :class="{\'actived\': item.id === repayMode}">\n                        <p class="method-name">{{item.name}}</p>\n                        <p class="method-hint">{{item.hintMulit}}</p>\n                    </div>\n                </div>\n                <p class="tip" v-if="bills.length === 0">\n                    <i class="icon icon-info"></i>\n                    输入借款金额，才可看到还款详情\n                </p>\n                <repay-list v-if="bills.length > 0" :bills="bills" :show-rate="showRate"></repay-list>\n            </div>\n        </div>\n    </select-popup>\n</section>';
n&&n.exports&&(n.exports.template=m),t&&t.default&&(t.default.template=m),n.exports=t["default"]});
;define("hiloan:app/component/select-repay-method/index",function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var a=e("hiloan:app/component/select-repay-method/select-repay-method.vue"),d=n(a);
t.default=d.default,o.exports=t["default"]});
;define("hiloan:app/static/config/supportapp.conf",function(e,a,n){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default={baiduMap:{lowestVersion:"10.1.8",channelSign:"bdmap",appName:"百度地图"},baiduCloud:{lowestVersion:"8.0.0",channelSign:"bdcloud",appName:"百度网盘"},baiduTieba:{lowestVersion:"*******",channelSign:"tieba",appName:"百度贴吧"},bdYouQianHua:{lowestVersion:"2.1.0",channelSign:"bdyouqianhua",appName:"百度有钱花"},baiduShipin:{lowestVersion:"7.9.9",channelSign:"bdshipin",appName:"百度视频"}},n.exports=a["default"]
});
;define("hiloan:app/component/version/index",function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{"default":t}}function o(){var t={method_name:"callAbout",checkUpdate:"1"};BLightApp&&BLightApp.invokeBdWalletNative(JSON.stringify(t),"callAboutSucc","callAboutFail")
}function p(t,e){if(d.indexOf(e)>=0)a.dialog({type:"alert",head:!1,ok:"知道了",content:"请升级"+l.default[e].appName+"到<br />最新版本，再来尝试借款"}).then(function(){window.BLightApp&&BLightApp.closeWindow()});else{var n=t>="2.6.0";
f&&!n?a.dialog({type:"alert",head:!1,ok:"知道了",content:u}).then(function(){window.BLightApp&&BLightApp.closeWindow()}):f&&n?a.dialog({type:"confirm",head:!1,ok:"去升级",cancel:"取消",content:u}).then(function(){o()
}).catch(function(){window.BLightApp&&BLightApp.closeWindow()}):a.dialog({type:"confirm",head:!1,ok:"去升级",cancel:"取消",content:u}).then(function(t){location.href="https://itunes.apple.com/app/apple-store/id992762663&mt=8",t.hideUi(),setTimeout(function(){window.BLightApp&&BLightApp.closeWindow()
},300)}).catch(function(){window.BLightApp&&BLightApp.closeWindow()})}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=p;var a=t("hiloan:components/fin-fg/util/ui/index"),c=t("hiloan:app/static/config/supportapp.conf"),l=i(c),d=Object.keys(l.default),u="请升级百度钱包到<br />最新版本，再来尝试借款",h=navigator.userAgent.toLowerCase(),f=/android/i.test(h);
n.exports=e["default"]});
;define("hiloan:app/component/wallet/index",function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var n=e("hiloan:app/component/wallet/auth"),u=l(n),o=e("hiloan:app/component/wallet/buildAuthPath"),d=l(o),p=e("hiloan:app/component/wallet/checkIdCard"),c=l(p);
t.default={auth:u.default,buildAuthPath:d.default,checkIdCard:c.default},a.exports=t["default"]});
;function _bfbStatistic(t,i,n){var c={en:i};"page"===t&&(c.eg="in"),n&&(c.ev=n),window.BfbStatistic&&window.BfbStatistic.sendUserActionData(c)}!function(){window.statisticUserActionSwitcher=!0}();
;!function(){var e=location.pathname,t=e.split("/").filter(function(e){return""!==e}).join("_");window.alogObjectConfig={sample:"1",product:"***",page:t,monkey_page:"",speed_page:"",speed:{sample:"1"},monkey:{sample:"1"},exception:{sample:"1"},feature:{sample:"1"},cus:{sample:"1"},csp:{sample:"","default-src":[{match:"*bae.baidu.com",target:"Accept,Warn"},{match:"*.baidu.com,*.bdstatic.com,*.baidustatic.com,*.baiduimg.com,*.bdimg.com,localhost,*.hao123.com,*.hao123img.com",target:"Accept"},{match:/^(127|172|192|10)(\.\d+){3}$/,target:"Accept"},{match:"*",target:"Accept,Warn"}]}}
}(),void function(e,t,a,o,n,c,i){e.alogObjectName=n,e[n]=e[n]||function(){(e[n].q=e[n].q||[]).push(arguments)},e[n].l=e[n].l||+new Date,o="https:"===e.location.protocol?"https://fex.bdstatic.com"+o:"http://fex.bdstatic.com"+o;
var m=!0;if(e.alogObjectConfig&&e.alogObjectConfig.sample){var s=Math.random();e.alogObjectConfig.rand=s,s>e.alogObjectConfig.sample&&(m=!1)}m&&(c=t.createElement(a),c.async=!0,c.src=o+"?v="+~(new Date/864e5)+~(new Date/864e5),i=t.getElementsByTagName(a)[0],i.parentNode.insertBefore(c,i))
}(window,document,"script","/hunter/alog/alog.mobile.min.js","alog"),void function(){function e(){}window.PDC={mark:function(e,t){alog("speed.set",e,t||+new Date),alog.fire&&alog.fire("mark")},init:function(e){alog("speed.set","options",e)
},view_start:e,tti:e,page_ready:e}}(),void function(e){var t=!1;e.onerror=function(e,a,o,n){var c=!0;return!a&&/^script error/i.test(e)&&(t?c=!1:t=!0),c&&alog("exception.send","exception",{msg:e,js:a,ln:o,col:n}),!1
},alog("exception.on","catch",function(e){alog("exception.send","exception",{msg:e.msg,js:e.path,ln:e.ln,method:e.method,flag:"catch"})})}(window);
;!function(e,t){function i(){var t=o.getBoundingClientRect().width;t/m>540&&(t=540*m);var i=t/10;o.style.fontSize=i+"px",c.rem=e.rem=i}function a(){var e=o.getBoundingClientRect().width;e/m>540&&(e=540*m);
var t=e,i=document.createElement("div");i.style.cssText="width: 10rem; height: 1px";var a=document.body;a.appendChild(i);var r=i.offsetWidth;if(r!=t){var n=parseFloat(o.style.fontSize,10),l=(n*t/r).toFixed(2);
o.style.fontSize=l+"px"}try{a.removeChild(i)}catch(d){}}var r,n=e.document,o=n.documentElement,l=n.querySelector('meta[name="viewport"]'),d=n.querySelector('meta[name="flexible"]'),m=0,s=0,c=t.flexible||(t.flexible={});
if(l){var p=l.getAttribute("content").match(/initial\-scale=([\d\.]+)/);p&&(s=parseFloat(p[1]),m=parseInt(1/s))}else if(d){var u=d.getAttribute("content");if(u){var f=u.match(/initial\-dpr=([\d\.]+)/),h=u.match(/maximum\-dpr=([\d\.]+)/);
f&&(m=parseFloat(f[1]),s=parseFloat((1/m).toFixed(2))),h&&(m=parseFloat(h[1]),s=parseFloat((1/m).toFixed(2)))}}if(!m&&!s){var v=(e.navigator.appVersion.match(/android/gi),e.navigator.appVersion.match(/iphone/gi)),x=e.devicePixelRatio;
v?(m=x>=3&&(!m||m>=3)?3:x>=2&&(!m||m>=2)?2:1,document.querySelector("body").className="hIphone"):(m=1,document.querySelector("body").className="hAndroid"),s=1/m}if(o.setAttribute("data-dpr",m),!l)if(l=n.createElement("meta"),l.setAttribute("name","viewport"),l.setAttribute("content","initial-scale="+s+", maximum-scale="+s+", minimum-scale="+s+", user-scalable=no"),o.firstElementChild)o.firstElementChild.appendChild(l);
else{var y=n.createElement("div");y.appendChild(l),n.write(y.innerHTML)}e.addEventListener("resize",function(){clearTimeout(r),r=setTimeout(i,300)},!1),e.addEventListener("pageshow",function(e){e.persisted&&(clearTimeout(r),r=setTimeout(i,300))
},!1),n.body.style.fontSize="complete"===n.readyState?12*m+"px":12*m+"px",i(),c.dpr=e.dpr=m,c.refreshRem=i,c.rem2px=function(e){var t=parseFloat(e)*this.rem;return"string"==typeof e&&e.match(/rem$/)&&(t+="px"),t
},c.px2rem=function(e){var t=parseFloat(e)/this.rem;return"string"==typeof e&&e.match(/px$/)&&(t+="rem"),t},a()}(window,window.lib||(window.lib={}));
;void function(e,t){for(var n=t.getElementsByTagName("img"),a=+new Date,i=[],o=function(){this.removeEventListener&&this.removeEventListener("load",o,!1),i.push({img:this,time:+new Date})},s=0;s<n.length;s++)!function(){var e=n[s];
e.addEventListener?!e.complete&&e.addEventListener("load",o,!1):e.attachEvent&&e.attachEvent("onreadystatechange",function(){"complete"==e.readyState&&o.call(e,o)})}();alog("speed.set",{fsItems:i,fs:a})
}(window,document);
;void function(e,t,n,a,o,c){function r(t){e.attachEvent?e.attachEvent("onload",t,!1):e.addEventListener&&e.addEventListener("load",t)}function i(e,n,a){a=a||15;var o=new Date;o.setTime((new Date).getTime()+1e3*a),t.cookie=e+"="+escape(n)+";path=/;expires="+o.toGMTString()
}function s(e){var n=t.cookie.match(new RegExp("(^| )"+e+"=([^;]*)(;|$)"));return null!=n?unescape(n[2]):null}function l(){var e=s("PMS_JT");if(e){i("PMS_JT","",-1);try{e=e.match(/{["']s["']:(\d+),["']r["']:["']([\s\S]+)["']}/),e=e&&e[1]&&e[2]?{s:parseInt(e[1]),r:e[2]}:{}
}catch(n){e={}}e.r&&t.referrer.replace(/#.*/,"")!=e.r||alog("speed.set","wt",e.s)}}if(e.alogObjectConfig){var d=e.alogObjectConfig.sample,p=e.alogObjectConfig.rand;a="https:"===e.location.protocol?"https://fex.bdstatic.com"+a:"http://fex.bdstatic.com"+a,d&&p&&p>d||(r(function(){alog("speed.set","lt",+new Date),o=t.createElement(n),o.async=!0,o.src=a+"?v="+~(new Date/864e5)+~(new Date/864e5),c=t.getElementsByTagName(n)[0],c.parentNode.insertBefore(o,c)
}),l())}}(window,document,"script","/hunter/alog/dp.mobile.min.js");
;define("hiloan:app/static/config/def-sup-banks",function(i,m,t){"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=[{bankName:"工商银行",singleLimit:"1000万以上",dayLimit:"1000万以上",monthLimit:"1000万以上"},{bankName:"建设银行",singleLimit:"1000万以上",dayLimit:"1000万以上",monthLimit:"1000万以上"},{bankName:"交通银行",singleLimit:"1000万以上",dayLimit:"1000万以上",monthLimit:"1000万以上"},{bankName:"邮储银行",singleLimit:"不限",dayLimit:"不限",monthLimit:"不限"},{bankName:"招商银行",singleLimit:"1000万以上",dayLimit:"1000万以上",monthLimit:"1000万以上"},{bankName:"中信银行",singleLimit:"不限",dayLimit:"不限",monthLimit:"不限"},{bankName:"华夏银行",singleLimit:"1000万以上",dayLimit:"1000万以上",monthLimit:"不限"},{bankName:"光大银行",singleLimit:"100万",dayLimit:"不限",monthLimit:"不限"},{bankName:"农业银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"中国银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"浦发银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"民生银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"广发银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"平安银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"北京银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"},{bankName:"上海银行",singleLimit:"200万",dayLimit:"不限",monthLimit:"不限"}],t.exports=m["default"]
});
;define("hiloan:app/static/config/form/input",function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});{var n=e("hiloan:components/fin-fg/util/index");n.validator.custom.extend}t.default={},i.exports=t["default"]
});
;define("hiloan:app/static/config/form/select",function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});{var n=e("hiloan:components/fin-fg/util/index");n.validator.custom.extend}t.default={},i.exports=t["default"]
});
;define("hiloan:app/static/config/form/index",function(e,t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0}),t.select=t.input=void 0;{var n=e("hiloan:app/static/config/form/input"),o=i(n),a=e("hiloan:app/static/config/form/select"),c=i(a);
t.input=o.default,t.select=c.default}});
;define("hiloan:app/static/config/notify-native-error/index",function(t,n,e){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(n,"__esModule",{value:!0});var i=t("hiloan:node_modules/underscore/underscore"),a=o(i),r=t("hiloan:components/fin-fg/util/ui/index"),c=t("hiloan:components/fin-fg/util/request/redirect"),l=o(c),d=(t("hiloan:app/static/config/constant"),t("hiloan:app/static/config/env-conf")),f=o(d),s=t("hiloan:app/static/config/front-system-path"),p=o(s),u=f.default.FRONT_SYSTEM+p.default.helpcontent+"?",g=Agent.OS.android,m={contactsCfg:{title:"无法访问通讯录",message:[(g?"获取通信录失败，":"")+"请进入系统【设置】→【隐私】→【通讯录】 →【百度钱包】打开通讯录开关"],android:[(g?"获取通信录失败，":"")+"请设置开启百度钱包的通讯录权限"],jumpUrl:u,extra:{key:"6_1"},ok:g?"取消":"知道了"},locationCfg:{title:"请开启定位功能",message:["<p>请进入系统【设置】→【隐私】→【定位服务】打开开关，并开启【百度钱包】的定位权限</p>",'<p class="notify-error-tip">如果您已开启，请点击重试</p>'],android:["<p>请设置并打开GPS定位服务开关，从而保证贷款顺利进行。</p>",'<p class="notify-error-tip">如果您已开启，请点击重试</p>'],jumpUrl:u,extra:{key:"6_0"},ok:"重试"},cameraCfg:{title:"无法打开摄像头",message:["请进入系统【设置】→【隐私】→【摄像头】→【百度钱包】打开摄像头开关"],android:[(g?"获取摄像头失败，":"")+"请设置开启百度钱包的摄像头权限"],jumpUrl:u,extra:{key:"6_2"},ok:g?"取消":"知道了"}},h={};
window.G.constants.nativeErroConfig&&(h=window.G.constants.nativeErroConfig),a.default.isEmpty(h)||(m=a.default.extend({},m,h));var y=function(t){var n=m[t+"Cfg"],e=g?n.android:n.message,o={type:"alert",head:!1,prefix:"dialog-notify-native notify-native-"+(g?"android":"ios"),content:'\n                <div class="notify-error-'+t+'">\n                    <h4>'+n.title+"</h4>\n                    <p>"+e.join("")+"</p>\n                </div>\n            ",ok:n.ok},i={cancel:"重试",ok:"如何开启"};
g&&(o.type="prompt",o=a.default.extend({},o,i)),r.dialog(o).then(function(t){g?n.jumpUrl&&l.default(n.jumpUrl,n.extra):location.reload(),t.hideUi()}).catch(function(){location.reload()})};n.default={config:m,getContacts:function(){y("contacts")
},position:function(){y("location")},camera:function(){y("camera")}},e.exports=n["default"]});