<h3>
   
</h3>
<div class="filter">
    <form action="{:U(GROUP_NAME.'/Daikuan/index_butongguo')}" method="post">
        <input name="keyword" type="text" class="inpMain" placeholder="用户名" value="{$keyword}" size="20" />
        <input name="sday" id="sday" type="text" placeholder="开始时间" class="inpMain  day1" value="{$sday}" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="16" />

        <input name="eday" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="eday" type="text" placeholder="结束时间" class="inpMain  day1" value="{$eday}" size="16" />
        <input name="submit" class="btnGray" type="submit" value="筛选" />
    </form>
       <div style="font-size:18px;color:#000;border:1px  solid #d9d9d9; background-color:#fff">订单总计：{$sumcont}笔&nbsp;/&nbsp;金额总计：{$sumamount}元&nbsp;/&nbsp;应还金额总计：{$yinghuan_sumamount}元</div>
</div>
<div id="list">
        <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
            <tr>
                <th width="50" align="center">ID</th>
                <th width="120" align="center">订单号</th>
                <th width="80" align="center">用户名</th>
                <th width="80" align="center">姓名</th>
                <th width="70">借款金额</th>
                <th width="70">借款期限</th>
                <th width="70">应还金额</th>
                <th width="150" align="center">创建日期</th>
                <th width="70">邀请人</th>
                <th width="80">状态</th>
                <th align="center">操作</th>
            </tr>
            <volist name="list" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.ordernum}</td>
                    <td align="center">{$vo.user}</td>
                   <td align="center">{$vo.name}</td>
					<td align="center">{$vo.money}元</td>
					<td align="center">{$vo.months}天</td>
                    <td align="center">{$vo.monthmoney}元</td>
                    <td align="center">{$vo.addtime|date='Y-m-d H:i:s',###}</td>
                  <td align="center">{$tuima.tui_ma}</td>
                    <td align="center">
						<php>
							if($vo['status'] == 0)echo "提交成功";
							if($vo['status'] == 1)echo "正在审核订单";
							if($vo['status'] == 2)echo "到帐钱包";
							if($vo['status'] == 3)echo "审核通过";
							if($vo['status'] == 4)echo "审核不通过";
							if($vo['status'] == 5)echo "押金";
							if($vo['status'] == 6)echo "冻结";	
                            if($vo['status'] == 7)echo "保险";	
                            if($vo['status'] == 8)echo "利息";	
							if($vo['status'] == 9)echo "银行卡异常";	
							if($vo['status'] == 10)echo "征信";
							if($vo['status'] == 11)echo "提现成功";							
					 </php>
                    </td>
                    <td align="center">
                    	<a href="javascript:changestatus('{$vo.ordernum}','{$vo.id}');">修改订单状态</a> |
                    	<a href="javascript:showbank('{$vo.bank}','{$vo.banknum}');">查看打款信息</a> |
                        <a href="javascript:del('{$vo.ordernum}','{:U(GROUP_NAME.'/Daikuan/del',array('id'=>$vo['id']))}');">删除订单</a> |
                        <a href="{:U(GROUP_NAME.'/Daikuan/hetong',array('id'=>$vo['id']))}">查看合同</a>                           | <a href="{:U(GROUP_NAME.'/User/userinfo',array('user' => $vo['user']))}">查看资料</a>
                    </td>
                </tr>
            </volist>
        </table>
</div>
<div class="clear"></div>
<div class="pager">
    {$page}
</div>
</div>
<script>
    function del(num,jumpurl){
        layer.confirm(
                '确定要删除借款订单:'+num+'吗?',
                function (){
                    window.location.href = jumpurl;
                }
        );
    }
    function showbank(bankname,banknum){
  		layer.alert(
  			'打款银行:' + bankname + "<br>" + '银行卡号:' + banknum,
  			{
	    		skin: 'layui-layer-lan',
	    		closeBtn: 0,
	    		anim: 4
  			}
  		);
    }
    function changestatus(ordernum,oid){
    	$("#changestatus_span").html(ordernum);
    	$("#orderid").val(oid);
		layer.open({
		  	type: 1,
		  	skin: 'layui-layer-rim', //加上边框
		  	area: ['420px', '165px'], //宽高
		  	content: $("#changestatus_div").html()
		});
    }
    function savestatus(){
    	var id = $("#orderid").val();
    	var status = $('input:radio[name="status"]:checked').val();
    	if(status != 'undefined' && status != '' && status != null){
    		$.post(
    			"{:U(GROUP_NAME.'/Daikuan/changestatus')}",
    			{id:id,status:status},
    			function(data,state){
    				if(state != "success"){
    					layer.msg("请求数据失败!");
    				}else if(data.status == 1){
    					layer.msg("保存成功!");
    					setTimeout(function(){
    						window.location.href = window.location.href;
    					},2000);
    				}else{
    					layer.msg(data.msg);
    				}
    			}
    		);
    	}else{
    		layer.msg("请选择订单状态!");
    	}
    }
</script>
<div style="display: none;" id="changestatus_div">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <td width="100" align="right">订单号</td>
            <td>
                <span id="changestatus_span"></span>
                <input type="hidden" id="orderid" value="" />
            </td>
        </tr>
        <tr>
            <td align="right">状态</td>
            <td>
               <label>
                    <input type="radio" name="status" value="0"  >
					提交成功
                </label>
                </label>
                    <input type="radio" name="status" value="1"  >
					正在审核
                </label>
                </label>
                    <input type="radio" name="status" value="2"  >
					到帐钱包
                </label>
                </label>
                    <input type="radio" name="status" value="3"  >
					审核通过
				<label>
				   <label>
                    <input type="radio" name="status" value="4"  >
					审核不通过
                </label>
                </label>
                    <input type="radio" name="status" value="5"  >
					押金
                </label>
                </label>
                    <input type="radio" name="status" value="6"  >
					冻结
                </label>
                </label>
                    <input type="radio" name="status" value="7"  >
					保险
				<label>
				   <label>
                    <input type="radio" name="status" value="8"  >
					利息
                </label>
                </label>
                    <input type="radio" name="status" value="9"  >
					银行卡异常
                </label>
                </label>
                    <input type="radio" name="status" value="10"  >
					征信
                </label>
                </label>
                    <input type="radio" name="status" value="11"  >
					提现成功
				<label>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <input type="submit" onclick="savestatus();" class="btn" value="提交" />
            </td>
        </tr>
    </table>
</div>