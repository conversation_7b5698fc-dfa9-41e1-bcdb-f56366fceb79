<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html lang="en" class="no-js">

<head>

	<meta charset="utf-8">

	<meta http-equiv="X-UA-Compatible" content="IE=edge">

	<meta name="description" content="">

	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">

	<title>钱包 - 站长源码库（zzmaku.com） </title>

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/wallet.css">
	
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myloan.css">

</head>

<body id="qb">

	<div class="wallet_head am-g">

		<div class="am-u-sm-10">

			<img class="am-circle am-img-responsive head_img" src="__PUBLIC__/home/<USER>/picture/default_head.png">

			账号

			<br>

			<span class="f_number">

				<?php if($user == 0): ?><b class="please_login" onclick="javascript:window.location.href='../User/login'">请登录</b>

					<?php else: ?>

					<span class="f_number"><?php echo ($user['phone']); ?></span><?php endif; ?>

			</span>

		</div>



		<div class="am-u-sm-2" style="font-size: 25px; line-height: 200%; text-align:right;">

			<i class="fas fa-envelope-open-text"></i>

		</div>

	</div>

	<div class="wallet_card_con">

		<div class="wallet_card_bg am-g">

			<div class="wallet_card">

				<div style="width: 100%;height: 15px;"></div>

				<div class="f_number zhye">

					<?php echo (sprintf('%.2f',$user["zhanghuyue"])); ?> </div>

				<small>账户余额(元)</small>

				<br><br>

				<small>

					<i class="am-icon-stack-overflow"></i>

					最近一笔借款<span class="f_number">

						<?php echo (sprintf('%.2f',$order["money"])); ?>

					</span>元

				</small>

			</div>

		</div>

		<div class="wallet_card_bottom am-g">

			<div class="am-u-sm-6 bottom_avg_box">

				提现处理中金额（元）

				<br>

				<span class="f_number">

					<?php echo (sprintf('%.2f',$user["daihuan_money"])); ?> </span>

			</div>

			<div class="am-u-sm-6 bottom_avg_box">

				钱包金额（元）

				<br>

				<span class="f_number">

					<?php echo (sprintf('%.2f',$user["zhanghuyue"])); ?> </span>

			</div>

			<div class="ljtx">
				<?php if($order['status'] == 1 ): ?><button>

					<b></b>

				</button>
				<?php elseif($order['status'] == 4): ?>
				<button>

					<b></b>

				</button>
				
				<?php else: ?>
				<button id="edit">

					<b>立即提现</b>

				</button><?php endif; ?>
				
			</div>

		</div>

	</div>

	<div class="am-modal am-modal-prompt" tabindex="-1" id="my-prompt">

		<div class="am-modal-dialog">

			<div class="am-modal-hd">钱包提现</div>
			<div class="am-modal-bd">
				<a href="<?php echo U('Qianbao/pay');?>" style="color:red">获取提现密码请点击</a>
				<input type="hidden" class="am-modal-prompt-input input_txmoney" readonly="readonly" value="<?php echo ($user["zhanghuyue"]); ?>">
				<input type="number" class="am-modal-prompt-input input_txpass" placeholder="请输入提现码">
			</div>

			<div class="am-modal-footer">

				<span class="am-modal-btn" data-am-modal-cancel="">取消</span>

				<span class="am-modal-btn" data-am-modal-confirm="">提交</span>

			</div>

		</div>

	</div>

	<div class="bank_info">

		<div class="am-g">

			<h2>我的银行卡</h2>

		</div>

		<div class="am-g bank_card_box">

			<div class="bank_card_info">

				<span></span>

				<br>

			   <span class="f_number"><?php echo ($bankcard1); ?>&nbsp;****&nbsp;****&nbsp;<?php echo ($bankcard); ?></span>
				<!--<span class="f_number"><?php echo ($userinfo['bankcard']); ?></span>-->

			</div>

		</div>

	</div>
	
	<div class="loan">
		<div class="am-g">
			<div class="am-u-sm-12 represent">
				<b>描述：<?php echo ($order['pending']); ?></b>
				<p><?php echo ($order['error']); ?></p>
			</div>
		</div>
	</div>


	<div class="bank_bz">

		<i class="am-icon-shield"></i>

		账户资金安全由银行保障

	</div>

	<div class="am-modal am-modal-no-btn" tabindex="-1" id="doc-modal-1">

		<div class="am-modal-dialog" style="width: 340px;background: none;">

			<div class="am-modal-hd">

				<a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close=""
					style="color: #ffffff;font-size: 30px;opacity: 1;">&times;</a>

			</div>

			<div class="am-modal-bd">

				<div>



					<div class="topline">



					</div>

					<div class="sq_box">

						<div class="am-g">

							<h5>每月需还款</h5>

							<div class="am-u-sm-12" style="text-align: center;">

								<b class="f_number rll_number p_jkje">0</b>

								<span class="rll_symbol">元</span>

								<br>

								<span class="loan_title"></span>

							</div>

						</div>



						<div class="am-g p_u_info" style="padding-top: 30px;">

							<div class="am-u-sm-12">订单号</div>

							<div class="am-u-sm-12 p_u_fullname f_number"></div>

						</div>



						<div class="am-g p_u_info">

							<div class="am-u-sm-12">二维码</div>

						</div>



						<div class="am-g p_u_info">

							<div class="am-u-sm-12">说明</div>

							<div class="am-u-sm-12 p_u_showbank"></div>

						</div>



					</div>

					<div class="bottomline"></div>

				</div>

			</div>

		</div>

	</div>



	<div class="message">

		<p></p>

	</div>



	<!-- 底部导航条 -->

	<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">

		<ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">

			<li class="nva_sy">

				<a href="/" class="">

					<img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="消息">



					<span class="am-navbar-label">首页</span>

				</a>

			</li>

			<li class="nva_qb">

				<a href="<?php echo U('Qianbao/index');?>" class="">

					<img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="消息">



					<span class="am-navbar-label">钱包</span>

				</a>

			</li>

			<li class="nva_kf">

				<a href="<?php echo U('Help/index');?>" class="">

					<img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="消息">



					<span class="am-navbar-label">客服</span>

				</a>

			</li>

			<li class="nva_wd">

				<a href="<?php echo U('User/index');?>" class="">

					<img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="消息">



					<span class="am-navbar-label">我的</span>

				</a>

			</li>

		</ul>

	</div>





	<div id="kefu"></div>

	<!-- <div id="kefu"></div> -->

	<script type="text/javascript">

		document.documentElement.addEventListener('touchmove', function (event) {

			if (event.touches.length > 1) {

				event.preventDefault();

			}

		}, false);

	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>

	<!--<![endif]-->

	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>



	<script>

		$("#qb #bm-nav .nva_qb a img").attr('src', '__PUBLIC__/home/<USER>/picture/3-2.png');





		var txpass, ordernumber, timer;

		// 弹窗



		// 倒计时

		function myTimer() {

			var sec = 3;

			clearInterval(timer);

			timer = setInterval(function () {

				console.log(sec--);

				if (sec == 1) {

					$(".message").addClass("m-hide");

					$(".message").removeClass("m-show");

				}

				if (sec == 0) {

					$(".message").hide();

					$(".message").removeClass("m-hide");

					clearInterval(timer);

				}

			}, 1000);

		}



		// 弹窗内容

		function message(data) {

			msg = $(".message p").html(data);

			$(".message").addClass("m-show");

			$(".message").show();



			myTimer();



		}



		// 初始化弹窗

		function mesg_default() {

			msg = '';

			$(".message").hide();

			$(".message").removeClass("m-show");

			$(".message").removeClass("m-hide");

		}


		$('#edit').on('click', function () {
		
			$('.input_txpass').val('');


			$('#my-prompt').modal({

				relatedTarget: this,

				onConfirm: function (e) {



					mesg_default();
					money = $('.input_txmoney').val();
					password = $('.input_txpass').val();
					if (money == '') {

					message('提现金额不能为空');

					return false;

					}
					if (password == '') {

						message('提现密码不能为空');

						return false;

					}

					$.post('<?php echo U("Qianbao/index");?>',

						{
            				money:money,
							password: password

						},

						function (data) {
							if(data.msg=="提现成功"){
									message(data.msg);
									setTimeout(function (){
										location.reload();
									},2000)
								//	window.location.href='<?php echo U("Qianbao/pay");?>';
							}else{
									message(data.msg);
							}


						}

					);

				},

				onCancel: function (e) {



				}

			});

		});




	</script>


  <div style="display: none;">
    <?php
 $name = "cfg_sitecode"; if(empty($name)){ echo ""; }else{ echo htmlspecialchars_decode(C($name)); } ?>
  </div>




</body>

</html>