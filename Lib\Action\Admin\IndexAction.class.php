<?php
class IndexAction extends CommonAction {
	
	public function index(){
		if(!$this->islogin()){
			$this->redirect(GROUP_NAME.'/Index/login');
		}else{
			$this->redirect(GROUP_NAME.'/Main/index');
		}
	}
	
	public function login(){
		$this->title="登录系统";
		if(IS_POST){
			
			$data = array('status' => 0,'msg' => '未知错误');
			$Admin = D("admin");
			$username = I('username','','trim');
			//print_r($username);
			$password = I('password');
			//$password = $this->getpass($password);
			$tmp = $Admin->where(array('username' => $username,'password' => md5($password)))
						 ->find();
			//$sql= $Admin->getlastsql();echo $sql;exit;
			if($tmp){
				if($tmp['status']){
					//写入登录记录
					$Admin_login = D("admin_login");
					$Admin_login->add(array(
						'username'  => $username,
						'logintime' => time(),
						'loginip'	=> get_client_ip()
					));
					//更新最近登录时间
					$this->setlogin($username);
                    $this->setlogin1($tmp['id']);
					$Admin->where(array('username' => $username))
						  ->save(array('lastlogin' => time() ));	
					$data['status'] = 1;
					
				}else{
					$data['msg'] = "该账户已被禁止登录!";
				}
			}else{
				$data['msg'] = "帐户名或密码错误";
			}

			
			$this->ajaxReturn($data);
			exit;
		}

		$this->display();
		
	}

	public function logout(){
		$this->title="注销登录";
		$this->setlogin('');
        $this->setlogin1('');
		$this->redirect(U(GROUP_NAME.'/Index/agreement'));
	}
	
  	public function changevip()
	{
		$id = I('id');
		if (!$id) {
			$this->error('参数有误');
		}
		$vip = I('edu');
		if (!$vip) {
			$this->error('请输入用户会员等级');
		}
		$userModel = D('User');
		$r = $userModel->where(array('id' => $id))->save(array('vip' => $vip));
		if (!$r) {
			$this->error('修改失败');
		}
		$this->success('修改成功');
	}

	
}