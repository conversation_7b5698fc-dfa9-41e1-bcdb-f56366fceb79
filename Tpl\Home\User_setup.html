<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="">
  <meta name="keywords" content="">
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
  <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
<title>设置 - 站长源码库（zzmaku.com） </title>
<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/setup.css">
</head>
<body>
	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('User/index')}'"><i class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">设置</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>

	<div class="setup_box">
		<div class="setup_menu">
			<div class="am-g setup_menu_list" id="edit">
				<div class="am-u-sm-6">修改登录密码</div>
				<div class="am-u-sm-6"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</div>
		</div>
		<div class="setup_menu">
			<!--
			<div class="am-g setup_menu_list">
				<div class="am-u-sm-6">关于我们</div>
				<div class="am-u-sm-6"><i class="am-icon-angle-right am-icon-fw"></i></div>
			</div>
			-->
			<div class="am-g setup_menu_list">
				<div class="am-u-sm-6">软件版本</div>
				<div class="am-u-sm-6"><span class="f_number">1.0.0</span><i class="am-icon-angle-right am-icon-fw"></i></div>
			</div>
		</div>
		<div class="setup_menu">
			<div class="am-g setup_menu_list center theme_color">
				<div class="am-u-sm-12" onclick="javascript:window.location.href='{:U('User/logout')}'">退出登录</div>
			</div>
		</div>
	</div>


	<div class="am-modal am-modal-prompt" tabindex="-1" id="my-prompt">
		<div class="am-modal-dialog">
			<div class="am-modal-hd"><Somnus:sitecfg name="sitetitle"/></div>
			<div class="am-modal-bd">
				请输入新密码
				<input type="password" class="am-modal-prompt-input input_password">
			</div>
			<div class="am-modal-footer">
				<span class="am-modal-btn" data-am-modal-cancel="">取消</span>
				<span class="am-modal-btn" data-am-modal-confirm="">提交</span>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>

<script type="text/javascript">
    document.documentElement.addEventListener('touchmove', function(event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    }, false);
</script>
<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
<!--<![endif]-->
<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

<script>
	var timer,msg,
		password
	;





	// 弹窗

	// 倒计时
	function myTimer(){
		var sec = 3;
		clearInterval(timer);
		timer = setInterval(function() {
			console.log(sec--);
			if(sec == 1){
				$(".message").addClass("m-hide");
				$(".message").removeClass("m-show");
			}
			if (sec == 0) {
				$(".message").hide();
				$(".message").removeClass("m-hide");
				clearInterval(timer);
			}
		} , 1000);
	}

	// 弹窗内容
	function message(data){
		msg = $(".message p").html(data);
		$(".message").addClass("m-show");
		$(".message").show();

		myTimer();

	}

	// 初始化弹窗
	function mesg_default(){
		msg = '';
		$(".message").hide();
		$(".message").removeClass("m-show");
		$(".message").removeClass("m-hide");
	}

	$('#edit').on('click', function() {
		$('.input_password').val('');
		$('#my-prompt').modal({
			relatedTarget: this,
			onConfirm: function(e) {
				
				mesg_default();
				password  = $('.input_password').val();
				if(password == '' || password.length<6){
					message('密码不能为空或小于6位');
					return false;
				}
				$.post('{:U("User/setup")}',
					{
						password:password
					},
					function (data) {
						message(data.msg);
					}
				);
			},
			onCancel: function(e) {

			}
		});
	});

</script>


  <div style="display: none;">
    <Somnus:sitecfg name="sitecode" />
  </div>


</body>
</html>