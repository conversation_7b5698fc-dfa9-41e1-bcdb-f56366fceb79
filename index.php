<html>
<head>

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no, email=no, address=none">
    <meta http-equiv="Expires" content="-1">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <link rel="icon" href="data:;base64,=">


    <title>首页 - 站长源码库（zzmaku.com） </title>

    <script async="" src="index_files/feature.min.js"></script>
    <script async="" src="index_files/element.min.js"></script>
    <script async="" src="index_files/monkey.mobile.min.js"></script>
    <script async="" src="index_files/speed.min.js"></script>
    <script async="" src="index_files/dp.mobile.min.js"></script>
    <script async="" src="index_files/dp.mobile.min.js"></script>
    <script async="" src="index_files/alog.mobile.min.js"></script>
    <script async="" src="index_files/alog.mobile.min.js"></script>
    <script src="index_files/statistic.min.js"></script>
    <script src="index_files/hm.js"></script>
    <link href="index_files/bootstrap.css" rel="stylesheet" type="text/css"/>
    <link href="index_files/home.css" rel="stylesheet" type="text/css"/>
    <link href="index_files//font_6yxmrwgmg7kl0udi.css" rel="stylesheet" type="text/css"/>

    <meta name="viewport" content="initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
</head>



<body class="hAndroid" style="font-size: 12px;"><input type="hidden" id="env" value="online">



<input type="hidden" id="is-from-back" value="1">
<link rel="stylesheet" type="text/css" href="index_files/fin_00bcb89.css">
<link rel="stylesheet" type="text/css" href="index_files/app_f1b2afd.css">
<link rel="stylesheet" type="text/css" href="index_files/userGuide_e42d6e0.css">




<input type="hidden" id="supportAppVersion" value="">
<input type="hidden" id="isWallet" value="">
<div class="wrapper">
    <div class="body">


        <aside class="fin-ui-loading zoom-center-transition" style="display: none;">
            <i class="icon-loading loading"></i>
            <span>正在加载...</span>
        </aside>
        <div class="fin-ui-mask fade-transition" style="background-color: rgba(0, 0, 0, 0); display: none; touch-action: manipulation; user-select: none; -webkit-user-drag: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
        </div>
        <section class="page-1c3rvkijq-userGuide">
            <section id="entrySeam" class="entry-seam">
                <div class="title"></div>
                <div class="input-box">
                    <div class="input-wrap">
                        <div class="input-info">
                            <h3>最高借款额度(元)</h3>
                            <h1>300,000</h1>
                            <h4>10000元借36期，每期最低还款<span class="gold">335</span>元</h4>
                        </div>
                        <div class="login">
                            <div onclick="location.href='/'" class="access-btn">我要申请</div>
                        </div>

                    </div>
                </div>
                <div class="approve-tip">额度和费用以最终审批结果为准</div>
                <div class="step-info"></div>
                <div class="qa-info">
                    <div class="qa-title"></div>
                    <div class="qa-card">
                        <div class="question"><span class="gold">Q</span>贷款的申请条件是什么？</div>
                        <div class="answer"><span class="gold">A</span>年龄在18-55岁的中国公民，均可申请。</div>
                    </div>
                    <div class="qa-card">
                        <div class="question"><span class="gold">Q</span>我需要准备哪些资料？</div>
                        <div class="answer"><span class="gold">A</span>本人身份证、银行卡(仅用于收款)、手机号码。</div>
                    </div>
					
                </div>

                <div class="bottom-btn fade-transition" style="display: none;">
                    我要申请</div>
            </section>

        </section>
    </div>
    <script type="text/javascript">'use strict';

    void function(e,t){for(var n=t.getElementsByTagName("img"),a=+new Date,i=[],o=function(){this.removeEventListener&&this.removeEventListener("load",o,!1),i.push({img:this,time:+new Date})},s=0;s<n.length;s++)!function(){var e=n[s];
        e.addEventListener?!e.complete&&e.addEventListener("load",o,!1):e.attachEvent&&e.attachEvent("onreadystatechange",function(){"complete"==e.readyState&&o.call(e,o)})}();alog("speed.set",{fsItems:i,fs:a})
    }(window,document);;</script>
    <script>'use strict';

    void function(e,t,n,a,o,c){function r(t){e.attachEvent?e.attachEvent("onload",t,!1):e.addEventListener&&e.addEventListener("load",t)}function i(e,n,a){a=a||15;var o=new Date;o.setTime((new Date).getTime()+1e3*a),t.cookie=e+"="+escape(n)+";path=/;expires="+o.toGMTString()
    }function s(e){var n=t.cookie.match(new RegExp("(^| )"+e+"=([^;]*)(;|$)"));return null!=n?unescape(n[2]):null}function l(){var e=s("PMS_JT");if(e){i("PMS_JT","",-1);try{e=e.match(/{["']s["']:(\d+),["']r["']:["']([\s\S]+)["']}/),e=e&&e[1]&&e[2]?{s:parseInt(e[1]),r:e[2]}:{}
    }catch(n){e={}}e.r&&t.referrer.replace(/#.*/,"")!=e.r||alog("speed.set","wt",e.s)}}if(e.alogObjectConfig){var d=e.alogObjectConfig.sample,p=e.alogObjectConfig.rand;a="https:"===e.location.protocol?"https://fex.bdstatic.com"+a:"http://fex.bdstatic.com"+a,d&&p&&p>d||(r(function(){alog("speed.set","lt",+new Date),o=t.createElement(n),o.async=!0,o.src=a+"?v="+~(new Date/864e5)+~(new Date/864e5),c=t.getElementsByTagName(n)[0],c.parentNode.insertBefore(o,c)
    }),l())}}(window,document,"script","/hunter/alog/dp.mobile.min.js");;
    alog('speed.set', 'drt', +new Date());</script>
</div>
<script src="index_files/agent.min.js"></script>
<script type="text/javascript" src="index_files/mod_9c0c8d0.js"></script>
<script type="text/javascript" src="index_files/node_modules_7d993e5.js"></script>
<script type="text/javascript" src="index_files/fin_6eeb30b.js"></script>
<script type="text/javascript" src="index_files/app_f104677.js"></script>
<script type="text/javascript" src="index_files/userGuide_6ee82cb.js"></script>
<script type="text/javascript">!function(){'use strict';

    define('page/userGuide', function () {
        return JSON.parse('{\x22isInWhiteList\x22:0,\x22creditStatus\x22:0,\x22allowInWhite\x22:1,\x22activityName\x22:\x22hiloan_jrmall\x22}');
    });

    require('hiloan:app/page/apply/userGuide/userGuide');}();
!function(){'use strict';

    var _log = require('hiloan:components/fin-fg/track/log');

    var _log2 = _interopRequireDefault(_log);

    function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

    _log2.default.init();
    document.body.addEventListener('touchstart', function () {});}();
!function(){'use strict';

    (function () {
        // 标记页面已经访问过了
        var $isFromBack = document.querySelector('#is-from-back');
        if ($isFromBack) {
            $isFromBack.value = '1';
        }
    })();}();</script><div></div><script>"use strict";</script>






<!--footer-->

<div class="footer">
    <div class="container">
        <div class="row">
            <div class="col-xs-3">
                <a href="/vip" class="dao" style="color:#246fc0;">
                    <i class="iconfont icon-shouye-copy-copy-copy"></i>
                    <span class="nav_ti" style="color:#246fc0;">首页</span>
                </a>
            </div>
            <div class="col-xs-3">
                <a href="/index.php?m=Qianbao&a=index" class="dao">
                    <i class="iconfont icon-zhangdan"></i>
                    <span class="nav_ti">钱包</span>
                </a>
            </div>
            <div class="col-xs-3">
            	
                <a href="{:C('cfg_kefu_link')}" class="dao">
                    <i class="iconfont icon-qianbao-"></i>
                    <span class="nav_ti">客服</span>
                </a>
            </div>
            <div class="col-xs-3">
                <a href="/index.php?m=User&a=index" class="dao">
                    <i class="iconfont icon-information" ></i>
                    <span class="nav_ti" >个人中心</span>
                </a>
            </div>
        </div>
    </div>
</div>


</body>
</html>