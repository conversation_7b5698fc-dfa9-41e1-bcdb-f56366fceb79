;define("hiloan:node_modules/process/browser",function(e,t,n){function r(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function i(e){if(f===setTimeout)return setTimeout(e,0);
if((f===r||!f)&&setTimeout)return f=setTimeout,setTimeout(e,0);try{return f(e,0)}catch(t){try{return f.call(null,e,0)}catch(t){return f.call(this,e,0)}}}function u(e){if(h===clearTimeout)return clearTimeout(e);
if((h===o||!h)&&clearTimeout)return h=clearTimeout,clearTimeout(e);try{return h(e)}catch(t){try{return h.call(null,e)}catch(t){return h.call(this,e)}}}function c(){T&&p&&(T=!1,p.length?d=p.concat(d):g=-1,d.length&&s())
}function s(){if(!T){var e=i(c);T=!0;for(var t=d.length;t;){for(p=d,d=[];++g<t;)p&&p[g].run();g=-1,t=d.length}p=null,T=!1,u(e)}}function a(e,t){this.fun=e,this.array=t}function l(){}var f,h,m=n.exports={};
!function(){try{f="function"==typeof setTimeout?setTimeout:r}catch(e){f=r}try{h="function"==typeof clearTimeout?clearTimeout:o}catch(e){h=o}}();var p,d=[],T=!1,g=-1;m.nextTick=function(e){var t=new Array(arguments.length-1);
if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];d.push(new a(e,t)),1!==d.length||T||i(s)},a.prototype.run=function(){this.fun.apply(null,this.array)},m.title="browser",m.browser=!0,m.env={},m.argv=[],m.version="",m.versions={},m.on=l,m.addListener=l,m.once=l,m.off=l,m.removeListener=l,m.removeAllListeners=l,m.emit=l,m.prependListener=l,m.prependOnceListener=l,m.listeners=function(){return[]
},m.binding=function(){throw new Error("process.binding is not supported")},m.cwd=function(){return"/"},m.chdir=function(){throw new Error("process.chdir is not supported")},m.umask=function(){return 0
}});
;define("hiloan:node_modules/hammerjs/hammer",function(t,e,n){t("hiloan:node_modules/process/browser");!function(t,e,i,r){"use strict";function s(t,e,n){return setTimeout(c(t,n),e)}function o(t,e,n){return Array.isArray(t)?(a(t,n[e],n),!0):!1
}function a(t,e,n){var i;if(t)if(t.forEach)t.forEach(e,n);else if(t.length!==r)for(i=0;i<t.length;)e.call(n,t[i],i,t),i++;else for(i in t)t.hasOwnProperty(i)&&e.call(n,t[i],i,t)}function u(e,n,i){var r="DEPRECATED METHOD: "+n+"\n"+i+" AT \n";
return function(){var n=new Error("get-stack-trace"),i=n&&n.stack?n.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",s=t.console&&(t.console.warn||t.console.log);
return s&&s.call(t.console,r,i),e.apply(this,arguments)}}function h(t,e,n){var i,r=e.prototype;i=t.prototype=Object.create(r),i.constructor=t,i._super=r,n&&pe(i,n)}function c(t,e){return function(){return t.apply(e,arguments)
}}function l(t,e){return typeof t==ve?t.apply(e?e[0]||r:r,e):t}function p(t,e){return t===r?e:t}function f(t,e,n){a(g(e),function(e){t.addEventListener(e,n,!1)})}function d(t,e,n){a(g(e),function(e){t.removeEventListener(e,n,!1)
})}function v(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function m(t,e){return t.indexOf(e)>-1}function g(t){return t.trim().split(/\s+/g)}function T(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);
for(var i=0;i<t.length;){if(n&&t[i][n]==e||!n&&t[i]===e)return i;i++}return-1}function y(t){return Array.prototype.slice.call(t,0)}function E(t,e,n){for(var i=[],r=[],s=0;s<t.length;){var o=e?t[s][e]:t[s];
T(r,o)<0&&i.push(t[s]),r[s]=o,s++}return n&&(i=e?i.sort(function(t,n){return t[e]>n[e]}):i.sort()),i}function I(t,e){for(var n,i,s=e[0].toUpperCase()+e.slice(1),o=0;o<fe.length;){if(n=fe[o],i=n?n+s:e,i in t)return i;
o++}return r}function A(){return Ie++}function _(e){var n=e.ownerDocument||e;return n.defaultView||n.parentWindow||t}function b(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){l(t.options.enable,[t])&&n.handler(e)
},this.init()}function C(t){var e,n=t.options.inputClass;return new(e=n?n:be?W:Ce?H:_e?U:F)(t,S)}function S(t,e,n){var i=n.pointers.length,r=n.changedPointers.length,s=e&Oe&&i-r===0,o=e&(Me|ze)&&i-r===0;
n.isFirst=!!s,n.isFinal=!!o,s&&(t.session={}),n.eventType=e,P(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function P(t,e){var n=t.session,i=e.pointers,r=i.length;n.firstInput||(n.firstInput=w(e)),r>1&&!n.firstMultiple?n.firstMultiple=w(e):1===r&&(n.firstMultiple=!1);
var s=n.firstInput,o=n.firstMultiple,a=o?o.center:s.center,u=e.center=O(i);e.timeStamp=Te(),e.deltaTime=e.timeStamp-s.timeStamp,e.angle=N(a,u),e.distance=z(a,u),D(n,e),e.offsetDirection=M(e.deltaX,e.deltaY);
var h=R(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=ge(h.x)>ge(h.y)?h.x:h.y,e.scale=o?Y(o.pointers,i):1,e.rotation=o?X(o.pointers,i):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,x(n,e);
var c=t.element;v(e.srcEvent.target,c)&&(c=e.srcEvent.target),e.target=c}function D(t,e){var n=e.center,i=t.offsetDelta||{},r=t.prevDelta||{},s=t.prevInput||{};(e.eventType===Oe||s.eventType===Me)&&(r=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},i=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=r.x+(n.x-i.x),e.deltaY=r.y+(n.y-i.y)
}function x(t,e){var n,i,s,o,a=t.lastInterval||e,u=e.timeStamp-a.timeStamp;if(e.eventType!=ze&&(u>we||a.velocity===r)){var h=e.deltaX-a.deltaX,c=e.deltaY-a.deltaY,l=R(u,h,c);i=l.x,s=l.y,n=ge(l.x)>ge(l.y)?l.x:l.y,o=M(h,c),t.lastInterval=e
}else n=a.velocity,i=a.velocityX,s=a.velocityY,o=a.direction;e.velocity=n,e.velocityX=i,e.velocityY=s,e.direction=o}function w(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:me(t.pointers[n].clientX),clientY:me(t.pointers[n].clientY)},n++;
return{timeStamp:Te(),pointers:e,center:O(e),deltaX:t.deltaX,deltaY:t.deltaY}}function O(t){var e=t.length;if(1===e)return{x:me(t[0].clientX),y:me(t[0].clientY)};for(var n=0,i=0,r=0;e>r;)n+=t[r].clientX,i+=t[r].clientY,r++;
return{x:me(n/e),y:me(i/e)}}function R(t,e,n){return{x:e/t||0,y:n/t||0}}function M(t,e){return t===e?Ne:ge(t)>=ge(e)?0>t?Xe:Ye:0>e?Fe:We}function z(t,e,n){n||(n=Le);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];
return Math.sqrt(i*i+r*r)}function N(t,e,n){n||(n=Le);var i=e[n[0]]-t[n[0]],r=e[n[1]]-t[n[1]];return 180*Math.atan2(r,i)/Math.PI}function X(t,e){return N(e[1],e[0],Ue)+N(t[1],t[0],Ue)}function Y(t,e){return z(e[0],e[1],Ue)/z(t[0],t[1],Ue)
}function F(){this.evEl=je,this.evWin=Ge,this.pressed=!1,b.apply(this,arguments)}function W(){this.evEl=$e,this.evWin=Je,b.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}function q(){this.evTarget=Qe,this.evWin=tn,this.started=!1,b.apply(this,arguments)
}function k(t,e){var n=y(t.touches),i=y(t.changedTouches);return e&(Me|ze)&&(n=E(n.concat(i),"identifier",!0)),[n,i]}function H(){this.evTarget=nn,this.targetIds={},b.apply(this,arguments)}function L(t,e){var n=y(t.touches),i=this.targetIds;
if(e&(Oe|Re)&&1===n.length)return i[n[0].identifier]=!0,[n,n];var r,s,o=y(t.changedTouches),a=[],u=this.target;if(s=n.filter(function(t){return v(t.target,u)}),e===Oe)for(r=0;r<s.length;)i[s[r].identifier]=!0,r++;
for(r=0;r<o.length;)i[o[r].identifier]&&a.push(o[r]),e&(Me|ze)&&delete i[o[r].identifier],r++;return a.length?[E(s.concat(a),"identifier",!0),a]:void 0}function U(){b.apply(this,arguments);var t=c(this.handler,this);
this.touch=new H(this.manager,t),this.mouse=new F(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function V(t,e){t&Oe?(this.primaryTouch=e.changedPointers[0].identifier,j.call(this,e)):t&(Me|ze)&&j.call(this,e)
}function j(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY};this.lastTouches.push(n);var i=this.lastTouches,r=function(){var t=i.indexOf(n);t>-1&&i.splice(t,1)
};setTimeout(r,rn)}}function G(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,i=0;i<this.lastTouches.length;i++){var r=this.lastTouches[i],s=Math.abs(e-r.x),o=Math.abs(n-r.y);if(sn>=s&&sn>=o)return!0
}return!1}function Z(t,e){this.manager=t,this.set(e)}function B(t){if(m(t,ln))return ln;var e=m(t,pn),n=m(t,fn);return e&&n?ln:e||n?e?pn:fn:m(t,cn)?cn:hn}function $(){if(!an)return!1;var e={},n=t.CSS&&t.CSS.supports;
return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){e[i]=n?t.CSS.supports("touch-action",i):!0}),e}function J(t){this.options=pe({},this.defaults,t||{}),this.id=A(),this.manager=null,this.options.enable=p(this.options.enable,!0),this.state=vn,this.simultaneous={},this.requireFail=[]
}function K(t){return t&En?"cancel":t&Tn?"end":t&gn?"move":t&mn?"start":""}function Q(t){return t==We?"down":t==Fe?"up":t==Xe?"left":t==Ye?"right":""}function te(t,e){var n=e.manager;return n?n.get(t):t
}function ee(){J.apply(this,arguments)}function ne(){ee.apply(this,arguments),this.pX=null,this.pY=null}function ie(){ee.apply(this,arguments)}function re(){J.apply(this,arguments),this._timer=null,this._input=null
}function se(){ee.apply(this,arguments)}function oe(){ee.apply(this,arguments)}function ae(){J.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function ue(t,e){return e=e||{},e.recognizers=p(e.recognizers,ue.defaults.preset),new he(t,e)
}function he(t,e){this.options=pe({},ue.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=C(this),this.touchAction=new Z(this,this.options.touchAction),ce(this,!0),a(this.options.recognizers,function(t){var e=this.add(new t[0](t[1]));
t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}function ce(t,e){var n=t.element;if(n.style){var i;a(t.options.cssProps,function(r,s){i=I(n.style,s),e?(t.oldCssProps[i]=n.style[i],n.style[i]=r):n.style[i]=t.oldCssProps[i]||""
}),e||(t.oldCssProps={})}}function le(t,n){var i=e.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=n,n.target.dispatchEvent(i)}var pe,fe=["","webkit","Moz","MS","ms","o"],de=e.createElement("div"),ve="function",me=Math.round,ge=Math.abs,Te=Date.now;
pe="function"!=typeof Object.assign?function(t){if(t===r||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var i=arguments[n];if(i!==r&&null!==i)for(var s in i)i.hasOwnProperty(s)&&(e[s]=i[s])
}return e}:Object.assign;var ye=u(function(t,e,n){for(var i=Object.keys(e),s=0;s<i.length;)(!n||n&&t[i[s]]===r)&&(t[i[s]]=e[i[s]]),s++;return t},"extend","Use `assign`."),Ee=u(function(t,e){return ye(t,e,!0)
},"merge","Use `assign`."),Ie=1,Ae=/mobile|tablet|ip(ad|hone|od)|android/i,_e="ontouchstart"in t,be=I(t,"PointerEvent")!==r,Ce=_e&&Ae.test(navigator.userAgent),Se="touch",Pe="pen",De="mouse",xe="kinect",we=25,Oe=1,Re=2,Me=4,ze=8,Ne=1,Xe=2,Ye=4,Fe=8,We=16,qe=Xe|Ye,ke=Fe|We,He=qe|ke,Le=["x","y"],Ue=["clientX","clientY"];
b.prototype={handler:function(){},init:function(){this.evEl&&f(this.element,this.evEl,this.domHandler),this.evTarget&&f(this.target,this.evTarget,this.domHandler),this.evWin&&f(_(this.element),this.evWin,this.domHandler)
},destroy:function(){this.evEl&&d(this.element,this.evEl,this.domHandler),this.evTarget&&d(this.target,this.evTarget,this.domHandler),this.evWin&&d(_(this.element),this.evWin,this.domHandler)}};var Ve={mousedown:Oe,mousemove:Re,mouseup:Me},je="mousedown",Ge="mousemove mouseup";
h(F,b,{handler:function(t){var e=Ve[t.type];e&Oe&&0===t.button&&(this.pressed=!0),e&Re&&1!==t.which&&(e=Me),this.pressed&&(e&Me&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:De,srcEvent:t}))
}});var Ze={pointerdown:Oe,pointermove:Re,pointerup:Me,pointercancel:ze,pointerout:ze},Be={2:Se,3:Pe,4:De,5:xe},$e="pointerdown",Je="pointermove pointerup pointercancel";t.MSPointerEvent&&!t.PointerEvent&&($e="MSPointerDown",Je="MSPointerMove MSPointerUp MSPointerCancel"),h(W,b,{handler:function(t){var e=this.store,n=!1,i=t.type.toLowerCase().replace("ms",""),r=Ze[i],s=Be[t.pointerType]||t.pointerType,o=s==Se,a=T(e,t.pointerId,"pointerId");
r&Oe&&(0===t.button||o)?0>a&&(e.push(t),a=e.length-1):r&(Me|ze)&&(n=!0),0>a||(e[a]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:s,srcEvent:t}),n&&e.splice(a,1))}});var Ke={touchstart:Oe,touchmove:Re,touchend:Me,touchcancel:ze},Qe="touchstart",tn="touchstart touchmove touchend touchcancel";
h(q,b,{handler:function(t){var e=Ke[t.type];if(e===Oe&&(this.started=!0),this.started){var n=k.call(this,t,e);e&(Me|ze)&&n[0].length-n[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:Se,srcEvent:t})
}}});var en={touchstart:Oe,touchmove:Re,touchend:Me,touchcancel:ze},nn="touchstart touchmove touchend touchcancel";h(H,b,{handler:function(t){var e=en[t.type],n=L.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:Se,srcEvent:t})
}});var rn=2500,sn=25;h(U,b,{handler:function(t,e,n){var i=n.pointerType==Se,r=n.pointerType==De;if(!(r&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)V.call(this,e,n);else if(r&&G.call(this,n))return;
this.callback(t,e,n)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var on=I(de.style,"touchAction"),an=on!==r,un="compute",hn="auto",cn="manipulation",ln="none",pn="pan-x",fn="pan-y",dn=$();
Z.prototype={set:function(t){t==un&&(t=this.compute()),an&&this.manager.element.style&&dn[t]&&(this.manager.element.style[on]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)
},compute:function(){var t=[];return a(this.manager.recognizers,function(e){l(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),B(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,n=t.offsetDirection;
if(this.manager.session.prevented)return void e.preventDefault();var i=this.actions,r=m(i,ln)&&!dn[ln],s=m(i,fn)&&!dn[fn],o=m(i,pn)&&!dn[pn];if(r){var a=1===t.pointers.length,u=t.distance<2,h=t.deltaTime<250;
if(a&&u&&h)return}return o&&s?void 0:r||s&&n&qe||o&&n&ke?this.preventSrc(e):void 0},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var vn=1,mn=2,gn=4,Tn=8,yn=Tn,En=16,In=32;
J.prototype={defaults:{},set:function(t){return pe(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(o(t,"recognizeWith",this))return this;var e=this.simultaneous;
return t=te(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return o(t,"dropRecognizeWith",this)?this:(t=te(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(o(t,"requireFailure",this))return this;
var e=this.requireFail;return t=te(t,this),-1===T(e,t)&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(o(t,"dropRequireFailure",this))return this;t=te(t,this);var e=T(this.requireFail,t);
return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){function e(e){n.manager.emit(e,t)
}var n=this,i=this.state;Tn>i&&e(n.options.event+K(i)),e(n.options.event),t.additionalEvent&&e(t.additionalEvent),i>=Tn&&e(n.options.event+K(i))},tryEmit:function(t){return this.canEmit()?this.emit(t):void(this.state=In)
},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(In|vn)))return!1;t++}return!0},recognize:function(t){var e=pe({},t);return l(this.options.enable,[this,e])?(this.state&(yn|En|In)&&(this.state=vn),this.state=this.process(e),void(this.state&(mn|gn|Tn|En)&&this.tryEmit(e))):(this.reset(),void(this.state=In))
},process:function(){},getTouchAction:function(){},reset:function(){}},h(ee,J,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,i=e&(mn|gn),r=this.attrTest(t);
return i&&(n&ze||!r)?e|En:i||r?n&Me?e|Tn:e&mn?e|gn:mn:In}}),h(ne,ee,{defaults:{event:"pan",threshold:10,pointers:1,direction:He},getTouchAction:function(){var t=this.options.direction,e=[];return t&qe&&e.push(fn),t&ke&&e.push(pn),e
},directionTest:function(t){var e=this.options,n=!0,i=t.distance,r=t.direction,s=t.deltaX,o=t.deltaY;return r&e.direction||(e.direction&qe?(r=0===s?Ne:0>s?Xe:Ye,n=s!=this.pX,i=Math.abs(t.deltaX)):(r=0===o?Ne:0>o?Fe:We,n=o!=this.pY,i=Math.abs(t.deltaY))),t.direction=r,n&&i>e.threshold&&r&e.direction
},attrTest:function(t){return ee.prototype.attrTest.call(this,t)&&(this.state&mn||!(this.state&mn)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Q(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)
}}),h(ie,ee,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[ln]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&mn)
},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),h(re,J,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[hn]
},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime>e.time;if(this._input=t,!i||!n||t.eventType&(Me|ze)&&!r)this.reset();else if(t.eventType&Oe)this.reset(),this._timer=s(function(){this.state=yn,this.tryEmit()
},e.time,this);else if(t.eventType&Me)return yn;return In},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===yn&&(t&&t.eventType&Me?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Te(),this.manager.emit(this.options.event,this._input)))
}}),h(se,ee,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[ln]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&mn)
}}),h(oe,ee,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:qe|ke,pointers:1},getTouchAction:function(){return ne.prototype.getTouchAction.call(this)},attrTest:function(t){var e,n=this.options.direction;
return n&(qe|ke)?e=t.overallVelocity:n&qe?e=t.overallVelocityX:n&ke&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&n&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&ge(e)>this.options.velocity&&t.eventType&Me
},emit:function(t){var e=Q(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),h(ae,J,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[cn]
},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,i=t.distance<e.threshold,r=t.deltaTime<e.time;if(this.reset(),t.eventType&Oe&&0===this.count)return this.failTimeout();if(i&&r&&n){if(t.eventType!=Me)return this.failTimeout();
var o=this.pTime?t.timeStamp-this.pTime<e.interval:!0,a=!this.pCenter||z(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,a&&o?this.count+=1:this.count=1,this._input=t;
var u=this.count%e.taps;if(0===u)return this.hasRequireFailures()?(this._timer=s(function(){this.state=yn,this.tryEmit()},e.interval,this),mn):yn}return In},failTimeout:function(){return this._timer=s(function(){this.state=In
},this.options.interval,this),In},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==yn&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),ue.VERSION="2.0.7",ue.defaults={domEvents:!1,touchAction:un,enable:!0,inputTarget:null,inputClass:null,preset:[[se,{enable:!1}],[ie,{enable:!1},["rotate"]],[oe,{direction:qe}],[ne,{direction:qe},["swipe"]],[ae],[ae,{event:"doubletap",taps:2},["tap"]],[re]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};
var An=1,_n=2;he.prototype={set:function(t){return pe(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this
},stop:function(t){this.session.stopped=t?_n:An},recognize:function(t){var e=this.session;if(!e.stopped){this.touchAction.preventDefaults(t);var n,i=this.recognizers,r=e.curRecognizer;(!r||r&&r.state&yn)&&(r=e.curRecognizer=null);
for(var s=0;s<i.length;)n=i[s],e.stopped===_n||r&&n!=r&&!n.canRecognizeWith(r)?n.reset():n.recognize(t),!r&&n.state&(mn|gn|Tn)&&(r=e.curRecognizer=n),s++}},get:function(t){if(t instanceof J)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];
return null},add:function(t){if(o(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(o(t,"remove",this))return this;
if(t=this.get(t)){var e=this.recognizers,n=T(e,t);-1!==n&&(e.splice(n,1),this.touchAction.update())}return this},on:function(t,e){if(t!==r&&e!==r){var n=this.handlers;return a(g(t),function(t){n[t]=n[t]||[],n[t].push(e)
}),this}},off:function(t,e){if(t!==r){var n=this.handlers;return a(g(t),function(t){e?n[t]&&n[t].splice(T(n[t],e),1):delete n[t]}),this}},emit:function(t,e){this.options.domEvents&&le(t,e);var n=this.handlers[t]&&this.handlers[t].slice();
if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var i=0;i<n.length;)n[i](e),i++}},destroy:function(){this.element&&ce(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null
}},pe(ue,{INPUT_START:Oe,INPUT_MOVE:Re,INPUT_END:Me,INPUT_CANCEL:ze,STATE_POSSIBLE:vn,STATE_BEGAN:mn,STATE_CHANGED:gn,STATE_ENDED:Tn,STATE_RECOGNIZED:yn,STATE_CANCELLED:En,STATE_FAILED:In,DIRECTION_NONE:Ne,DIRECTION_LEFT:Xe,DIRECTION_RIGHT:Ye,DIRECTION_UP:Fe,DIRECTION_DOWN:We,DIRECTION_HORIZONTAL:qe,DIRECTION_VERTICAL:ke,DIRECTION_ALL:He,Manager:he,Input:b,TouchAction:Z,TouchInput:H,MouseInput:F,PointerEventInput:W,TouchMouseInput:U,SingleTouchInput:q,Recognizer:J,AttrRecognizer:ee,Tap:ae,Pan:ne,Swipe:oe,Pinch:ie,Rotate:se,Press:re,on:f,off:d,each:a,merge:Ee,extend:ye,assign:pe,inherit:h,bindFn:c,prefixed:I});
var bn="undefined"!=typeof t?t:"undefined"!=typeof self?self:{};bn.Hammer=ue,"function"==typeof define&&define.amd?define(function(){return ue}):"undefined"!=typeof n&&n.exports?n.exports=ue:t[i]=ue}(window,document,"Hammer")
});
;define("hiloan:node_modules/vue-touch/vue-touch",function(e,t,i){!function(){function n(e){return e.charAt(0).toUpperCase()+e.slice(1)}function r(e){var t=e.direction;if("string"==typeof t){var i="DIRECTION_"+t.toUpperCase();
s.indexOf(t)>-1&&a.hasOwnProperty(i)?e.direction=a[i]:console.warn("[vue-touch] invalid direction: "+t)}}var o={},a="function"==typeof e?e("hiloan:node_modules/hammerjs/hammer"):window.Hammer,h=["tap","pan","pinch","press","rotate","swipe"],s=["up","down","left","right","horizontal","vertical","all"],c={};
if(!a)throw new Error("[vue-touch] cannot locate Hammer.js.");o.config={},o.install=function(e){e.directive("touch",{isFn:!0,acceptStatement:!0,priority:e.directive("on").priority,bind:function(){this.el.hammer||(this.el.hammer=new a.Manager(this.el));
var e=this.mc=this.el.hammer,t=this.arg;t||console.warn("[vue-touch] event type argument is required.");var i,s;if(c[t]){var u=c[t];i=u.type,s=new(a[n(i)])(u),s.recognizeWith(e.recognizers),e.add(s)}else{for(var d=0;d<h.length;d++)if(0===t.indexOf(h[d])){i=h[d];
break}if(!i)return void console.warn("[vue-touch] invalid event type: "+t);s=e.get(i),s||(s=new(a[n(i)]),s.recognizeWith(e.recognizers),e.add(s));var l=o.config[i];l&&(r(l),s.set(l));var f=this.el.hammerOptions&&this.el.hammerOptions[i];
f&&(r(f),s.set(f))}this.recognizer=s},update:function(e){var t=this.mc,i=this.arg;this.handler&&t.off(i,this.handler),"function"!=typeof e?(this.handler=null,console.warn("[vue-touch] invalid handler function for v-touch: "+this.arg+'="'+this.descriptor.raw)):t.on(i,this.handler=e)
},unbind:function(){this.handler&&this.mc.off(this.arg,this.handler),Object.keys(this.mc.handlers).length||(this.mc.destroy(),this.el.hammer=null)}}),e.directive("touch-options",{priority:e.directive("on").priority+1,update:function(e){var t=this.el.hammerOptions||(this.el.hammerOptions={});
this.arg?t[this.arg]=e:console.warn("[vue-touch] recognizer type argument for v-touch-options is required.")}})},o.registerCustomEvent=function(e,t){t.event=e,c[e]=t},"object"==typeof t?i.exports=o:"function"==typeof define&&define.amd?define([],function(){return o
}):window.Vue&&(window.VueTouch=o,Vue.use(o))}()});
;define("hiloan:node_modules/underscore/underscore",function(n,r,t){(function(){function n(n){function r(r,t,e,u,i,o){for(;i>=0&&o>i;i+=n){var a=u?u[i]:i;e=t(e,r[a],a,r)}return e}return function(t,e,u,i){e=j(e,i,4);
var o=!S(t)&&_.keys(t),a=(o||t).length,c=n>0?0:a-1;return arguments.length<3&&(u=t[o?o[c]:c],c+=n),r(t,e,u,o,c,a)}}function e(n){return function(r,t,e){t=x(t,e);for(var u=F(r),i=n>0?0:u-1;i>=0&&u>i;i+=n)if(t(r[i],i,r))return i;
return-1}}function u(n,r,t){return function(e,u,i){var o=0,a=F(e);if("number"==typeof i)n>0?o=i>=0?i:Math.max(i+a,o):a=i>=0?Math.min(i+1,a):i+a+1;else if(t&&i&&a)return i=t(e,u),e[i]===u?i:-1;if(u!==u)return i=r(p.call(e,o,a),_.isNaN),i>=0?i+o:-1;
for(i=n>0?o:a-1;i>=0&&a>i;i+=n)if(e[i]===u)return i;return-1}}function i(n,r){var t=B.length,e=n.constructor,u=_.isFunction(e)&&e.prototype||f,i="constructor";for(_.has(n,i)&&!_.contains(r,i)&&r.push(i);t--;)i=B[t],i in n&&n[i]!==u[i]&&!_.contains(r,i)&&r.push(i)
}var o=this,a=o._,c=Array.prototype,f=Object.prototype,l=Function.prototype,s=c.push,p=c.slice,h=f.toString,v=f.hasOwnProperty,g=Array.isArray,y=Object.keys,d=l.bind,m=Object.create,b=function(){},_=function(n){return n instanceof _?n:this instanceof _?void(this._wrapped=n):new _(n)
};"undefined"!=typeof r?("undefined"!=typeof t&&t.exports&&(r=t.exports=_),r._=_):o._=_,_.VERSION="1.8.3";var j=function(n,r,t){if(void 0===r)return n;switch(null==t?3:t){case 1:return function(t){return n.call(r,t)
};case 2:return function(t,e){return n.call(r,t,e)};case 3:return function(t,e,u){return n.call(r,t,e,u)};case 4:return function(t,e,u,i){return n.call(r,t,e,u,i)}}return function(){return n.apply(r,arguments)
}},x=function(n,r,t){return null==n?_.identity:_.isFunction(n)?j(n,r,t):_.isObject(n)?_.matcher(n):_.property(n)};_.iteratee=function(n,r){return x(n,r,1/0)};var w=function(n,r){return function(t){var e=arguments.length;
if(2>e||null==t)return t;for(var u=1;e>u;u++)for(var i=arguments[u],o=n(i),a=o.length,c=0;a>c;c++){var f=o[c];r&&void 0!==t[f]||(t[f]=i[f])}return t}},A=function(n){if(!_.isObject(n))return{};if(m)return m(n);
b.prototype=n;var r=new b;return b.prototype=null,r},O=function(n){return function(r){return null==r?void 0:r[n]}},k=Math.pow(2,53)-1,F=O("length"),S=function(n){var r=F(n);return"number"==typeof r&&r>=0&&k>=r
};_.each=_.forEach=function(n,r,t){r=j(r,t);var e,u;if(S(n))for(e=0,u=n.length;u>e;e++)r(n[e],e,n);else{var i=_.keys(n);for(e=0,u=i.length;u>e;e++)r(n[i[e]],i[e],n)}return n},_.map=_.collect=function(n,r,t){r=x(r,t);
for(var e=!S(n)&&_.keys(n),u=(e||n).length,i=Array(u),o=0;u>o;o++){var a=e?e[o]:o;i[o]=r(n[a],a,n)}return i},_.reduce=_.foldl=_.inject=n(1),_.reduceRight=_.foldr=n(-1),_.find=_.detect=function(n,r,t){var e;
return e=S(n)?_.findIndex(n,r,t):_.findKey(n,r,t),void 0!==e&&-1!==e?n[e]:void 0},_.filter=_.select=function(n,r,t){var e=[];return r=x(r,t),_.each(n,function(n,t,u){r(n,t,u)&&e.push(n)}),e},_.reject=function(n,r,t){return _.filter(n,_.negate(x(r)),t)
},_.every=_.all=function(n,r,t){r=x(r,t);for(var e=!S(n)&&_.keys(n),u=(e||n).length,i=0;u>i;i++){var o=e?e[i]:i;if(!r(n[o],o,n))return!1}return!0},_.some=_.any=function(n,r,t){r=x(r,t);for(var e=!S(n)&&_.keys(n),u=(e||n).length,i=0;u>i;i++){var o=e?e[i]:i;
if(r(n[o],o,n))return!0}return!1},_.contains=_.includes=_.include=function(n,r,t,e){return S(n)||(n=_.values(n)),("number"!=typeof t||e)&&(t=0),_.indexOf(n,r,t)>=0},_.invoke=function(n,r){var t=p.call(arguments,2),e=_.isFunction(r);
return _.map(n,function(n){var u=e?r:n[r];return null==u?u:u.apply(n,t)})},_.pluck=function(n,r){return _.map(n,_.property(r))},_.where=function(n,r){return _.filter(n,_.matcher(r))},_.findWhere=function(n,r){return _.find(n,_.matcher(r))
},_.max=function(n,r,t){var e,u,i=-1/0,o=-1/0;if(null==r&&null!=n){n=S(n)?n:_.values(n);for(var a=0,c=n.length;c>a;a++)e=n[a],e>i&&(i=e)}else r=x(r,t),_.each(n,function(n,t,e){u=r(n,t,e),(u>o||u===-1/0&&i===-1/0)&&(i=n,o=u)
});return i},_.min=function(n,r,t){var e,u,i=1/0,o=1/0;if(null==r&&null!=n){n=S(n)?n:_.values(n);for(var a=0,c=n.length;c>a;a++)e=n[a],i>e&&(i=e)}else r=x(r,t),_.each(n,function(n,t,e){u=r(n,t,e),(o>u||1/0===u&&1/0===i)&&(i=n,o=u)
});return i},_.shuffle=function(n){for(var r,t=S(n)?n:_.values(n),e=t.length,u=Array(e),i=0;e>i;i++)r=_.random(0,i),r!==i&&(u[i]=u[r]),u[r]=t[i];return u},_.sample=function(n,r,t){return null==r||t?(S(n)||(n=_.values(n)),n[_.random(n.length-1)]):_.shuffle(n).slice(0,Math.max(0,r))
},_.sortBy=function(n,r,t){return r=x(r,t),_.pluck(_.map(n,function(n,t,e){return{value:n,index:t,criteria:r(n,t,e)}}).sort(function(n,r){var t=n.criteria,e=r.criteria;if(t!==e){if(t>e||void 0===t)return 1;
if(e>t||void 0===e)return-1}return n.index-r.index}),"value")};var E=function(n){return function(r,t,e){var u={};return t=x(t,e),_.each(r,function(e,i){var o=t(e,i,r);n(u,e,o)}),u}};_.groupBy=E(function(n,r,t){_.has(n,t)?n[t].push(r):n[t]=[r]
}),_.indexBy=E(function(n,r,t){n[t]=r}),_.countBy=E(function(n,r,t){_.has(n,t)?n[t]++:n[t]=1}),_.toArray=function(n){return n?_.isArray(n)?p.call(n):S(n)?_.map(n,_.identity):_.values(n):[]},_.size=function(n){return null==n?0:S(n)?n.length:_.keys(n).length
},_.partition=function(n,r,t){r=x(r,t);var e=[],u=[];return _.each(n,function(n,t,i){(r(n,t,i)?e:u).push(n)}),[e,u]},_.first=_.head=_.take=function(n,r,t){return null==n?void 0:null==r||t?n[0]:_.initial(n,n.length-r)
},_.initial=function(n,r,t){return p.call(n,0,Math.max(0,n.length-(null==r||t?1:r)))},_.last=function(n,r,t){return null==n?void 0:null==r||t?n[n.length-1]:_.rest(n,Math.max(0,n.length-r))},_.rest=_.tail=_.drop=function(n,r,t){return p.call(n,null==r||t?1:r)
},_.compact=function(n){return _.filter(n,_.identity)};var M=function(n,r,t,e){for(var u=[],i=0,o=e||0,a=F(n);a>o;o++){var c=n[o];if(S(c)&&(_.isArray(c)||_.isArguments(c))){r||(c=M(c,r,t));var f=0,l=c.length;
for(u.length+=l;l>f;)u[i++]=c[f++]}else t||(u[i++]=c)}return u};_.flatten=function(n,r){return M(n,r,!1)},_.without=function(n){return _.difference(n,p.call(arguments,1))},_.uniq=_.unique=function(n,r,t,e){_.isBoolean(r)||(e=t,t=r,r=!1),null!=t&&(t=x(t,e));
for(var u=[],i=[],o=0,a=F(n);a>o;o++){var c=n[o],f=t?t(c,o,n):c;r?(o&&i===f||u.push(c),i=f):t?_.contains(i,f)||(i.push(f),u.push(c)):_.contains(u,c)||u.push(c)}return u},_.union=function(){return _.uniq(M(arguments,!0,!0))
},_.intersection=function(n){for(var r=[],t=arguments.length,e=0,u=F(n);u>e;e++){var i=n[e];if(!_.contains(r,i)){for(var o=1;t>o&&_.contains(arguments[o],i);o++);o===t&&r.push(i)}}return r},_.difference=function(n){var r=M(arguments,!0,!0,1);
return _.filter(n,function(n){return!_.contains(r,n)})},_.zip=function(){return _.unzip(arguments)},_.unzip=function(n){for(var r=n&&_.max(n,F).length||0,t=Array(r),e=0;r>e;e++)t[e]=_.pluck(n,e);return t
},_.object=function(n,r){for(var t={},e=0,u=F(n);u>e;e++)r?t[n[e]]=r[e]:t[n[e][0]]=n[e][1];return t},_.findIndex=e(1),_.findLastIndex=e(-1),_.sortedIndex=function(n,r,t,e){t=x(t,e,1);for(var u=t(r),i=0,o=F(n);o>i;){var a=Math.floor((i+o)/2);
t(n[a])<u?i=a+1:o=a}return i},_.indexOf=u(1,_.findIndex,_.sortedIndex),_.lastIndexOf=u(-1,_.findLastIndex),_.range=function(n,r,t){null==r&&(r=n||0,n=0),t=t||1;for(var e=Math.max(Math.ceil((r-n)/t),0),u=Array(e),i=0;e>i;i++,n+=t)u[i]=n;
return u};var I=function(n,r,t,e,u){if(!(e instanceof r))return n.apply(t,u);var i=A(n.prototype),o=n.apply(i,u);return _.isObject(o)?o:i};_.bind=function(n,r){if(d&&n.bind===d)return d.apply(n,p.call(arguments,1));
if(!_.isFunction(n))throw new TypeError("Bind must be called on a function");var t=p.call(arguments,2),e=function(){return I(n,e,r,this,t.concat(p.call(arguments)))};return e},_.partial=function(n){var r=p.call(arguments,1),t=function(){for(var e=0,u=r.length,i=Array(u),o=0;u>o;o++)i[o]=r[o]===_?arguments[e++]:r[o];
for(;e<arguments.length;)i.push(arguments[e++]);return I(n,t,this,this,i)};return t},_.bindAll=function(n){var r,t,e=arguments.length;if(1>=e)throw new Error("bindAll must be passed function names");for(r=1;e>r;r++)t=arguments[r],n[t]=_.bind(n[t],n);
return n},_.memoize=function(n,r){var t=function(e){var u=t.cache,i=""+(r?r.apply(this,arguments):e);return _.has(u,i)||(u[i]=n.apply(this,arguments)),u[i]};return t.cache={},t},_.delay=function(n,r){var t=p.call(arguments,2);
return setTimeout(function(){return n.apply(null,t)},r)},_.defer=_.partial(_.delay,_,1),_.throttle=function(n,r,t){var e,u,i,o=null,a=0;t||(t={});var c=function(){a=t.leading===!1?0:_.now(),o=null,i=n.apply(e,u),o||(e=u=null)
};return function(){var f=_.now();a||t.leading!==!1||(a=f);var l=r-(f-a);return e=this,u=arguments,0>=l||l>r?(o&&(clearTimeout(o),o=null),a=f,i=n.apply(e,u),o||(e=u=null)):o||t.trailing===!1||(o=setTimeout(c,l)),i
}},_.debounce=function(n,r,t){var e,u,i,o,a,c=function(){var f=_.now()-o;r>f&&f>=0?e=setTimeout(c,r-f):(e=null,t||(a=n.apply(i,u),e||(i=u=null)))};return function(){i=this,u=arguments,o=_.now();var f=t&&!e;
return e||(e=setTimeout(c,r)),f&&(a=n.apply(i,u),i=u=null),a}},_.wrap=function(n,r){return _.partial(r,n)},_.negate=function(n){return function(){return!n.apply(this,arguments)}},_.compose=function(){var n=arguments,r=n.length-1;
return function(){for(var t=r,e=n[r].apply(this,arguments);t--;)e=n[t].call(this,e);return e}},_.after=function(n,r){return function(){return--n<1?r.apply(this,arguments):void 0}},_.before=function(n,r){var t;
return function(){return--n>0&&(t=r.apply(this,arguments)),1>=n&&(r=null),t}},_.once=_.partial(_.before,2);var N=!{toString:null}.propertyIsEnumerable("toString"),B=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"];
_.keys=function(n){if(!_.isObject(n))return[];if(y)return y(n);var r=[];for(var t in n)_.has(n,t)&&r.push(t);return N&&i(n,r),r},_.allKeys=function(n){if(!_.isObject(n))return[];var r=[];for(var t in n)r.push(t);
return N&&i(n,r),r},_.values=function(n){for(var r=_.keys(n),t=r.length,e=Array(t),u=0;t>u;u++)e[u]=n[r[u]];return e},_.mapObject=function(n,r,t){r=x(r,t);for(var e,u=_.keys(n),i=u.length,o={},a=0;i>a;a++)e=u[a],o[e]=r(n[e],e,n);
return o},_.pairs=function(n){for(var r=_.keys(n),t=r.length,e=Array(t),u=0;t>u;u++)e[u]=[r[u],n[r[u]]];return e},_.invert=function(n){for(var r={},t=_.keys(n),e=0,u=t.length;u>e;e++)r[n[t[e]]]=t[e];return r
},_.functions=_.methods=function(n){var r=[];for(var t in n)_.isFunction(n[t])&&r.push(t);return r.sort()},_.extend=w(_.allKeys),_.extendOwn=_.assign=w(_.keys),_.findKey=function(n,r,t){r=x(r,t);for(var e,u=_.keys(n),i=0,o=u.length;o>i;i++)if(e=u[i],r(n[e],e,n))return e
},_.pick=function(n,r,t){var e,u,i={},o=n;if(null==o)return i;_.isFunction(r)?(u=_.allKeys(o),e=j(r,t)):(u=M(arguments,!1,!1,1),e=function(n,r,t){return r in t},o=Object(o));for(var a=0,c=u.length;c>a;a++){var f=u[a],l=o[f];
e(l,f,o)&&(i[f]=l)}return i},_.omit=function(n,r,t){if(_.isFunction(r))r=_.negate(r);else{var e=_.map(M(arguments,!1,!1,1),String);r=function(n,r){return!_.contains(e,r)}}return _.pick(n,r,t)},_.defaults=w(_.allKeys,!0),_.create=function(n,r){var t=A(n);
return r&&_.extendOwn(t,r),t},_.clone=function(n){return _.isObject(n)?_.isArray(n)?n.slice():_.extend({},n):n},_.tap=function(n,r){return r(n),n},_.isMatch=function(n,r){var t=_.keys(r),e=t.length;if(null==n)return!e;
for(var u=Object(n),i=0;e>i;i++){var o=t[i];if(r[o]!==u[o]||!(o in u))return!1}return!0};var T=function(n,r,t,e){if(n===r)return 0!==n||1/n===1/r;if(null==n||null==r)return n===r;n instanceof _&&(n=n._wrapped),r instanceof _&&(r=r._wrapped);
var u=h.call(n);if(u!==h.call(r))return!1;switch(u){case"[object RegExp]":case"[object String]":return""+n==""+r;case"[object Number]":return+n!==+n?+r!==+r:0===+n?1/+n===1/r:+n===+r;case"[object Date]":case"[object Boolean]":return+n===+r
}var i="[object Array]"===u;if(!i){if("object"!=typeof n||"object"!=typeof r)return!1;var o=n.constructor,a=r.constructor;if(o!==a&&!(_.isFunction(o)&&o instanceof o&&_.isFunction(a)&&a instanceof a)&&"constructor"in n&&"constructor"in r)return!1
}t=t||[],e=e||[];for(var c=t.length;c--;)if(t[c]===n)return e[c]===r;if(t.push(n),e.push(r),i){if(c=n.length,c!==r.length)return!1;for(;c--;)if(!T(n[c],r[c],t,e))return!1}else{var f,l=_.keys(n);if(c=l.length,_.keys(r).length!==c)return!1;
for(;c--;)if(f=l[c],!_.has(r,f)||!T(n[f],r[f],t,e))return!1}return t.pop(),e.pop(),!0};_.isEqual=function(n,r){return T(n,r)},_.isEmpty=function(n){return null==n?!0:S(n)&&(_.isArray(n)||_.isString(n)||_.isArguments(n))?0===n.length:0===_.keys(n).length
},_.isElement=function(n){return!(!n||1!==n.nodeType)},_.isArray=g||function(n){return"[object Array]"===h.call(n)},_.isObject=function(n){var r=typeof n;return"function"===r||"object"===r&&!!n},_.each(["Arguments","Function","String","Number","Date","RegExp","Error"],function(n){_["is"+n]=function(r){return h.call(r)==="[object "+n+"]"
}}),_.isArguments(arguments)||(_.isArguments=function(n){return _.has(n,"callee")}),"function"!=typeof/./&&"object"!=typeof Int8Array&&(_.isFunction=function(n){return"function"==typeof n||!1}),_.isFinite=function(n){return isFinite(n)&&!isNaN(parseFloat(n))
},_.isNaN=function(n){return _.isNumber(n)&&n!==+n},_.isBoolean=function(n){return n===!0||n===!1||"[object Boolean]"===h.call(n)},_.isNull=function(n){return null===n},_.isUndefined=function(n){return void 0===n
},_.has=function(n,r){return null!=n&&v.call(n,r)},_.noConflict=function(){return o._=a,this},_.identity=function(n){return n},_.constant=function(n){return function(){return n}},_.noop=function(){},_.property=O,_.propertyOf=function(n){return null==n?function(){}:function(r){return n[r]
}},_.matcher=_.matches=function(n){return n=_.extendOwn({},n),function(r){return _.isMatch(r,n)}},_.times=function(n,r,t){var e=Array(Math.max(0,n));r=j(r,t,1);for(var u=0;n>u;u++)e[u]=r(u);return e},_.random=function(n,r){return null==r&&(r=n,n=0),n+Math.floor(Math.random()*(r-n+1))
},_.now=Date.now||function(){return(new Date).getTime()};var R={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},q=_.invert(R),K=function(n){var r=function(r){return n[r]},t="(?:"+_.keys(n).join("|")+")",e=RegExp(t),u=RegExp(t,"g");
return function(n){return n=null==n?"":""+n,e.test(n)?n.replace(u,r):n}};_.escape=K(R),_.unescape=K(q),_.result=function(n,r,t){var e=null==n?void 0:n[r];return void 0===e&&(e=t),_.isFunction(e)?e.call(n):e
};var z=0;_.uniqueId=function(n){var r=++z+"";return n?n+r:r},_.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var D=/(.)^/,L={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},P=/\\|'|\r|\n|\u2028|\u2029/g,C=function(n){return"\\"+L[n]
};_.template=function(n,r,t){!r&&t&&(r=t),r=_.defaults({},r,_.templateSettings);var e=RegExp([(r.escape||D).source,(r.interpolate||D).source,(r.evaluate||D).source].join("|")+"|$","g"),u=0,i="__p+='";n.replace(e,function(r,t,e,o,a){return i+=n.slice(u,a).replace(P,C),u=a+r.length,t?i+="'+\n((__t=("+t+"))==null?'':_.escape(__t))+\n'":e?i+="'+\n((__t=("+e+"))==null?'':__t)+\n'":o&&(i+="';\n"+o+"\n__p+='"),r
}),i+="';\n",r.variable||(i="with(obj||{}){\n"+i+"}\n"),i="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+i+"return __p;\n";try{var o=new Function(r.variable||"obj","_",i)
}catch(a){throw a.source=i,a}var c=function(n){return o.call(this,n,_)},f=r.variable||"obj";return c.source="function("+f+"){\n"+i+"}",c},_.chain=function(n){var r=_(n);return r._chain=!0,r};var J=function(n,r){return n._chain?_(r).chain():r
};_.mixin=function(n){_.each(_.functions(n),function(r){var t=_[r]=n[r];_.prototype[r]=function(){var n=[this._wrapped];return s.apply(n,arguments),J(this,t.apply(_,n))}})},_.mixin(_),_.each(["pop","push","reverse","shift","sort","splice","unshift"],function(n){var r=c[n];
_.prototype[n]=function(){var t=this._wrapped;return r.apply(t,arguments),"shift"!==n&&"splice"!==n||0!==t.length||delete t[0],J(this,t)}}),_.each(["concat","join","slice"],function(n){var r=c[n];_.prototype[n]=function(){return J(this,r.apply(this._wrapped,arguments))
}}),_.prototype.value=function(){return this._wrapped},_.prototype.valueOf=_.prototype.toJSON=_.prototype.value,_.prototype.toString=function(){return""+this._wrapped},"function"==typeof define&&define.amd&&define("underscore",[],function(){return _
})}).call(this)});
;define("hiloan:node_modules/strict-uri-encode/index",function(e,n,t){"use strict";t.exports=function(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()
})}});
;define("hiloan:node_modules/object-assign/index",function(e,r,t){"use strict";function n(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)
}function o(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var r={},t=0;10>t;t++)r["_"+String.fromCharCode(t)]=t;var n=Object.getOwnPropertyNames(r).map(function(e){return r[e]
});if("0123456789"!==n.join(""))return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach(function(e){o[e]=e}),"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},o)).join("")?!1:!0}catch(a){return!1
}}var a=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;t.exports=o()?Object.assign:function(e){for(var r,t,o=n(e),s=1;s<arguments.length;s++){r=Object(arguments[s]);
for(var u in r)i.call(r,u)&&(o[u]=r[u]);if(a){t=a(r);for(var f=0;f<t.length;f++)c.call(r,t[f])&&(o[t[f]]=r[t[f]])}}return o}});
;define("hiloan:node_modules/query-string/index",function(n,r){"use strict";function e(n){switch(n.arrayFormat){case"index":return function(r,e,t){return null===e?[o(r,n),"[",t,"]"].join(""):[o(r,n),"[",o(t,n),"]=",o(e,n)].join("")
};case"bracket":return function(r,e){return null===e?o(r,n):[o(r,n),"[]=",o(e,n)].join("")};default:return function(r,e){return null===e?o(r,n):[o(r,n),"=",o(e,n)].join("")}}}function t(n){var r;switch(n.arrayFormat){case"index":return function(n,e,t){return r=/\[(\d*)\]$/.exec(n),n=n.replace(/\[\d*\]$/,""),r?(void 0===t[n]&&(t[n]={}),void(t[n][r[1]]=e)):void(t[n]=e)
};case"bracket":return function(n,e,t){return r=/(\[\])$/.exec(n),n=n.replace(/\[\]$/,""),r?void 0===t[n]?void(t[n]=[e]):void(t[n]=[].concat(t[n],e)):void(t[n]=e)};default:return function(n,r,e){return void 0===e[n]?void(e[n]=r):void(e[n]=[].concat(e[n],r))
}}}function o(n,r){return r.encode?r.strict?u(n):encodeURIComponent(n):n}function i(n){return Array.isArray(n)?n.sort():"object"==typeof n?i(Object.keys(n)).sort(function(n,r){return Number(n)-Number(r)
}).map(function(r){return n[r]}):n}var u=n("hiloan:node_modules/strict-uri-encode/index"),c=n("hiloan:node_modules/object-assign/index");r.extract=function(n){return n.split("?")[1]||""},r.parse=function(n,r){r=c({arrayFormat:"none"},r);
var e=t(r),o=Object.create(null);return"string"!=typeof n?o:(n=n.trim().replace(/^(\?|#|&)/,""))?(n.split("&").forEach(function(n){var r=n.replace(/\+/g," ").split("="),t=r.shift(),i=r.length>0?r.join("="):void 0;
i=void 0===i?null:decodeURIComponent(i),e(decodeURIComponent(t),i,o)}),Object.keys(o).sort().reduce(function(n,r){var e=o[r];return n[r]=Boolean(e)&&"object"==typeof e&&!Array.isArray(e)?i(e):e,n},Object.create(null))):o
},r.stringify=function(n,r){var t={encode:!0,strict:!0,arrayFormat:"none"};r=c(t,r);var i=e(r);return n?Object.keys(n).sort().map(function(e){var t=n[e];if(void 0===t)return"";if(null===t)return o(e,r);
if(Array.isArray(t)){var u=[];return t.slice().forEach(function(n){void 0!==n&&u.push(i(e,n,u.length))}),u.join("&")}return o(e,r)+"="+o(t,r)}).filter(function(n){return n.length>0}).join("&"):""}});
;define("hiloan:node_modules/vue/dist/vue.common",function(t,e,i){"use strict";function n(t,e,i){if(s(t,e))return void(t[e]=i);if(t._isVue)return void n(t._data,e,i);var r=t.__ob__;if(!r)return void(t[e]=i);
if(r.convert(e,i),r.dep.notify(),r.vms)for(var o=r.vms.length;o--;){var a=r.vms[o];a._proxy(e),a._digest()}return i}function r(t,e){if(s(t,e)){delete t[e];var i=t.__ob__;if(!i)return void(t._isVue&&(delete t._data[e],t._digest()));
if(i.dep.notify(),i.vms)for(var n=i.vms.length;n--;){var r=i.vms[n];r._unproxy(e),r._digest()}}}function s(t,e){return zn.call(t,e)}function o(t){return Un.test(t)}function a(t){var e=(t+"").charCodeAt(0);
return 36===e||95===e}function h(t){return null==t?"":t.toString()}function l(t){if("string"!=typeof t)return t;var e=Number(t);return isNaN(e)?t:e}function c(t){return"true"===t?!0:"false"===t?!1:t}function u(t){var e=t.charCodeAt(0),i=t.charCodeAt(t.length-1);
return e!==i||34!==e&&39!==e?t:t.slice(1,-1)}function f(t){return t.replace(Jn,p)}function p(t,e){return e?e.toUpperCase():""}function d(t){return t.replace(qn,"$1-$2").replace(qn,"$1-$2").toLowerCase()
}function v(t){return t.replace(Qn,p)}function m(t,e){return function(i){var n=arguments.length;return n?n>1?t.apply(e,arguments):t.call(e,i):t.call(e)}}function g(t,e){e=e||0;for(var i=t.length-e,n=new Array(i);i--;)n[i]=t[i+e];
return n}function _(t,e){for(var i=Object.keys(e),n=i.length;n--;)t[i[n]]=e[i[n]];return t}function y(t){return null!==t&&"object"==typeof t}function b(t){return Gn.call(t)===Zn}function w(t,e,i,n){Object.defineProperty(t,e,{value:i,enumerable:!!n,writable:!0,configurable:!0})
}function C(t,e){var i,n,r,s,o,a=function h(){var a=Date.now()-s;e>a&&a>=0?i=setTimeout(h,e-a):(i=null,o=t.apply(r,n),i||(r=n=null))};return function(){return r=this,n=arguments,s=Date.now(),i||(i=setTimeout(a,e)),o
}}function $(t,e){for(var i=t.length;i--;)if(t[i]===e)return i;return-1}function k(t){var e=function i(){return i.cancelled?void 0:t.apply(this,arguments)};return e.cancel=function(){e.cancelled=!0},e}function x(t,e){return t==e||(y(t)&&y(e)?JSON.stringify(t)===JSON.stringify(e):!1)
}function A(t){return/native code/.test(t.toString())}function O(t){this.size=0,this.limit=t,this.head=this.tail=void 0,this._keymap=Object.create(null)}function T(){return vr.charCodeAt(_r+1)}function N(){return vr.charCodeAt(++_r)
}function j(){return _r>=gr}function E(){for(;T()===Er;)N()}function S(t){return t===Or||t===Tr}function F(t){return Sr[t]}function D(t,e){return Fr[t]===e}function P(){for(var t,e=N();!j();)if(t=N(),t===jr)N();
else if(t===e)break}function R(t){for(var e=0,i=t;!j();)if(t=T(),S(t))P();else if(i===t&&e++,D(i,t)&&e--,N(),0===e)break}function L(){for(var t=_r;!j();)if(yr=T(),S(yr))P();else if(F(yr))R(yr);else if(yr===Nr){if(N(),yr=T(),yr!==Nr){(br===$r||br===Ar)&&(br=kr);
break}N()}else{if(yr===Er&&(br===xr||br===Ar)){E();break}br===kr&&(br=xr),N()}return vr.slice(t+1,_r)||null}function H(){for(var t=[];!j();)t.push(I());return t}function I(){var t,e={};return br=kr,e.name=L().trim(),br=Ar,t=M(),t.length&&(e.args=t),e
}function M(){for(var t=[];!j()&&br!==kr;){var e=L();if(!e)break;t.push(W(e))}return t}function W(t){if(Cr.test(t))return{value:l(t),dynamic:!1};var e=u(t),i=e===t;return{value:i?t:e,dynamic:i}}function B(t){var e=wr.get(t);
if(e)return e;vr=t,mr={},gr=vr.length,_r=-1,yr="",br=$r;var i;return vr.indexOf("|")<0?mr.expression=vr.trim():(mr.expression=L().trim(),i=H(),i.length&&(mr.filters=i)),wr.put(t,mr),mr}function V(t){return t.replace(Pr,"\\$&")
}function z(){var t=V(Vr.delimiters[0]),e=V(Vr.delimiters[1]),i=V(Vr.unsafeDelimiters[0]),n=V(Vr.unsafeDelimiters[1]);Lr=new RegExp(i+"((?:.|\\n)+?)"+n+"|"+t+"((?:.|\\n)+?)"+e,"g"),Hr=new RegExp("^"+i+"((?:.|\\n)+?)"+n+"$"),Rr=new O(1e3)
}function U(t){Rr||z();var e=Rr.get(t);if(e)return e;if(!Lr.test(t))return null;for(var i,n,r,s,o,a,h=[],l=Lr.lastIndex=0;i=Lr.exec(t);)n=i.index,n>l&&h.push({value:t.slice(l,n)}),r=Hr.test(i[0]),s=r?i[1]:i[2],o=s.charCodeAt(0),a=42===o,s=a?s.slice(1):s,h.push({tag:!0,value:s.trim(),html:r,oneTime:a}),l=n+i[0].length;
return l<t.length&&h.push({value:t.slice(l)}),Rr.put(t,h),h}function J(t,e){return t.length>1?t.map(function(t){return q(t,e)}).join("+"):q(t[0],e,!0)}function q(t,e,i){return t.tag?t.oneTime&&e?'"'+e.$eval(t.value)+'"':Q(t.value,i):'"'+t.value+'"'
}function Q(t,e){if(Ir.test(t)){var i=B(t);return i.filters?"this._applyFilters("+i.expression+",null,"+JSON.stringify(i.filters)+",false)":"("+t+")"}return e?t:"("+t+")"}function G(t,e,i,n){Y(t,1,function(){e.appendChild(t)
},i,n)}function Z(t,e,i,n){Y(t,1,function(){re(t,e)},i,n)}function X(t,e,i){Y(t,-1,function(){oe(t)},e,i)}function Y(t,e,i,n,r){var s=t.__v_trans;if(!s||!s.hooks&&!ar||!n._isCompiled||n.$parent&&!n.$parent._isCompiled)return i(),void(r&&r());
var o=e>0?"enter":"leave";s[o](i,r)}function K(t){if("string"==typeof t){t=document.querySelector(t)}return t}function te(t){if(!t)return!1;var e=t.ownerDocument.documentElement,i=t.parentNode;return e===t||e===i||!(!i||1!==i.nodeType||!e.contains(i))
}function ee(t,e){var i=t.getAttribute(e);return null!==i&&t.removeAttribute(e),i}function ie(t,e){var i=ee(t,":"+e);return null===i&&(i=ee(t,"v-bind:"+e)),i}function ne(t,e){return t.hasAttribute(e)||t.hasAttribute(":"+e)||t.hasAttribute("v-bind:"+e)
}function re(t,e){e.parentNode.insertBefore(t,e)}function se(t,e){e.nextSibling?re(t,e.nextSibling):e.parentNode.appendChild(t)}function oe(t){t.parentNode.removeChild(t)}function ae(t,e){e.firstChild?re(t,e.firstChild):e.appendChild(t)
}function he(t,e){var i=t.parentNode;i&&i.replaceChild(e,t)}function le(t,e,i,n){t.addEventListener(e,i,n)}function ce(t,e,i){t.removeEventListener(e,i)}function ue(t){var e=t.className;return"object"==typeof e&&(e=e.baseVal||""),e
}function fe(t,e){nr&&!/svg$/.test(t.namespaceURI)?t.className=e:t.setAttribute("class",e)}function pe(t,e){if(t.classList)t.classList.add(e);else{var i=" "+ue(t)+" ";i.indexOf(" "+e+" ")<0&&fe(t,(i+e).trim())
}}function de(t,e){if(t.classList)t.classList.remove(e);else{for(var i=" "+ue(t)+" ",n=" "+e+" ";i.indexOf(n)>=0;)i=i.replace(n," ");fe(t,i.trim())}t.className||t.removeAttribute("class")}function ve(t,e){var i,n;
if(_e(t)&&$e(t.content)&&(t=t.content),t.hasChildNodes())for(me(t),n=e?document.createDocumentFragment():document.createElement("div");i=t.firstChild;)n.appendChild(i);return n}function me(t){for(var e;e=t.firstChild,ge(e);)t.removeChild(e);
for(;e=t.lastChild,ge(e);)t.removeChild(e)}function ge(t){return t&&(3===t.nodeType&&!t.data.trim()||8===t.nodeType)}function _e(t){return t.tagName&&"template"===t.tagName.toLowerCase()}function ye(t,e){var i=Vr.debug?document.createComment(t):document.createTextNode(e?" ":"");
return i.__v_anchor=!0,i}function be(t){if(t.hasAttributes())for(var e=t.attributes,i=0,n=e.length;n>i;i++){var r=e[i].name;if(Jr.test(r))return f(r.replace(Jr,""))}}function we(t,e,i){for(var n;t!==e;)n=t.nextSibling,i(t),t=n;
i(e)}function Ce(t,e,i,n,r){function s(){if(a++,o&&a>=h.length){for(var t=0;t<h.length;t++)n.appendChild(h[t]);r&&r()}}var o=!1,a=0,h=[];we(t,e,function(t){t===e&&(o=!0),h.push(t),X(t,i,s)})}function $e(t){return t&&11===t.nodeType
}function ke(t){if(t.outerHTML)return t.outerHTML;var e=document.createElement("div");return e.appendChild(t.cloneNode(!0)),e.innerHTML}function xe(t,e){var i=t.tagName.toLowerCase(),n=t.hasAttributes();
if(qr.test(i)||Qr.test(i)){if(n)return Ae(t,e)}else{if(Fe(e,"components",i))return{id:i};var r=n&&Ae(t,e);if(r)return r}}function Ae(t,e){var i=t.getAttribute("is");if(null!=i){if(Fe(e,"components",i))return t.removeAttribute("is"),{id:i}
}else if(i=ie(t,"is"),null!=i)return{id:i,dynamic:!0}}function Oe(t,e){var i,r,o;for(i in e)r=t[i],o=e[i],s(t,i)?y(r)&&y(o)&&Oe(r,o):n(t,i,o);return t}function Te(t,e){var i=Object.create(t||null);return e?_(i,Ee(e)):i
}function Ne(t){if(t.components)for(var e,i=t.components=Ee(t.components),n=Object.keys(i),r=0,s=n.length;s>r;r++){var o=n[r];qr.test(o)||Qr.test(o)||(e=i[o],b(e)&&(i[o]=Hn.extend(e)))}}function je(t){var e,i,n=t.props;
if(Xn(n))for(t.props={},e=n.length;e--;)i=n[e],"string"==typeof i?t.props[i]=null:i.name&&(t.props[i.name]=i);else if(b(n)){var r=Object.keys(n);for(e=r.length;e--;)i=n[r[e]],"function"==typeof i&&(n[r[e]]={type:i})
}}function Ee(t){if(Xn(t)){for(var e,i={},n=t.length;n--;){e=t[n];var r="function"==typeof e?e.options&&e.options.name||e.id:e.name||e.id;r&&(i[r]=e)}return i}return t}function Se(t,e,i){function n(n){var r=Gr[n]||Zr;
o[n]=r(t[n],e[n],i,n)}Ne(e),je(e);var r,o={};if(e["extends"]&&(t="function"==typeof e["extends"]?Se(t,e["extends"].options,i):Se(t,e["extends"],i)),e.mixins)for(var a=0,h=e.mixins.length;h>a;a++){var l=e.mixins[a],c=l.prototype instanceof Hn?l.options:l;
t=Se(t,c,i)}for(r in t)n(r);for(r in e)s(t,r)||n(r);return o}function Fe(t,e,i,n){if("string"==typeof i){var r,s=t[e],o=s[i]||s[r=f(i)]||s[r.charAt(0).toUpperCase()+r.slice(1)];return o}}function De(){this.id=Xr++,this.subs=[]
}function Pe(t){es=!1,t(),es=!0}function Re(t){if(this.value=t,this.dep=new De,w(t,"__ob__",this),Xn(t)){var e=Yn?Le:He;e(t,Kr,ts),this.observeArray(t)}else this.walk(t)}function Le(t,e){t.__proto__=e}function He(t,e,i){for(var n=0,r=i.length;r>n;n++){var s=i[n];
w(t,s,e[s])}}function Ie(t,e){if(t&&"object"==typeof t){var i;return s(t,"__ob__")&&t.__ob__ instanceof Re?i=t.__ob__:es&&(Xn(t)||b(t))&&Object.isExtensible(t)&&!t._isVue&&(i=new Re(t)),i&&e&&i.addVm(e),i
}}function Me(t,e,i){var n=new De,r=Object.getOwnPropertyDescriptor(t,e);if(!r||r.configurable!==!1){var s=r&&r.get,o=r&&r.set,a=Ie(i);Object.defineProperty(t,e,{enumerable:!0,configurable:!0,get:function(){var e=s?s.call(t):i;
if(De.target&&(n.depend(),a&&a.dep.depend(),Xn(e)))for(var r,o=0,h=e.length;h>o;o++)r=e[o],r&&r.__ob__&&r.__ob__.dep.depend();return e},set:function(e){var r=s?s.call(t):i;e!==r&&(o?o.call(t,e):i=e,a=Ie(e),n.notify())
}})}}function We(t){t.prototype._init=function(t){t=t||{},this.$el=null,this.$parent=t.parent,this.$root=this.$parent?this.$parent.$root:this,this.$children=[],this.$refs={},this.$els={},this._watchers=[],this._directives=[],this._uid=ns++,this._isVue=!0,this._events={},this._eventsCount={},this._isFragment=!1,this._fragment=this._fragmentStart=this._fragmentEnd=null,this._isCompiled=this._isDestroyed=this._isReady=this._isAttached=this._isBeingDestroyed=this._vForRemoving=!1,this._unlinkFn=null,this._context=t._context||this.$parent,this._scope=t._scope,this._frag=t._frag,this._frag&&this._frag.children.push(this),this.$parent&&this.$parent.$children.push(this),t=this.$options=Se(this.constructor.options,t,this),this._updateRef(),this._data={},this._callHook("init"),this._initState(),this._initEvents(),this._callHook("created"),t.el&&this.$mount(t.el)
}}function Be(t){if(void 0===t)return"eof";var e=t.charCodeAt(0);switch(e){case 91:case 93:case 46:case 34:case 39:case 48:return t;case 95:case 36:return"ident";case 32:case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"ws"
}return e>=97&&122>=e||e>=65&&90>=e?"ident":e>=49&&57>=e?"number":"else"}function Ve(t){var e=t.trim();return"0"===t.charAt(0)&&isNaN(t)?!1:o(e)?u(e):"*"+e}function ze(t){function e(){var e=t[c+1];return u===ds&&"'"===e||u===vs&&'"'===e?(c++,n="\\"+e,p[ss](),!0):void 0
}var i,n,r,s,o,a,h,l=[],c=-1,u=ls,f=0,p=[];for(p[os]=function(){void 0!==r&&(l.push(r),r=void 0)},p[ss]=function(){void 0===r?r=n:r+=n},p[as]=function(){p[ss](),f++},p[hs]=function(){if(f>0)f--,u=ps,p[ss]();
else{if(f=0,r=Ve(r),r===!1)return!1;p[os]()}};null!=u;)if(c++,i=t[c],"\\"!==i||!e()){if(s=Be(i),h=_s[u],o=h[s]||h["else"]||gs,o===gs)return;if(u=o[0],a=p[o[1]],a&&(n=o[2],n=void 0===n?i:n,a()===!1))return;
if(u===ms)return l.raw=t,l}}function Ue(t){var e=rs.get(t);return e||(e=ze(t),e&&rs.put(t,e)),e}function Je(t,e){return ei(e).get(t)}function qe(t,e,i){var r=t;if("string"==typeof e&&(e=ze(e)),!e||!y(t))return!1;
for(var s,o,a=0,h=e.length;h>a;a++)s=t,o=e[a],"*"===o.charAt(0)&&(o=ei(o.slice(1)).get.call(r,r)),h-1>a?(t=t[o],y(t)||(t={},n(s,o,t))):Xn(t)?t.$set(o,i):o in t?t[o]=i:n(t,o,i);return!0}function Qe(){}function Ge(t,e){var i=Ss.length;
return Ss[i]=e?t.replace(As,"\\n"):t,'"'+i+'"'}function Ze(t){var e=t.charAt(0),i=t.slice(1);return Cs.test(i)?t:(i=i.indexOf('"')>-1?i.replace(Ts,Xe):i,e+"scope."+i)}function Xe(t,e){return Ss[e]}function Ye(t){ks.test(t),Ss.length=0;
var e=t.replace(Os,Ge).replace(xs,"");return e=(" "+e).replace(js,Ze).replace(Ts,Xe),Ke(e)}function Ke(t){try{return new Function("scope","return "+t+";")}catch(e){return Qe}}function ti(t){var e=Ue(t);
return e?function(t,i){qe(t,e,i)}:void 0}function ei(t,e){t=t.trim();var i=bs.get(t);if(i)return e&&!i.set&&(i.set=ti(i.exp)),i;var n={exp:t};return n.get=ii(t)&&t.indexOf("[")<0?Ke("scope."+t):Ye(t),e&&(n.set=ti(t)),bs.put(t,n),n
}function ii(t){return Ns.test(t)&&!Es.test(t)&&"Math."!==t.slice(0,5)}function ni(){Ds.length=0,Ps.length=0,Rs={},Ls={},Hs=!1}function ri(){for(var t=!0;t;)t=!1,si(Ds),si(Ps),Ds.length?t=!0:(tr&&Vr.devtools&&tr.emit("flush"),ni())
}function si(t){for(var e=0;e<t.length;e++){var i=t[e],n=i.id;Rs[n]=null,i.run()}t.length=0}function oi(t){var e=t.id;if(null==Rs[e]){var i=t.user?Ps:Ds;Rs[e]=i.length,i.push(t),Hs||(Hs=!0,fr(ri))}}function ai(t,e,i,n){n&&_(this,n);
var r="function"==typeof e;if(this.vm=t,t._watchers.push(this),this.expression=e,this.cb=i,this.id=++Is,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new pr,this.newDepIds=new pr,this.prevError=null,r)this.getter=e,this.setter=void 0;
else{var s=ei(e,this.twoWay);this.getter=s.get,this.setter=s.set}this.value=this.lazy?void 0:this.get(),this.queued=this.shallow=!1}function hi(t,e){var i=void 0,n=void 0;e||(e=Ms,e.clear());var r=Xn(t),s=y(t);
if((r||s)&&Object.isExtensible(t)){if(t.__ob__){var o=t.__ob__.dep.id;if(e.has(o))return;e.add(o)}if(r)for(i=t.length;i--;)hi(t[i],e);else if(s)for(n=Object.keys(t),i=n.length;i--;)hi(t[n[i]],e)}}function li(t){return _e(t)&&$e(t.content)
}function ci(t,e){var i=e?t:t.trim(),n=Bs.get(i);if(n)return n;var r=document.createDocumentFragment(),s=t.match(Us),o=Js.test(t),a=qs.test(t);if(s||o||a){var h=s&&s[1],l=zs[h]||zs.efault,c=l[0],u=l[1],f=l[2],p=document.createElement("div");
for(p.innerHTML=u+t+f;c--;)p=p.lastChild;for(var d;d=p.firstChild;)r.appendChild(d)}else r.appendChild(document.createTextNode(t));return e||me(r),Bs.put(i,r),r}function ui(t){if(li(t))return ci(t.innerHTML);
if("SCRIPT"===t.tagName)return ci(t.textContent);for(var e,i=fi(t),n=document.createDocumentFragment();e=i.firstChild;)n.appendChild(e);return me(n),n}function fi(t){if(!t.querySelectorAll)return t.cloneNode();
var e,i,n,r=t.cloneNode(!0);if(Qs){var s=r;if(li(t)&&(t=t.content,s=r.content),i=t.querySelectorAll("template"),i.length)for(n=s.querySelectorAll("template"),e=n.length;e--;)n[e].parentNode.replaceChild(fi(i[e]),n[e])
}if(Gs)if("TEXTAREA"===t.tagName)r.value=t.value;else if(i=t.querySelectorAll("textarea"),i.length)for(n=r.querySelectorAll("textarea"),e=n.length;e--;)n[e].value=i[e].value;return r}function pi(t,e,i){var n,r;
return $e(t)?(me(t),e?fi(t):t):("string"==typeof t?i||"#"!==t.charAt(0)?r=ci(t,i):(r=Vs.get(t),r||(n=document.getElementById(t.slice(1)),n&&(r=ui(n),Vs.put(t,r)))):t.nodeType&&(r=ui(t)),r&&e?fi(r):r)}function di(t,e,i,n,r,s){this.children=[],this.childFrags=[],this.vm=e,this.scope=r,this.inserted=!1,this.parentFrag=s,s&&s.childFrags.push(this),this.unlink=t(e,i,n,r,this);
var o=this.single=1===i.childNodes.length&&!i.childNodes[0].__v_anchor;o?(this.node=i.childNodes[0],this.before=vi,this.remove=mi):(this.node=ye("fragment-start"),this.end=ye("fragment-end"),this.frag=i,ae(this.node,i),i.appendChild(this.end),this.before=gi,this.remove=_i),this.node.__v_frag=this
}function vi(t,e){this.inserted=!0;var i=e!==!1?Z:re;i(this.node,t,this.vm),te(this.node)&&this.callHook(yi)}function mi(){this.inserted=!1;var t=te(this.node),e=this;this.beforeRemove(),X(this.node,this.vm,function(){t&&e.callHook(bi),e.destroy()
})}function gi(t,e){this.inserted=!0;var i=this.vm,n=e!==!1?Z:re;we(this.node,this.end,function(e){n(e,t,i)}),te(this.node)&&this.callHook(yi)}function _i(){this.inserted=!1;var t=this,e=te(this.node);
this.beforeRemove(),Ce(this.node,this.end,this.vm,this.frag,function(){e&&t.callHook(bi),t.destroy()})}function yi(t){!t._isAttached&&te(t.$el)&&t._callHook("attached")}function bi(t){t._isAttached&&!te(t.$el)&&t._callHook("detached")
}function wi(t,e){this.vm=t;var i,n="string"==typeof e;n||_e(e)&&!e.hasAttribute("v-if")?i=pi(e,!0):(i=document.createDocumentFragment(),i.appendChild(e)),this.template=i;var r,s=t.constructor.cid;if(s>0){var o=s+(n?e:ke(e));
r=Ys.get(o),r||(r=Zi(i,t.$options,!0),Ys.put(o,r))}else r=Zi(i,t.$options,!0);this.linker=r}function Ci(t,e,i){var n=t.node.previousSibling;if(n){for(t=n.__v_frag;!(t&&t.forId===i&&t.inserted||n===e);){if(n=n.previousSibling,!n)return;
t=n.__v_frag}return t}}function $i(t){for(var e=-1,i=new Array(Math.floor(t));++e<t;)i[e]=e;return i}function ki(t,e,i,n){return n?"$index"===n?t:n.charAt(0).match(/\w/)?Je(i,n):i[n]:e||i}function xi(t){var e=t.node;
if(t.end)for(;!e.__vue__&&e!==t.end&&e.nextSibling;)e=e.nextSibling;return e.__vue__}function Ai(t,e,i){for(var n,r,s,o=e?[]:null,a=0,h=t.options.length;h>a;a++)if(n=t.options[a],s=i?n.hasAttribute("selected"):n.selected){if(r=n.hasOwnProperty("_value")?n._value:n.value,!e)return r;
o.push(r)}return o}function Oi(t,e){for(var i=t.length;i--;)if(x(t[i],e))return i;return-1}function Ti(t,e){var i=e.map(function(t){var e=t.charCodeAt(0);return e>47&&58>e?parseInt(t,10):1===t.length&&(e=t.toUpperCase().charCodeAt(0),e>64&&91>e)?e:bo[t]
});return i=[].concat.apply([],i),function(e){return i.indexOf(e.keyCode)>-1?t.call(this,e):void 0}}function Ni(t){return function(e){return e.stopPropagation(),t.call(this,e)}}function ji(t){return function(e){return e.preventDefault(),t.call(this,e)
}}function Ei(t){return function(e){return e.target===e.currentTarget?t.call(this,e):void 0}}function Si(t){if(xo[t])return xo[t];var e=Fi(t);return xo[t]=xo[e]=e,e}function Fi(t){t=d(t);var e=f(t),i=e.charAt(0).toUpperCase()+e.slice(1);
Ao||(Ao=document.createElement("div"));var n,r=Co.length;if("filter"!==e&&e in Ao.style)return{kebab:t,camel:e};for(;r--;)if(n=$o[r]+i,n in Ao.style)return{kebab:Co[r]+t,camel:n}}function Di(t){var e=[];
if(Xn(t))for(var i=0,n=t.length;n>i;i++){var r=t[i];if(r)if("string"==typeof r)e.push(r);else for(var s in r)r[s]&&e.push(s)}else if(y(t))for(var o in t)t[o]&&e.push(o);return e}function Pi(t,e,i){if(e=e.trim(),-1===e.indexOf(" "))return void i(t,e);
for(var n=e.split(/\s+/),r=0,s=n.length;s>r;r++)i(t,n[r])}function Ri(t,e,i){function n(){++s>=r?i():t[s].call(e,n)}var r=t.length,s=0;t[0].call(e,n)}function Li(t,e,i){for(var n,r,s,a,h,l,c,u=[],p=i.$options.propsData,v=Object.keys(e),m=v.length;m--;)if(r=v[m],n=e[r]||Bo,h=f(r),Vo.test(h)){if(c={name:r,path:h,options:n,mode:Wo.ONE_WAY,raw:null},s=d(r),null===(a=ie(t,s))&&(null!==(a=ie(t,s+".sync"))?c.mode=Wo.TWO_WAY:null!==(a=ie(t,s+".once"))&&(c.mode=Wo.ONE_TIME)),null!==a)c.raw=a,l=B(a),a=l.expression,c.filters=l.filters,o(a)&&!l.filters?c.optimizedLiteral=!0:c.dynamic=!0,c.parentPath=a;
else if(null!==(a=ee(t,s)))c.raw=a;else if(p&&null!==(a=p[r]||p[h]))c.raw=a;else;u.push(c)}return Hi(u)}function Hi(t){return function(e,i){e._props={};for(var n,r,o,a,h,f=e.$options.propsData,p=t.length;p--;)if(n=t[p],h=n.raw,r=n.path,o=n.options,e._props[r]=n,f&&s(f,r)&&Mi(e,n,f[r]),null===h)Mi(e,n,void 0);
else if(n.dynamic)n.mode===Wo.ONE_TIME?(a=(i||e._context||e).$get(n.parentPath),Mi(e,n,a)):e._context?e._bindDir({name:"prop",def:Uo,prop:n},null,null,i):Mi(e,n,e.$get(n.parentPath));else if(n.optimizedLiteral){var v=u(h);
a=v===h?c(l(h)):v,Mi(e,n,a)}else a=o.type!==Boolean||""!==h&&h!==d(n.name)?h:!0,Mi(e,n,a)}}function Ii(t,e,i,n){var r=e.dynamic&&ii(e.parentPath),s=i;void 0===s&&(s=Bi(t,e)),s=zi(e,s,t);var o=s!==i;Vi(e,s,t)||(s=void 0),r&&!o?Pe(function(){n(s)
}):n(s)}function Mi(t,e,i){Ii(t,e,i,function(i){Me(t,e.path,i)})}function Wi(t,e,i){Ii(t,e,i,function(i){t[e.path]=i})}function Bi(t,e){var i=e.options;if(!s(i,"default"))return i.type===Boolean?!1:void 0;
var n=i["default"];return y(n),"function"==typeof n&&i.type!==Function?n.call(t):n}function Vi(t,e,i){if(!t.options.required&&(null===t.raw||null==e))return!0;var n=t.options,r=n.type,s=!r,o=[];if(r){Xn(r)||(r=[r]);
for(var a=0;a<r.length&&!s;a++){var h=Ui(e,r[a]);o.push(h.expectedType),s=h.valid}}if(!s)return!1;var l=n.validator;return l&&!l(e)?!1:!0}function zi(t,e,i){var n=t.options.coerce;return n&&"function"==typeof n?n(e):e
}function Ui(t,e){var i,n;return e===String?(n="string",i=typeof t===n):e===Number?(n="number",i=typeof t===n):e===Boolean?(n="boolean",i=typeof t===n):e===Function?(n="function",i=typeof t===n):e===Object?(n="object",i=b(t)):e===Array?(n="array",i=Xn(t)):i=t instanceof e,{valid:i,expectedType:n}
}function Ji(t){Jo.push(t),qo||(qo=!0,fr(qi))}function qi(){for(var t=document.documentElement.offsetHeight,e=0;e<Jo.length;e++)Jo[e]();return Jo=[],qo=!1,t}function Qi(t,e,i,n){this.id=e,this.el=t,this.enterClass=i&&i.enterClass||e+"-enter",this.leaveClass=i&&i.leaveClass||e+"-leave",this.hooks=i,this.vm=n,this.pendingCssEvent=this.pendingCssCb=this.cancel=this.pendingJsCb=this.op=this.cb=null,this.justEntered=!1,this.entered=this.left=!1,this.typeCache={},this.type=i&&i.type;
var r=this;["enterNextTick","enterDone","leaveNextTick","leaveDone"].forEach(function(t){r[t]=m(r[t],r)})}function Gi(t){if(/svg$/.test(t.namespaceURI)){var e=t.getBoundingClientRect();return!(e.width||e.height)
}return!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)}function Zi(t,e,i){var n=i||!e._asComponent?rn(t,e):null,r=n&&n.terminal||wn(t)||!t.hasChildNodes()?null:cn(t.childNodes,e);return function(t,e,i,s,o){var a=g(e.childNodes),h=Xi(function(){n&&n(t,e,i,s,o),r&&r(t,a,i,s,o)
},t);return Ki(t,h)}}function Xi(t,e){e._directives=[];var i=e._directives.length;t();var n=e._directives.slice(i);Yi(n);for(var r=0,s=n.length;s>r;r++)n[r]._bind();return n}function Yi(t){if(0!==t.length){var e,i,n,r,s={},o=0,a=[];
for(e=0,i=t.length;i>e;e++){var h=t[e],l=h.descriptor.def.priority||ha,c=s[l];c||(c=s[l]=[],a.push(l)),c.push(h)}for(a.sort(function(t,e){return t>e?-1:t===e?0:1}),e=0,i=a.length;i>e;e++){var u=s[a[e]];
for(n=0,r=u.length;r>n;n++)t[o++]=u[n]}}}function Ki(t,e,i,n){function r(r){tn(t,e,r),i&&n&&tn(i,n)}return r.dirs=e,r}function tn(t,e,i){for(var n=e.length;n--;)e[n]._teardown()}function en(t,e,i,n){var r=Li(e,i,t),s=Xi(function(){r(t,n)
},t);return Ki(t,s)}function nn(t,e,i){var n,r,s=e._containerAttrs,o=e._replacerAttrs;if(11!==t.nodeType)e._asComponent?(s&&i&&(n=gn(s,i)),o&&(r=gn(o,e))):r=gn(t.attributes,e);else;return e._containerAttrs=e._replacerAttrs=null,function(t,e,i){var s,o=t._context;
o&&n&&(s=Xi(function(){n(o,e,null,i)},o));var a=Xi(function(){r&&r(t,e)},t);return Ki(t,a,o,s)}}function rn(t,e){var i=t.nodeType;return 1!==i||wn(t)?3===i&&t.data.trim()?on(t,e):null:sn(t,e)}function sn(t,e){if("TEXTAREA"===t.tagName){if(null!==ee(t,"v-pre"))return vn;
var i=U(t.value);i&&(t.setAttribute(":value",J(i)),t.value="")}var n,r=t.hasAttributes(),s=r&&g(t.attributes);return r&&(n=dn(t,s,e)),n||(n=fn(t,e)),n||(n=pn(t,e)),!n&&r&&(n=gn(s,e)),n}function on(t,e){if(t._skip)return an;
var i=U(t.wholeText);if(!i)return null;for(var n=t.nextSibling;n&&3===n.nodeType;)n._skip=!0,n=n.nextSibling;for(var r,s,o=document.createDocumentFragment(),a=0,h=i.length;h>a;a++)s=i[a],r=s.tag?hn(s,e):document.createTextNode(s.value),o.appendChild(r);
return ln(i,o,e)}function an(t,e){oe(e)}function hn(t){function e(e){if(!t.descriptor){var i=B(t.value);t.descriptor={name:e,def:Ho[e],expression:i.expression,filters:i.filters}}}var i;return t.oneTime?i=document.createTextNode(t.value):t.html?(i=document.createComment("v-html"),e("html")):(i=document.createTextNode(" "),e("text")),i
}function ln(t,e){return function(i,n,r,s){for(var o,a,l,c=e.cloneNode(!0),u=g(c.childNodes),f=0,p=t.length;p>f;f++)o=t[f],a=o.value,o.tag&&(l=u[f],o.oneTime?(a=(s||i).$eval(a),o.html?he(l,pi(a,!0)):l.data=h(a)):i._bindDir(o.descriptor,l,r,s));
he(n,c)}}function cn(t,e){for(var i,n,r,s=[],o=0,a=t.length;a>o;o++)r=t[o],i=rn(r,e),n=i&&i.terminal||"SCRIPT"===r.tagName||!r.hasChildNodes()?null:cn(r.childNodes,e),s.push(i,n);return s.length?un(s):null
}function un(t){return function(e,i,n,r,s){for(var o,a,h,l=0,c=0,u=t.length;u>l;c++){o=i[c],a=t[l++],h=t[l++];var f=g(o.childNodes);a&&a(e,o,n,r,s),h&&h(e,f,n,r,s)}}}function fn(t,e){var i=t.tagName.toLowerCase();
if(!qr.test(i)){var n=Fe(e,"elementDirectives",i);return n?mn(t,i,"",e,n):void 0}}function pn(t,e){var i=xe(t,e);if(i){var n=be(t),r={name:"component",ref:n,expression:i.id,def:ia.component,modifiers:{literal:!i.dynamic}},s=function(t,e,i,s,o){n&&Me((s||t).$refs,n,null),t._bindDir(r,e,i,s,o)
};return s.terminal=!0,s}}function dn(t,e,i){if(null!==ee(t,"v-pre"))return vn;if(t.hasAttribute("v-else")){var n=t.previousElementSibling;if(n&&n.hasAttribute("v-if"))return vn}for(var r,s,o,a,h,l,c,u,f,p,d=0,v=e.length;v>d;d++)r=e[d],s=r.name.replace(oa,""),(h=s.match(sa))&&(f=Fe(i,"directives",h[1]),f&&f.terminal&&(!p||(f.priority||la)>p.priority)&&(p=f,c=r.name,a=_n(r.name),o=r.value,l=h[1],u=h[2]));
return p?mn(t,l,o,i,p,c,u,a):void 0}function vn(){}function mn(t,e,i,n,r,s,o,a){var h=B(i),l={name:e,arg:o,expression:h.expression,filters:h.filters,raw:i,attr:s,modifiers:a,def:r};("for"===e||"router-view"===e)&&(l.ref=be(t));
var c=function(t,e,i,n,r){l.ref&&Me((n||t).$refs,l.ref,null),t._bindDir(l,e,i,n,r)};return c.terminal=!0,c}function gn(t,e){function i(t,e,i){var n=i&&bn(i),r=!n&&B(s);v.push({name:t,attr:o,raw:a,def:e,arg:l,modifiers:c,expression:r&&r.expression,filters:r&&r.filters,interp:i,hasOneTime:n})
}for(var n,r,s,o,a,h,l,c,u,f,p,d=t.length,v=[];d--;)if(n=t[d],r=o=n.name,s=a=n.value,f=U(s),l=null,c=_n(r),r=r.replace(oa,""),f)s=J(f),l=r,i("bind",Ho.bind,f);else if(aa.test(r))c.literal=!na.test(r),i("transition",ia.transition);
else if(ra.test(r))l=r.replace(ra,""),i("on",Ho.on);else if(na.test(r))h=r.replace(na,""),"style"===h||"class"===h?i(h,ia[h]):(l=h,i("bind",Ho.bind));else if(p=r.match(sa)){if(h=p[1],l=p[2],"else"===h)continue;
u=Fe(e,"directives",h,!0),u&&i(h,u)}return v.length?yn(v):void 0}function _n(t){var e=Object.create(null),i=t.match(oa);if(i)for(var n=i.length;n--;)e[i[n].slice(1)]=!0;return e}function yn(t){return function(e,i,n,r,s){for(var o=t.length;o--;)e._bindDir(t[o],i,n,r,s)
}}function bn(t){for(var e=t.length;e--;)if(t[e].oneTime)return!0}function wn(t){return"SCRIPT"===t.tagName&&(!t.hasAttribute("type")||"text/javascript"===t.getAttribute("type"))}function Cn(t,e){return e&&(e._containerAttrs=kn(t)),_e(t)&&(t=pi(t)),e&&(e._asComponent&&!e.template&&(e.template="<slot></slot>"),e.template&&(e._content=ve(t),t=$n(t,e))),$e(t)&&(ae(ye("v-start",!0),t),t.appendChild(ye("v-end",!0))),t
}function $n(t,e){var i=e.template,n=pi(i,!0);if(n){var r=n.firstChild;if(!r)return n;var s=r.tagName&&r.tagName.toLowerCase();return e.replace?(t===document.body,n.childNodes.length>1||1!==r.nodeType||"component"===s||Fe(e,"components",s)||ne(r,"is")||Fe(e,"elementDirectives",s)||r.hasAttribute("v-for")||r.hasAttribute("v-if")?n:(e._replacerAttrs=kn(r),xn(t,r),r)):(t.appendChild(n),t)
}}function kn(t){return 1===t.nodeType&&t.hasAttributes()?g(t.attributes):void 0}function xn(t,e){for(var i,n,r=t.attributes,s=r.length;s--;)i=r[s].name,n=r[s].value,e.hasAttribute(i)||ca.test(i)?"class"===i&&!U(n)&&(n=n.trim())&&n.split(/\s+/).forEach(function(t){pe(e,t)
}):e.setAttribute(i,n)}function An(t,e){if(e){for(var i,n,r=t._slotContents=Object.create(null),s=0,o=e.children.length;o>s;s++)i=e.children[s],(n=i.getAttribute("slot"))&&(r[n]||(r[n]=[])).push(i);for(n in r)r[n]=On(r[n],e);
if(e.hasChildNodes()){var a=e.childNodes;if(1===a.length&&3===a[0].nodeType&&!a[0].data.trim())return;r["default"]=On(e.childNodes,e)}}}function On(t,e){var i=document.createDocumentFragment();t=g(t);for(var n=0,r=t.length;r>n;n++){var s=t[n];
!_e(s)||s.hasAttribute("v-if")||s.hasAttribute("v-for")||(e.removeChild(s),s=pi(s,!0)),i.appendChild(s)}return i}function Tn(t){function e(){}function i(t,e){var i=new ai(e,t,null,{lazy:!0});return function(){return i.dirty&&i.evaluate(),De.target&&i.depend(),i.value
}}Object.defineProperty(t.prototype,"$data",{get:function(){return this._data},set:function(t){t!==this._data&&this._setData(t)}}),t.prototype._initState=function(){this._initProps(),this._initMeta(),this._initMethods(),this._initData(),this._initComputed()
},t.prototype._initProps=function(){var t=this.$options,e=t.el,i=t.props;e=t.el=K(e),this._propsUnlinkFn=e&&1===e.nodeType&&i?en(this,e,i,this._scope):null},t.prototype._initData=function(){var t=this.$options.data,e=this._data=t?t():{};
b(e)||(e={});var i,n,r=this._props,o=Object.keys(e);for(i=o.length;i--;)n=o[i],r&&s(r,n)||this._proxy(n);Ie(e,this)},t.prototype._setData=function(t){t=t||{};var e=this._data;this._data=t;var i,n,r;for(i=Object.keys(e),r=i.length;r--;)n=i[r],n in t||this._unproxy(n);
for(i=Object.keys(t),r=i.length;r--;)n=i[r],s(this,n)||this._proxy(n);e.__ob__.removeVm(this),Ie(t,this),this._digest()},t.prototype._proxy=function(t){if(!a(t)){var e=this;Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return e._data[t]
},set:function(i){e._data[t]=i}})}},t.prototype._unproxy=function(t){a(t)||delete this[t]},t.prototype._digest=function(){for(var t=0,e=this._watchers.length;e>t;t++)this._watchers[t].update(!0)},t.prototype._initComputed=function(){var t=this.$options.computed;
if(t)for(var n in t){var r=t[n],s={enumerable:!0,configurable:!0};"function"==typeof r?(s.get=i(r,this),s.set=e):(s.get=r.get?r.cache!==!1?i(r.get,this):m(r.get,this):e,s.set=r.set?m(r.set,this):e),Object.defineProperty(this,n,s)
}},t.prototype._initMethods=function(){var t=this.$options.methods;if(t)for(var e in t)this[e]=m(t[e],this)},t.prototype._initMeta=function(){var t=this.$options._meta;if(t)for(var e in t)Me(this,e,t[e])
}}function Nn(t){function e(t,e){for(var i,n,r,s=e.attributes,o=0,a=s.length;a>o;o++)i=s[o].name,fa.test(i)&&(i=i.replace(fa,""),n=s[o].value,ii(n)&&(n+=".apply(this, $arguments)"),r=(t._scope||t._context).$eval(n,!0),r._fromParent=!0,t.$on(i.replace(fa),r))
}function i(t,e,i){if(i){var r,s,o,a;for(s in i)if(r=i[s],Xn(r))for(o=0,a=r.length;a>o;o++)n(t,e,s,r[o]);else n(t,e,s,r)}}function n(t,e,i,r,s){var o=typeof r;if("function"===o)t[e](i,r,s);else if("string"===o){var a=t.$options.methods,h=a&&a[r];
h&&t[e](i,h,s)}else r&&"object"===o&&n(t,e,i,r.handler,r)}function r(){this._isAttached||(this._isAttached=!0,this.$children.forEach(s))}function s(t){!t._isAttached&&te(t.$el)&&t._callHook("attached")
}function o(){this._isAttached&&(this._isAttached=!1,this.$children.forEach(a))}function a(t){t._isAttached&&!te(t.$el)&&t._callHook("detached")}t.prototype._initEvents=function(){var t=this.$options;t._asComponent&&e(this,t.el),i(this,"$on",t.events),i(this,"$watch",t.watch)
},t.prototype._initDOMHooks=function(){this.$on("hook:attached",r),this.$on("hook:detached",o)},t.prototype._callHook=function(t){this.$emit("pre-hook:"+t);var e=this.$options[t];if(e)for(var i=0,n=e.length;n>i;i++)e[i].call(this);
this.$emit("hook:"+t)}}function jn(){}function En(t,e,i,n,r,s){this.vm=e,this.el=i,this.descriptor=t,this.name=t.name,this.expression=t.expression,this.arg=t.arg,this.modifiers=t.modifiers,this.filters=t.filters,this.literal=this.modifiers&&this.modifiers.literal,this._locked=!1,this._bound=!1,this._listeners=null,this._host=n,this._scope=r,this._frag=s
}function Sn(t){t.prototype._updateRef=function(t){var e=this.$options._ref;if(e){var i=(this._scope||this._context).$refs;t?i[e]===this&&(i[e]=null):i[e]=this}},t.prototype._compile=function(t){var e=this.$options,i=t;
if(t=Cn(t,e),this._initElement(t),1!==t.nodeType||null===ee(t,"v-pre")){var n=this._context&&this._context.$options,r=nn(t,e,n);An(this,e._content);var s,o=this.constructor;e._linkerCachable&&(s=o.linker,s||(s=o.linker=Zi(t,e)));
var a=r(this,t,this._scope),h=s?s(this,t):Zi(t,e)(this,t);this._unlinkFn=function(){a(),h(!0)},e.replace&&he(i,t),this._isCompiled=!0,this._callHook("compiled")}},t.prototype._initElement=function(t){$e(t)?(this._isFragment=!0,this.$el=this._fragmentStart=t.firstChild,this._fragmentEnd=t.lastChild,3===this._fragmentStart.nodeType&&(this._fragmentStart.data=this._fragmentEnd.data=""),this._fragment=t):this.$el=t,this.$el.__vue__=this,this._callHook("beforeCompile")
},t.prototype._bindDir=function(t,e,i,n,r){this._directives.push(new En(t,this,e,i,n,r))},t.prototype._destroy=function(t,e){if(this._isBeingDestroyed)return void(e||this._cleanup());var i,n,r=this,s=function(){!i||n||e||r._cleanup()
};t&&this.$el&&(n=!0,this.$remove(function(){n=!1,s()})),this._callHook("beforeDestroy"),this._isBeingDestroyed=!0;var o,a=this.$parent;for(a&&!a._isBeingDestroyed&&(a.$children.$remove(this),this._updateRef(!0)),o=this.$children.length;o--;)this.$children[o].$destroy();
for(this._propsUnlinkFn&&this._propsUnlinkFn(),this._unlinkFn&&this._unlinkFn(),o=this._watchers.length;o--;)this._watchers[o].teardown();this.$el&&(this.$el.__vue__=null),i=!0,s()},t.prototype._cleanup=function(){this._isDestroyed||(this._frag&&this._frag.children.$remove(this),this._data&&this._data.__ob__&&this._data.__ob__.removeVm(this),this.$el=this.$parent=this.$root=this.$children=this._watchers=this._context=this._scope=this._directives=null,this._isDestroyed=!0,this._callHook("destroyed"),this.$off())
}}function Fn(t){t.prototype._applyFilters=function(t,e,i,n){var r,s,o,a,h,l,c,u,f;for(l=0,c=i.length;c>l;l++)if(r=i[n?c-l-1:l],s=Fe(this.$options,"filters",r.name,!0),s&&(s=n?s.write:s.read||s,"function"==typeof s)){if(o=n?[t,e]:[t],h=n?2:1,r.args)for(u=0,f=r.args.length;f>u;u++)a=r.args[u],o[u+h]=a.dynamic?this.$get(a.value):a.value;
t=s.apply(this,o)}return t},t.prototype._resolveComponent=function(e,i){var n;if(n="function"==typeof e?e:Fe(this.$options,"components",e,!0))if(n.options)i(n);else if(n.resolved)i(n.resolved);else if(n.requested)n.pendingCallbacks.push(i);
else{n.requested=!0;var r=n.pendingCallbacks=[i];n.call(this,function(e){b(e)&&(e=t.extend(e)),n.resolved=e;for(var i=0,s=r.length;s>i;i++)r[i](e)},function(t){})}}}function Dn(t){function e(t){return JSON.parse(JSON.stringify(t))
}t.prototype.$get=function(t,e){var i=ei(t);if(i){if(e){var n=this;return function(){n.$arguments=g(arguments);var t=i.get.call(n,n);return n.$arguments=null,t}}try{return i.get.call(this,this)}catch(r){}}},t.prototype.$set=function(t,e){var i=ei(t,!0);
i&&i.set&&i.set.call(this,this,e)},t.prototype.$delete=function(t){r(this._data,t)},t.prototype.$watch=function(t,e,i){var n,r=this;"string"==typeof t&&(n=B(t),t=n.expression);var s=new ai(r,t,e,{deep:i&&i.deep,sync:i&&i.sync,filters:n&&n.filters,user:!i||i.user!==!1});
return i&&i.immediate&&e.call(r,s.value),function(){s.teardown()}},t.prototype.$eval=function(t,e){if(pa.test(t)){var i=B(t),n=this.$get(i.expression,e);return i.filters?this._applyFilters(n,null,i.filters):n
}return this.$get(t,e)},t.prototype.$interpolate=function(t){var e=U(t),i=this;return e?1===e.length?i.$eval(e[0].value)+"":e.map(function(t){return t.tag?i.$eval(t.value):t.value}).join(""):t},t.prototype.$log=function(t){var i=t?Je(this._data,t):this._data;
if(i&&(i=e(i)),!t){var n;for(n in this.$options.computed)i[n]=e(this[n]);if(this._props)for(n in this._props)i[n]=e(this[n])}console.log(i)}}function Pn(t){function e(t,e,n,r,s,o){e=i(e);var a=!te(e),h=r===!1||a?s:o,l=!a&&!t._isAttached&&!te(t.$el);
return t._isFragment?(we(t._fragmentStart,t._fragmentEnd,function(i){h(i,e,t)}),n&&n()):h(t.$el,e,t,n),l&&t._callHook("attached"),t}function i(t){return"string"==typeof t?document.querySelector(t):t}function n(t,e,i,n){e.appendChild(t),n&&n()
}function r(t,e,i,n){re(t,e),n&&n()}function s(t,e,i){oe(t),i&&i()}t.prototype.$nextTick=function(t){fr(t,this)},t.prototype.$appendTo=function(t,i,r){return e(this,t,i,r,n,G)},t.prototype.$prependTo=function(t,e,n){return t=i(t),t.hasChildNodes()?this.$before(t.firstChild,e,n):this.$appendTo(t,e,n),this
},t.prototype.$before=function(t,i,n){return e(this,t,i,n,r,Z)},t.prototype.$after=function(t,e,n){return t=i(t),t.nextSibling?this.$before(t.nextSibling,e,n):this.$appendTo(t.parentNode,e,n),this},t.prototype.$remove=function(t,e){if(!this.$el.parentNode)return t&&t();
var i=this._isAttached&&te(this.$el);i||(e=!1);var n=this,r=function(){i&&n._callHook("detached"),t&&t()};if(this._isFragment)Ce(this._fragmentStart,this._fragmentEnd,this,this._fragment,r);else{var o=e===!1?s:X;
o(this.$el,this,r)}return this}}function Rn(t){function e(t,e,n){var r=t.$parent;if(r&&n&&!i.test(e))for(;r;)r._eventsCount[e]=(r._eventsCount[e]||0)+n,r=r.$parent}t.prototype.$on=function(t,i){return(this._events[t]||(this._events[t]=[])).push(i),e(this,t,1),this
},t.prototype.$once=function(t,e){function i(){n.$off(t,i),e.apply(this,arguments)}var n=this;return i.fn=e,this.$on(t,i),this},t.prototype.$off=function(t,i){var n;if(!arguments.length){if(this.$parent)for(t in this._events)n=this._events[t],n&&e(this,t,-n.length);
return this._events={},this}if(n=this._events[t],!n)return this;if(1===arguments.length)return e(this,t,-n.length),this._events[t]=null,this;for(var r,s=n.length;s--;)if(r=n[s],r===i||r.fn===i){e(this,t,-1),n.splice(s,1);
break}return this},t.prototype.$emit=function(t){var e="string"==typeof t;t=e?t:t.name;var i=this._events[t],n=e||!i;if(i){i=i.length>1?g(i):i;var r=e&&i.some(function(t){return t._fromParent});r&&(n=!1);
for(var s=g(arguments,1),o=0,a=i.length;a>o;o++){var h=i[o],l=h.apply(this,s);l!==!0||r&&!h._fromParent||(n=!0)}}return n},t.prototype.$broadcast=function(t){var e="string"==typeof t;if(t=e?t:t.name,this._eventsCount[t]){var i=this.$children,n=g(arguments);
e&&(n[0]={name:t,source:this});for(var r=0,s=i.length;s>r;r++){var o=i[r],a=o.$emit.apply(o,n);a&&o.$broadcast.apply(o,n)}return this}},t.prototype.$dispatch=function(t){var e=this.$emit.apply(this,arguments);
if(e){var i=this.$parent,n=g(arguments);for(n[0]={name:t,source:this};i;)e=i.$emit.apply(i,n),i=e?i.$parent:null;return this}};var i=/^hook:/}function Ln(t){function e(){this._isAttached=!0,this._isReady=!0,this._callHook("ready")
}t.prototype.$mount=function(t){return this._isCompiled?void 0:(t=K(t),t||(t=document.createElement("div")),this._compile(t),this._initDOMHooks(),te(this.$el)?(this._callHook("attached"),e.call(this)):this.$once("hook:attached",e),this)
},t.prototype.$destroy=function(t,e){this._destroy(t,e)},t.prototype.$compile=function(t,e,i,n){return Zi(t,this.$options,!0)(this,t,e,i,n)}}function Hn(t){this._init(t)}function In(t,e,i){return i=i?parseInt(i,10):0,e=l(e),"number"==typeof e?t.slice(i,i+e):t
}function Mn(t,e,i){if(t=ga(t),null==e)return t;if("function"==typeof e)return t.filter(e);e=(""+e).toLowerCase();for(var n,r,s,o,a="in"===i?3:2,h=Array.prototype.concat.apply([],g(arguments,a)),l=[],c=0,u=t.length;u>c;c++)if(n=t[c],s=n&&n.$value||n,o=h.length){for(;o--;)if(r=h[o],"$key"===r&&Bn(n.$key,e)||Bn(Je(s,r),e)){l.push(n);
break}}else Bn(n,e)&&l.push(n);return l}function Wn(t){function e(t,e,i){var r=n[i];return r&&("$key"!==r&&(y(t)&&"$value"in t&&(t=t.$value),y(e)&&"$value"in e&&(e=e.$value)),t=y(t)?Je(t,r):t,e=y(e)?Je(e,r):e),t===e?0:t>e?s:-s
}var i=null,n=void 0;t=ga(t);var r=g(arguments,1),s=r[r.length-1];"number"==typeof s?(s=0>s?-1:1,r=r.length>1?r.slice(0,-1):r):s=1;var o=r[0];return o?("function"==typeof o?i=function(t,e){return o(t,e)*s
}:(n=Array.prototype.concat.apply([],r),i=function(t,r,s){return s=s||0,s>=n.length-1?e(t,r,s):e(t,r,s)||i(t,r,s+1)}),t.slice().sort(i)):t}function Bn(t,e){var i;if(b(t)){var n=Object.keys(t);for(i=n.length;i--;)if(Bn(t[n[i]],e))return!0
}else if(Xn(t)){for(i=t.length;i--;)if(Bn(t[i],e))return!0}else if(null!=t)return t.toString().toLowerCase().indexOf(e)>-1}function Vn(t){function e(t){return new Function("return function "+v(t)+" (options) { this._init(options) }")()
}t.options={directives:Ho,elementDirectives:ma,filters:ya,transitions:{},components:{},partials:{},replace:!0},t.util=is,t.config=Vr,t.set=n,t["delete"]=r,t.nextTick=fr,t.compiler=ua,t.FragmentFactory=wi,t.internalDirectives=ia,t.parsers={path:ys,text:Mr,template:Zs,directive:Dr,expression:Fs},t.cid=0;
var i=1;t.extend=function(t){t=t||{};var n=this,r=0===n.cid;if(r&&t._Ctor)return t._Ctor;var s=t.name||n.options.name,o=e(s||"VueComponent");return o.prototype=Object.create(n.prototype),o.prototype.constructor=o,o.cid=i++,o.options=Se(n.options,t),o["super"]=n,o.extend=n.extend,Vr._assetTypes.forEach(function(t){o[t]=n[t]
}),s&&(o.options.components[s]=o),r&&(t._Ctor=o),o},t.use=function(t){if(!t.installed){var e=g(arguments,1);return e.unshift(this),"function"==typeof t.install?t.install.apply(t,e):t.apply(null,e),t.installed=!0,this
}},t.mixin=function(e){t.options=Se(t.options,e)},Vr._assetTypes.forEach(function(e){t[e]=function(i,n){return n?("component"===e&&b(n)&&(n.name||(n.name=i),n=t.extend(n)),this.options[e+"s"][i]=n,n):this.options[e+"s"][i]
}}),_(t.transition,Ur)}var zn=Object.prototype.hasOwnProperty,Un=/^\s?(true|false|-?[\d\.]+|'[^']*'|"[^"]*")\s?$/,Jn=/-(\w)/g,qn=/([^-])([A-Z])/g,Qn=/(?:^|[-_\/])(\w)/g,Gn=Object.prototype.toString,Zn="[object Object]",Xn=Array.isArray,Yn="__proto__"in{},Kn="undefined"!=typeof window&&"[object Object]"!==Object.prototype.toString.call(window),tr=Kn&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__,er=Kn&&window.navigator.userAgent.toLowerCase(),ir=er&&er.indexOf("trident")>0,nr=er&&er.indexOf("msie 9.0")>0,rr=er&&er.indexOf("android")>0,sr=er&&/iphone|ipad|ipod|ios/.test(er),or=void 0,ar=void 0,hr=void 0,lr=void 0;
if(Kn&&!nr){var cr=void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend,ur=void 0===window.onanimationend&&void 0!==window.onwebkitanimationend;or=cr?"WebkitTransition":"transition",ar=cr?"webkitTransitionEnd":"transitionend",hr=ur?"WebkitAnimation":"animation",lr=ur?"webkitAnimationEnd":"animationend"
}var fr=function(){function t(){i=!1;var t=e.slice(0);e.length=0;for(var n=0;n<t.length;n++)t[n]()}var e=[],i=!1,n=void 0;if("undefined"!=typeof Promise&&A(Promise)){var r=Promise.resolve(),s=function(){};
n=function(){r.then(t),sr&&setTimeout(s)}}else if("undefined"!=typeof MutationObserver){var o=1,a=new MutationObserver(t),h=document.createTextNode(String(o));a.observe(h,{characterData:!0}),n=function(){o=(o+1)%2,h.data=String(o)
}}else n=setTimeout;return function(r,s){var o=s?function(){r.call(s)}:r;e.push(o),i||(i=!0,n(t,0))}}(),pr=void 0;"undefined"!=typeof Set&&A(Set)?pr=Set:(pr=function(){this.set=Object.create(null)},pr.prototype.has=function(t){return void 0!==this.set[t]
},pr.prototype.add=function(t){this.set[t]=1},pr.prototype.clear=function(){this.set=Object.create(null)});var dr=O.prototype;dr.put=function(t,e){var i,n=this.get(t,!0);return n||(this.size===this.limit&&(i=this.shift()),n={key:t},this._keymap[t]=n,this.tail?(this.tail.newer=n,n.older=this.tail):this.head=n,this.tail=n,this.size++),n.value=e,i
},dr.shift=function(){var t=this.head;return t&&(this.head=this.head.newer,this.head.older=void 0,t.newer=t.older=void 0,this._keymap[t.key]=void 0,this.size--),t},dr.get=function(t,e){var i=this._keymap[t];
if(void 0!==i)return i===this.tail?e?i:i.value:(i.newer&&(i===this.head&&(this.head=i.newer),i.newer.older=i.older),i.older&&(i.older.newer=i.newer),i.newer=void 0,i.older=this.tail,this.tail&&(this.tail.newer=i),this.tail=i,e?i:i.value)
};var vr,mr,gr,_r,yr,br,wr=new O(1e3),Cr=/^in$|^-?\d+/,$r=0,kr=1,xr=2,Ar=3,Or=34,Tr=39,Nr=124,jr=92,Er=32,Sr={91:1,123:1,40:1},Fr={91:93,123:125,40:41},Dr=Object.freeze({parseDirective:B}),Pr=/[-.*+?^${}()|[\]\/\\]/g,Rr=void 0,Lr=void 0,Hr=void 0,Ir=/[^|]\|[^|]/,Mr=Object.freeze({compileRegex:z,parseText:U,tokensToExp:J}),Wr=["{{","}}"],Br=["{{{","}}}"],Vr=Object.defineProperties({debug:!1,silent:!1,async:!0,warnExpressionErrors:!0,devtools:!1,_delimitersChanged:!0,_assetTypes:["component","directive","elementDirective","filter","transition","partial"],_propBindingModes:{ONE_WAY:0,TWO_WAY:1,ONE_TIME:2},_maxUpdateCount:100},{delimiters:{get:function(){return Wr
},set:function(t){Wr=t,z()},configurable:!0,enumerable:!0},unsafeDelimiters:{get:function(){return Br},set:function(t){Br=t,z()},configurable:!0,enumerable:!0}}),zr=void 0,Ur=Object.freeze({appendWithTransition:G,beforeWithTransition:Z,removeWithTransition:X,applyTransition:Y}),Jr=/^v-ref:/,qr=/^(div|p|span|img|a|b|i|br|ul|ol|li|h1|h2|h3|h4|h5|h6|code|pre|table|th|td|tr|form|label|input|select|option|nav|article|section|header|footer)$/i,Qr=/^(slot|partial|component)$/i,Gr=Vr.optionMergeStrategies=Object.create(null);
Gr.data=function(t,e,i){return i?t||e?function(){var n="function"==typeof e?e.call(i):e,r="function"==typeof t?t.call(i):void 0;return n?Oe(n,r):r}:void 0:e?"function"!=typeof e?t:t?function(){return Oe(e.call(this),t.call(this))
}:e:t},Gr.el=function(t,e,i){if(i||!e||"function"==typeof e){var n=e||t;return i&&"function"==typeof n?n.call(i):n}},Gr.init=Gr.created=Gr.ready=Gr.attached=Gr.detached=Gr.beforeCompile=Gr.compiled=Gr.beforeDestroy=Gr.destroyed=Gr.activate=function(t,e){return e?t?t.concat(e):Xn(e)?e:[e]:t
},Vr._assetTypes.forEach(function(t){Gr[t+"s"]=Te}),Gr.watch=Gr.events=function(t,e){if(!e)return t;if(!t)return e;var i={};_(i,t);for(var n in e){var r=i[n],s=e[n];r&&!Xn(r)&&(r=[r]),i[n]=r?r.concat(s):[s]
}return i},Gr.props=Gr.methods=Gr.computed=function(t,e){if(!e)return t;if(!t)return e;var i=Object.create(null);return _(i,t),_(i,e),i};var Zr=function(t,e){return void 0===e?t:e},Xr=0;De.target=null,De.prototype.addSub=function(t){this.subs.push(t)
},De.prototype.removeSub=function(t){this.subs.$remove(t)},De.prototype.depend=function(){De.target.addDep(this)},De.prototype.notify=function(){for(var t=g(this.subs),e=0,i=t.length;i>e;e++)t[e].update()
};var Yr=Array.prototype,Kr=Object.create(Yr);["push","pop","shift","unshift","splice","sort","reverse"].forEach(function(t){var e=Yr[t];w(Kr,t,function(){for(var i=arguments.length,n=new Array(i);i--;)n[i]=arguments[i];
var r,s=e.apply(this,n),o=this.__ob__;switch(t){case"push":r=n;break;case"unshift":r=n;break;case"splice":r=n.slice(2)}return r&&o.observeArray(r),o.dep.notify(),s})}),w(Yr,"$set",function(t,e){return t>=this.length&&(this.length=Number(t)+1),this.splice(t,1,e)[0]
}),w(Yr,"$remove",function(t){if(this.length){var e=$(this,t);return e>-1?this.splice(e,1):void 0}});var ts=Object.getOwnPropertyNames(Kr),es=!0;Re.prototype.walk=function(t){for(var e=Object.keys(t),i=0,n=e.length;n>i;i++)this.convert(e[i],t[e[i]])
},Re.prototype.observeArray=function(t){for(var e=0,i=t.length;i>e;e++)Ie(t[e])},Re.prototype.convert=function(t,e){Me(this.value,t,e)},Re.prototype.addVm=function(t){(this.vms||(this.vms=[])).push(t)},Re.prototype.removeVm=function(t){this.vms.$remove(t)
};var is=Object.freeze({defineReactive:Me,set:n,del:r,hasOwn:s,isLiteral:o,isReserved:a,_toString:h,toNumber:l,toBoolean:c,stripQuotes:u,camelize:f,hyphenate:d,classify:v,bind:m,toArray:g,extend:_,isObject:y,isPlainObject:b,def:w,debounce:C,indexOf:$,cancellable:k,looseEqual:x,isArray:Xn,hasProto:Yn,inBrowser:Kn,devtools:tr,isIE:ir,isIE9:nr,isAndroid:rr,isIOS:sr,get transitionProp(){return or
},get transitionEndEvent(){return ar},get animationProp(){return hr},get animationEndEvent(){return lr},nextTick:fr,get _Set(){return pr},query:K,inDoc:te,getAttr:ee,getBindAttr:ie,hasBindAttr:ne,before:re,after:se,remove:oe,prepend:ae,replace:he,on:le,off:ce,setClass:fe,addClass:pe,removeClass:de,extractContent:ve,trimNode:me,isTemplate:_e,createAnchor:ye,findRef:be,mapNodeRange:we,removeNodeRange:Ce,isFragment:$e,getOuterHTML:ke,mergeOptions:Se,resolveAsset:Fe,checkComponentAttr:xe,commonTagRE:qr,reservedTagRE:Qr,get warn(){return zr
}}),ns=0,rs=new O(1e3),ss=0,os=1,as=2,hs=3,ls=0,cs=1,us=2,fs=3,ps=4,ds=5,vs=6,ms=7,gs=8,_s=[];_s[ls]={ws:[ls],ident:[fs,ss],"[":[ps],eof:[ms]},_s[cs]={ws:[cs],".":[us],"[":[ps],eof:[ms]},_s[us]={ws:[us],ident:[fs,ss]},_s[fs]={ident:[fs,ss],0:[fs,ss],number:[fs,ss],ws:[cs,os],".":[us,os],"[":[ps,os],eof:[ms,os]},_s[ps]={"'":[ds,ss],'"':[vs,ss],"[":[ps,as],"]":[cs,hs],eof:gs,"else":[ps,ss]},_s[ds]={"'":[ps,ss],eof:gs,"else":[ds,ss]},_s[vs]={'"':[ps,ss],eof:gs,"else":[vs,ss]};
var ys=Object.freeze({parsePath:Ue,getPath:Je,setPath:qe}),bs=new O(1e3),ws="Math,Date,this,true,false,null,undefined,Infinity,NaN,isNaN,isFinite,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,parseInt,parseFloat",Cs=new RegExp("^("+ws.replace(/,/g,"\\b|")+"\\b)"),$s="break,case,class,catch,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,in,instanceof,let,return,super,switch,throw,try,var,while,with,yield,enum,await,implements,package,protected,static,interface,private,public",ks=new RegExp("^("+$s.replace(/,/g,"\\b|")+"\\b)"),xs=/\s/g,As=/\n/g,Os=/[\{,]\s*[\w\$_]+\s*:|('(?:[^'\\]|\\.)*'|"(?:[^"\\]|\\.)*"|`(?:[^`\\]|\\.)*\$\{|\}(?:[^`\\"']|\\.)*`|`(?:[^`\\]|\\.)*`)|new |typeof |void /g,Ts=/"(\d+)"/g,Ns=/^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['.*?'\]|\[".*?"\]|\[\d+\]|\[[A-Za-z_$][\w$]*\])*$/,js=/[^\w$\.](?:[A-Za-z_$][\w$]*)/g,Es=/^(?:true|false|null|undefined|Infinity|NaN)$/,Ss=[],Fs=Object.freeze({parseExpression:ei,isSimplePath:ii}),Ds=[],Ps=[],Rs={},Ls={},Hs=!1,Is=0;
ai.prototype.get=function(){this.beforeGet();var t,e=this.scope||this.vm;try{t=this.getter.call(e,e)}catch(i){}return this.deep&&hi(t),this.preProcess&&(t=this.preProcess(t)),this.filters&&(t=e._applyFilters(t,null,this.filters,!1)),this.postProcess&&(t=this.postProcess(t)),this.afterGet(),t
},ai.prototype.set=function(t){var e=this.scope||this.vm;this.filters&&(t=e._applyFilters(t,this.value,this.filters,!0));try{this.setter.call(e,e,t)}catch(i){}var n=e.$forContext;if(n&&n.alias===this.expression){if(n.filters)return;
n._withLock(function(){e.$key?n.rawValue[e.$key]=t:n.rawValue.$set(e.$index,t)})}},ai.prototype.beforeGet=function(){De.target=this},ai.prototype.addDep=function(t){var e=t.id;this.newDepIds.has(e)||(this.newDepIds.add(e),this.newDeps.push(t),this.depIds.has(e)||t.addSub(this))
},ai.prototype.afterGet=function(){De.target=null;for(var t=this.deps.length;t--;){var e=this.deps[t];this.newDepIds.has(e.id)||e.removeSub(this)}var i=this.depIds;this.depIds=this.newDepIds,this.newDepIds=i,this.newDepIds.clear(),i=this.deps,this.deps=this.newDeps,this.newDeps=i,this.newDeps.length=0
},ai.prototype.update=function(t){this.lazy?this.dirty=!0:this.sync||!Vr.async?this.run():(this.shallow=this.queued?t?this.shallow:!1:!!t,this.queued=!0,oi(this))},ai.prototype.run=function(){if(this.active){var t=this.get();
if(t!==this.value||(y(t)||this.deep)&&!this.shallow){var e=this.value;this.value=t;{this.prevError}this.cb.call(this.vm,t,e)}this.queued=this.shallow=!1}},ai.prototype.evaluate=function(){var t=De.target;
this.value=this.get(),this.dirty=!1,De.target=t},ai.prototype.depend=function(){for(var t=this.deps.length;t--;)this.deps[t].depend()},ai.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||this.vm._vForRemoving||this.vm._watchers.$remove(this);
for(var t=this.deps.length;t--;)this.deps[t].removeSub(this);this.active=!1,this.vm=this.cb=this.value=null}};var Ms=new pr,Ws={bind:function(){this.attr=3===this.el.nodeType?"data":"textContent"},update:function(t){this.el[this.attr]=h(t)
}},Bs=new O(1e3),Vs=new O(1e3),zs={efault:[0,"",""],legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"]};zs.td=zs.th=[3,"<table><tbody><tr>","</tr></tbody></table>"],zs.option=zs.optgroup=[1,'<select multiple="multiple">',"</select>"],zs.thead=zs.tbody=zs.colgroup=zs.caption=zs.tfoot=[1,"<table>","</table>"],zs.g=zs.defs=zs.symbol=zs.use=zs.image=zs.text=zs.circle=zs.ellipse=zs.line=zs.path=zs.polygon=zs.polyline=zs.rect=[1,'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"version="1.1">',"</svg>"];
var Us=/<([\w:-]+)/,Js=/&#?\w+?;/,qs=/<!--/,Qs=function(){if(Kn){var t=document.createElement("div");return t.innerHTML="<template>1</template>",!t.cloneNode(!0).firstChild.innerHTML}return!1}(),Gs=function(){if(Kn){var t=document.createElement("textarea");
return t.placeholder="t","t"===t.cloneNode(!0).value}return!1}(),Zs=Object.freeze({cloneNode:fi,parseTemplate:pi}),Xs={bind:function(){8===this.el.nodeType&&(this.nodes=[],this.anchor=ye("v-html"),he(this.el,this.anchor))
},update:function(t){t=h(t),this.nodes?this.swap(t):this.el.innerHTML=t},swap:function(t){for(var e=this.nodes.length;e--;)oe(this.nodes[e]);var i=pi(t,!0,!0);this.nodes=g(i.childNodes),re(i,this.anchor)
}};di.prototype.callHook=function(t){var e,i;for(e=0,i=this.childFrags.length;i>e;e++)this.childFrags[e].callHook(t);for(e=0,i=this.children.length;i>e;e++)t(this.children[e])},di.prototype.beforeRemove=function(){var t,e;
for(t=0,e=this.childFrags.length;e>t;t++)this.childFrags[t].beforeRemove(!1);for(t=0,e=this.children.length;e>t;t++)this.children[t].$destroy(!1,!0);var i=this.unlink.dirs;for(t=0,e=i.length;e>t;t++)i[t]._watcher&&i[t]._watcher.teardown()
},di.prototype.destroy=function(){this.parentFrag&&this.parentFrag.childFrags.$remove(this),this.node.__v_frag=null,this.unlink()};var Ys=new O(5e3);wi.prototype.create=function(t,e,i){var n=fi(this.template);
return new di(this.linker,this.vm,n,t,e,i)};var Ks=700,to=800,eo=850,io=1100,no=1500,ro=1500,so=1750,oo=2100,ao=2200,ho=2300,lo=0,co={priority:ao,terminal:!0,params:["track-by","stagger","enter-stagger","leave-stagger"],bind:function(){var t=this.expression.match(/(.*) (?:in|of) (.*)/);
if(t){var e=t[1].match(/\((.*),(.*)\)/);e?(this.iterator=e[1].trim(),this.alias=e[2].trim()):this.alias=t[1].trim(),this.expression=t[2]}if(this.alias){this.id="__v-for__"+ ++lo;var i=this.el.tagName;this.isOption=("OPTION"===i||"OPTGROUP"===i)&&"SELECT"===this.el.parentNode.tagName,this.start=ye("v-for-start"),this.end=ye("v-for-end"),he(this.el,this.end),re(this.start,this.end),this.cache=Object.create(null),this.factory=new wi(this.vm,this.el)
}},update:function(t){this.diff(t),this.updateRef(),this.updateModel()},diff:function(t){var e,i,n,r,o,a,h=t[0],l=this.fromObject=y(h)&&s(h,"$key")&&s(h,"$value"),c=this.params.trackBy,u=this.frags,f=this.frags=new Array(t.length),p=this.alias,d=this.iterator,v=this.start,m=this.end,g=te(v),_=!u;
for(e=0,i=t.length;i>e;e++)h=t[e],r=l?h.$key:null,o=l?h.$value:h,a=!y(o),n=!_&&this.getCachedFrag(o,e,r),n?(n.reused=!0,n.scope.$index=e,r&&(n.scope.$key=r),d&&(n.scope[d]=null!==r?r:e),(c||l||a)&&Pe(function(){n.scope[p]=o
})):(n=this.create(o,p,e,r),n.fresh=!_),f[e]=n,_&&n.before(m);if(!_){var b=0,w=u.length-f.length;for(this.vm._vForRemoving=!0,e=0,i=u.length;i>e;e++)n=u[e],n.reused||(this.deleteCachedFrag(n),this.remove(n,b++,w,g));
this.vm._vForRemoving=!1,b&&(this.vm._watchers=this.vm._watchers.filter(function(t){return t.active}));var C,$,k,x=0;for(e=0,i=f.length;i>e;e++)n=f[e],C=f[e-1],$=C?C.staggerCb?C.staggerAnchor:C.end||C.node:v,n.reused&&!n.staggerCb?(k=Ci(n,v,this.id),k===C||k&&Ci(k,v,this.id)===C||this.move(n,$)):this.insert(n,x++,$,g),n.reused=n.fresh=!1
}},create:function(t,e,i,n){var r=this._host,s=this._scope||this.vm,o=Object.create(s);o.$refs=Object.create(s.$refs),o.$els=Object.create(s.$els),o.$parent=s,o.$forContext=this,Pe(function(){Me(o,e,t)
}),Me(o,"$index",i),n?Me(o,"$key",n):o.$key&&w(o,"$key",null),this.iterator&&Me(o,this.iterator,null!==n?n:i);var a=this.factory.create(r,o,this._frag);return a.forId=this.id,this.cacheFrag(t,a,i,n),a},updateRef:function(){var t=this.descriptor.ref;
if(t){var e,i=(this._scope||this.vm).$refs;this.fromObject?(e={},this.frags.forEach(function(t){e[t.scope.$key]=xi(t)})):e=this.frags.map(xi),i[t]=e}},updateModel:function(){if(this.isOption){var t=this.start.parentNode,e=t&&t.__v_model;
e&&e.forceUpdate()}},insert:function(t,e,i,n){t.staggerCb&&(t.staggerCb.cancel(),t.staggerCb=null);var r=this.getStagger(t,e,null,"enter");if(n&&r){var s=t.staggerAnchor;s||(s=t.staggerAnchor=ye("stagger-anchor"),s.__v_frag=t),se(s,i);
var o=t.staggerCb=k(function(){t.staggerCb=null,t.before(s),oe(s)});setTimeout(o,r)}else{var a=i.nextSibling;a||(se(this.end,i),a=this.end),t.before(a)}},remove:function(t,e,i,n){if(t.staggerCb)return t.staggerCb.cancel(),void(t.staggerCb=null);
var r=this.getStagger(t,e,i,"leave");if(n&&r){var s=t.staggerCb=k(function(){t.staggerCb=null,t.remove()});setTimeout(s,r)}else t.remove()},move:function(t,e){e.nextSibling||this.end.parentNode.appendChild(this.end),t.before(e.nextSibling,!1)
},cacheFrag:function(t,e,i,n){var r,o=this.params.trackBy,a=this.cache,h=!y(t);n||o||h?(r=ki(i,n,t,o),a[r]||(a[r]=e)):(r=this.id,s(t,r)?null===t[r]&&(t[r]=e):Object.isExtensible(t)&&w(t,r,e)),e.raw=t},getCachedFrag:function(t,e,i){var n,r=this.params.trackBy,s=!y(t);
if(i||r||s){var o=ki(e,i,t,r);n=this.cache[o]}else n=t[this.id];return n&&(n.reused||n.fresh),n},deleteCachedFrag:function(t){var e=t.raw,i=this.params.trackBy,n=t.scope,r=n.$index,o=s(n,"$key")&&n.$key,a=!y(e);
if(i||o||a){var h=ki(r,o,e,i);this.cache[h]=null}else e[this.id]=null,t.raw=null},getStagger:function(t,e,i,n){n+="Stagger";var r=t.node.__v_trans,s=r&&r.hooks,o=s&&(s[n]||s.stagger);return o?o.call(t,e,i):e*parseInt(this.params[n]||this.params.stagger,10)
},_preProcess:function(t){return this.rawValue=t,t},_postProcess:function(t){if(Xn(t))return t;if(b(t)){for(var e,i=Object.keys(t),n=i.length,r=new Array(n);n--;)e=i[n],r[n]={$key:e,$value:t[e]};return r
}return"number"!=typeof t||isNaN(t)||(t=$i(t)),t||[]},unbind:function(){if(this.descriptor.ref&&((this._scope||this.vm).$refs[this.descriptor.ref]=null),this.frags)for(var t,e=this.frags.length;e--;)t=this.frags[e],this.deleteCachedFrag(t),t.destroy()
}},uo={priority:oo,terminal:!0,bind:function(){var t=this.el;if(t.__vue__)this.invalid=!0;else{var e=t.nextElementSibling;e&&null!==ee(e,"v-else")&&(oe(e),this.elseEl=e),this.anchor=ye("v-if"),he(t,this.anchor)
}},update:function(t){this.invalid||(t?this.frag||this.insert():this.remove())},insert:function(){this.elseFrag&&(this.elseFrag.remove(),this.elseFrag=null),this.factory||(this.factory=new wi(this.vm,this.el)),this.frag=this.factory.create(this._host,this._scope,this._frag),this.frag.before(this.anchor)
},remove:function(){this.frag&&(this.frag.remove(),this.frag=null),this.elseEl&&!this.elseFrag&&(this.elseFactory||(this.elseFactory=new wi(this.elseEl._context||this.vm,this.elseEl)),this.elseFrag=this.elseFactory.create(this._host,this._scope,this._frag),this.elseFrag.before(this.anchor))
},unbind:function(){this.frag&&this.frag.destroy(),this.elseFrag&&this.elseFrag.destroy()}},fo={bind:function(){var t=this.el.nextElementSibling;t&&null!==ee(t,"v-else")&&(this.elseEl=t)},update:function(t){this.apply(this.el,t),this.elseEl&&this.apply(this.elseEl,!t)
},apply:function(t,e){function i(){t.style.display=e?"":"none"}te(t)?Y(t,e?1:-1,i,this.vm):i()}},po={bind:function(){var t=this,e=this.el,i="range"===e.type,n=this.params.lazy,r=this.params.number,s=this.params.debounce,o=!1;
if(rr||i||(this.on("compositionstart",function(){o=!0}),this.on("compositionend",function(){o=!1,n||t.listener()})),this.focused=!1,i||n||(this.on("focus",function(){t.focused=!0}),this.on("blur",function(){t.focused=!1,(!t._frag||t._frag.inserted)&&t.rawListener()
})),this.listener=this.rawListener=function(){if(!o&&t._bound){var n=r||i?l(e.value):e.value;t.set(n),fr(function(){t._bound&&!t.focused&&t.update(t._watcher.value)})}},s&&(this.listener=C(this.listener,s)),this.hasjQuery="function"==typeof jQuery,this.hasjQuery){var a=jQuery.fn.on?"on":"bind";
jQuery(e)[a]("change",this.rawListener),n||jQuery(e)[a]("input",this.listener)}else this.on("change",this.rawListener),n||this.on("input",this.listener);!n&&nr&&(this.on("cut",function(){fr(t.listener)
}),this.on("keyup",function(e){(46===e.keyCode||8===e.keyCode)&&t.listener()})),(e.hasAttribute("value")||"TEXTAREA"===e.tagName&&e.value.trim())&&(this.afterBind=this.listener)},update:function(t){t=h(t),t!==this.el.value&&(this.el.value=t)
},unbind:function(){var t=this.el;if(this.hasjQuery){var e=jQuery.fn.off?"off":"unbind";jQuery(t)[e]("change",this.listener),jQuery(t)[e]("input",this.listener)}}},vo={bind:function(){var t=this,e=this.el;
this.getValue=function(){if(e.hasOwnProperty("_value"))return e._value;var i=e.value;return t.params.number&&(i=l(i)),i},this.listener=function(){t.set(t.getValue())},this.on("change",this.listener),e.hasAttribute("checked")&&(this.afterBind=this.listener)
},update:function(t){this.el.checked=x(t,this.getValue())}},mo={bind:function(){var t=this,e=this,i=this.el;this.forceUpdate=function(){e._watcher&&e.update(e._watcher.get())};var n=this.multiple=i.hasAttribute("multiple");
this.listener=function(){var t=Ai(i,n);t=e.params.number?Xn(t)?t.map(l):l(t):t,e.set(t)},this.on("change",this.listener);var r=Ai(i,n,!0);(n&&r.length||!n&&null!==r)&&(this.afterBind=this.listener),this.vm.$on("hook:attached",function(){fr(t.forceUpdate)
}),te(i)||fr(this.forceUpdate)},update:function(t){var e=this.el;e.selectedIndex=-1;for(var i,n,r=this.multiple&&Xn(t),s=e.options,o=s.length;o--;)i=s[o],n=i.hasOwnProperty("_value")?i._value:i.value,i.selected=r?Oi(t,n)>-1:x(t,n)
},unbind:function(){this.vm.$off("hook:attached",this.forceUpdate)}},go={bind:function(){function t(){var t=i.checked;return t&&i.hasOwnProperty("_trueValue")?i._trueValue:!t&&i.hasOwnProperty("_falseValue")?i._falseValue:t
}var e=this,i=this.el;this.getValue=function(){return i.hasOwnProperty("_value")?i._value:e.params.number?l(i.value):i.value},this.listener=function(){var n=e._watcher.get();if(Xn(n)){var r=e.getValue(),s=$(n,r);
i.checked?0>s&&e.set(n.concat(r)):s>-1&&e.set(n.slice(0,s).concat(n.slice(s+1)))}else e.set(t())},this.on("change",this.listener),i.hasAttribute("checked")&&(this.afterBind=this.listener)},update:function(t){var e=this.el;
e.checked=Xn(t)?$(t,this.getValue())>-1:e.hasOwnProperty("_trueValue")?x(t,e._trueValue):!!t}},_o={text:po,radio:vo,select:mo,checkbox:go},yo={priority:to,twoWay:!0,handlers:_o,params:["lazy","number","debounce"],bind:function(){this.checkFilters(),this.hasRead&&!this.hasWrite;
var t,e=this.el,i=e.tagName;if("INPUT"===i)t=_o[e.type]||_o.text;else if("SELECT"===i)t=_o.select;else{if("TEXTAREA"!==i)return;t=_o.text}e.__v_model=this,t.bind.call(this),this.update=t.update,this._unbind=t.unbind
},checkFilters:function(){var t=this.filters;if(t)for(var e=t.length;e--;){var i=Fe(this.vm.$options,"filters",t[e].name);("function"==typeof i||i.read)&&(this.hasRead=!0),i.write&&(this.hasWrite=!0)}},unbind:function(){this.el.__v_model=null,this._unbind&&this._unbind()
}},bo={esc:27,tab:9,enter:13,space:32,"delete":[8,46],up:38,left:37,right:39,down:40},wo={priority:Ks,acceptStatement:!0,keyCodes:bo,bind:function(){if("IFRAME"===this.el.tagName&&"load"!==this.arg){var t=this;
this.iframeBind=function(){le(t.el.contentWindow,t.arg,t.handler,t.modifiers.capture)},this.on("load",this.iframeBind)}},update:function(t){if(this.descriptor.raw||(t=function(){}),"function"==typeof t){this.modifiers.stop&&(t=Ni(t)),this.modifiers.prevent&&(t=ji(t)),this.modifiers.self&&(t=Ei(t));
var e=Object.keys(this.modifiers).filter(function(t){return"stop"!==t&&"prevent"!==t&&"self"!==t&&"capture"!==t});e.length&&(t=Ti(t,e)),this.reset(),this.handler=t,this.iframeBind?this.iframeBind():le(this.el,this.arg,this.handler,this.modifiers.capture)
}},reset:function(){var t=this.iframeBind?this.el.contentWindow:this.el;this.handler&&ce(t,this.arg,this.handler)},unbind:function(){this.reset()}},Co=["-webkit-","-moz-","-ms-"],$o=["Webkit","Moz","ms"],ko=/!important;?$/,xo=Object.create(null),Ao=null,Oo={deep:!0,update:function(t){"string"==typeof t?this.el.style.cssText=t:this.handleObject(Xn(t)?t.reduce(_,{}):t||{})
},handleObject:function(t){var e,i,n=this.cache||(this.cache={});for(e in n)e in t||(this.handleSingle(e,null),delete n[e]);for(e in t)i=t[e],i!==n[e]&&(n[e]=i,this.handleSingle(e,i))},handleSingle:function(t,e){if(t=Si(t))if(null!=e&&(e+=""),e){var i=ko.test(e)?"important":"";
i?(e=e.replace(ko,"").trim(),this.el.style.setProperty(t.kebab,e,i)):this.el.style[t.camel]=e}else this.el.style[t.camel]=""}},To="http://www.w3.org/1999/xlink",No=/^xlink:/,jo=/^v-|^:|^@|^(?:is|transition|transition-mode|debounce|track-by|stagger|enter-stagger|leave-stagger)$/,Eo=/^(?:value|checked|selected|muted)$/,So=/^(?:draggable|contenteditable|spellcheck)$/,Fo={value:"_value","true-value":"_trueValue","false-value":"_falseValue"},Do={priority:eo,bind:function(){var t=this.arg,e=this.el.tagName;
t||(this.deep=!0);var i=this.descriptor,n=i.interp;if(n){i.hasOneTime&&(this.expression=J(n,this._scope||this.vm)),(jo.test(t)||"name"===t&&("PARTIAL"===e||"SLOT"===e))&&(this.el.removeAttribute(t),this.invalid=!0)
}},update:function(t){if(!this.invalid){var e=this.arg;this.arg?this.handleSingle(e,t):this.handleObject(t||{})}},handleObject:Oo.handleObject,handleSingle:function(t,e){var i=this.el,n=this.descriptor.interp;
if(this.modifiers.camel&&(t=f(t)),!n&&Eo.test(t)&&t in i){var r="value"===t&&null==e?"":e;i[t]!==r&&(i[t]=r)}var s=Fo[t];if(!n&&s){i[s]=e;var o=i.__v_model;o&&o.listener()}return"value"===t&&"TEXTAREA"===i.tagName?void i.removeAttribute(t):void(So.test(t)?i.setAttribute(t,e?"true":"false"):null!=e&&e!==!1?"class"===t?(i.__v_trans&&(e+=" "+i.__v_trans.id+"-transition"),fe(i,e)):No.test(t)?i.setAttributeNS(To,t,e===!0?"":e):i.setAttribute(t,e===!0?"":e):i.removeAttribute(t))
}},Po={priority:no,bind:function(){if(this.arg){var t=this.id=f(this.arg),e=(this._scope||this.vm).$els;s(e,t)?e[t]=this.el:Me(e,t,this.el)}},unbind:function(){var t=(this._scope||this.vm).$els;t[this.id]===this.el&&(t[this.id]=null)
}},Ro={bind:function(){}},Lo={bind:function(){var t=this.el;this.vm.$once("pre-hook:compiled",function(){t.removeAttribute("v-cloak")})}},Ho={text:Ws,html:Xs,"for":co,"if":uo,show:fo,model:yo,on:wo,bind:Do,el:Po,ref:Ro,cloak:Lo},Io={deep:!0,update:function(t){t?this.setClass("string"==typeof t?t.trim().split(/\s+/):Di(t)):this.cleanup()
},setClass:function(t){this.cleanup(t);for(var e=0,i=t.length;i>e;e++){var n=t[e];n&&Pi(this.el,n,pe)}this.prevKeys=t},cleanup:function(t){var e=this.prevKeys;if(e)for(var i=e.length;i--;){var n=e[i];(!t||t.indexOf(n)<0)&&Pi(this.el,n,de)
}}},Mo={priority:ro,params:["keep-alive","transition-mode","inline-template"],bind:function(){this.el.__vue__||(this.keepAlive=this.params.keepAlive,this.keepAlive&&(this.cache={}),this.params.inlineTemplate&&(this.inlineTemplate=ve(this.el,!0)),this.pendingComponentCb=this.Component=null,this.pendingRemovals=0,this.pendingRemovalCb=null,this.anchor=ye("v-component"),he(this.el,this.anchor),this.el.removeAttribute("is"),this.el.removeAttribute(":is"),this.descriptor.ref&&this.el.removeAttribute("v-ref:"+d(this.descriptor.ref)),this.literal&&this.setComponent(this.expression))
},update:function(t){this.literal||this.setComponent(t)},setComponent:function(t,e){if(this.invalidatePending(),t){var i=this;this.resolveComponent(t,function(){i.mountComponent(e)})}else this.unbuild(!0),this.remove(this.childVM,e),this.childVM=null
},resolveComponent:function(t,e){var i=this;this.pendingComponentCb=k(function(n){i.ComponentName=n.options.name||("string"==typeof t?t:null),i.Component=n,e()}),this.vm._resolveComponent(t,this.pendingComponentCb)
},mountComponent:function(t){this.unbuild(!0);var e=this,i=this.Component.options.activate,n=this.getCached(),r=this.build();i&&!n?(this.waitingFor=r,Ri(i,r,function(){e.waitingFor===r&&(e.waitingFor=null,e.transition(r,t))
})):(n&&r._updateRef(),this.transition(r,t))},invalidatePending:function(){this.pendingComponentCb&&(this.pendingComponentCb.cancel(),this.pendingComponentCb=null)},build:function(t){var e=this.getCached();
if(e)return e;if(this.Component){var i={name:this.ComponentName,el:fi(this.el),template:this.inlineTemplate,parent:this._host||this.vm,_linkerCachable:!this.inlineTemplate,_ref:this.descriptor.ref,_asComponent:!0,_isRouterView:this._isRouterView,_context:this.vm,_scope:this._scope,_frag:this._frag};
t&&_(i,t);var n=new this.Component(i);return this.keepAlive&&(this.cache[this.Component.cid]=n),n}},getCached:function(){return this.keepAlive&&this.cache[this.Component.cid]},unbuild:function(t){this.waitingFor&&(this.keepAlive||this.waitingFor.$destroy(),this.waitingFor=null);
var e=this.childVM;return!e||this.keepAlive?void(e&&(e._inactive=!0,e._updateRef(!0))):void e.$destroy(!1,t)},remove:function(t,e){var i=this.keepAlive;if(t){this.pendingRemovals++,this.pendingRemovalCb=e;
var n=this;t.$remove(function(){n.pendingRemovals--,i||t._cleanup(),!n.pendingRemovals&&n.pendingRemovalCb&&(n.pendingRemovalCb(),n.pendingRemovalCb=null)})}else e&&e()},transition:function(t,e){var i=this,n=this.childVM;
switch(n&&(n._inactive=!0),t._inactive=!1,this.childVM=t,i.params.transitionMode){case"in-out":t.$before(i.anchor,function(){i.remove(n,e)});break;case"out-in":i.remove(n,function(){t.$before(i.anchor,e)
});break;default:i.remove(n),t.$before(i.anchor,e)}},unbind:function(){if(this.invalidatePending(),this.unbuild(),this.cache){for(var t in this.cache)this.cache[t].$destroy();this.cache=null}}},Wo=Vr._propBindingModes,Bo={},Vo=/^[$_a-zA-Z]+[\w$]*$/,zo=Vr._propBindingModes,Uo={bind:function(){var t=this.vm,e=t._context,i=this.descriptor.prop,n=i.path,r=i.parentPath,s=i.mode===zo.TWO_WAY,o=this.parentWatcher=new ai(e,r,function(e){Wi(t,i,e)
},{twoWay:s,filters:i.filters,scope:this._scope});if(Mi(t,i,o.value),s){var a=this;t.$once("pre-hook:created",function(){a.childWatcher=new ai(t,n,function(t){o.set(t)},{sync:!0})})}},unbind:function(){this.parentWatcher.teardown(),this.childWatcher&&this.childWatcher.teardown()
}},Jo=[],qo=!1,Qo="transition",Go="animation",Zo=or+"Duration",Xo=hr+"Duration",Yo=Kn&&window.requestAnimationFrame,Ko=Yo?function(t){Yo(function(){Yo(t)})}:function(t){setTimeout(t,50)},ta=Qi.prototype;
ta.enter=function(t,e){this.cancelPending(),this.callHook("beforeEnter"),this.cb=e,pe(this.el,this.enterClass),t(),this.entered=!1,this.callHookWithCb("enter"),this.entered||(this.cancel=this.hooks&&this.hooks.enterCancelled,Ji(this.enterNextTick))
},ta.enterNextTick=function(){var t=this;this.justEntered=!0,Ko(function(){t.justEntered=!1});var e=this.enterDone,i=this.getCssTransitionType(this.enterClass);this.pendingJsCb?i===Qo&&de(this.el,this.enterClass):i===Qo?(de(this.el,this.enterClass),this.setupCssCb(ar,e)):i===Go?this.setupCssCb(lr,e):e()
},ta.enterDone=function(){this.entered=!0,this.cancel=this.pendingJsCb=null,de(this.el,this.enterClass),this.callHook("afterEnter"),this.cb&&this.cb()},ta.leave=function(t,e){this.cancelPending(),this.callHook("beforeLeave"),this.op=t,this.cb=e,pe(this.el,this.leaveClass),this.left=!1,this.callHookWithCb("leave"),this.left||(this.cancel=this.hooks&&this.hooks.leaveCancelled,this.op&&!this.pendingJsCb&&(this.justEntered?this.leaveDone():Ji(this.leaveNextTick)))
},ta.leaveNextTick=function(){var t=this.getCssTransitionType(this.leaveClass);if(t){var e=t===Qo?ar:lr;this.setupCssCb(e,this.leaveDone)}else this.leaveDone()},ta.leaveDone=function(){this.left=!0,this.cancel=this.pendingJsCb=null,this.op(),de(this.el,this.leaveClass),this.callHook("afterLeave"),this.cb&&this.cb(),this.op=null
},ta.cancelPending=function(){this.op=this.cb=null;var t=!1;this.pendingCssCb&&(t=!0,ce(this.el,this.pendingCssEvent,this.pendingCssCb),this.pendingCssEvent=this.pendingCssCb=null),this.pendingJsCb&&(t=!0,this.pendingJsCb.cancel(),this.pendingJsCb=null),t&&(de(this.el,this.enterClass),de(this.el,this.leaveClass)),this.cancel&&(this.cancel.call(this.vm,this.el),this.cancel=null)
},ta.callHook=function(t){this.hooks&&this.hooks[t]&&this.hooks[t].call(this.vm,this.el)},ta.callHookWithCb=function(t){var e=this.hooks&&this.hooks[t];e&&(e.length>1&&(this.pendingJsCb=k(this[t+"Done"])),e.call(this.vm,this.el,this.pendingJsCb))
},ta.getCssTransitionType=function(t){if(!(!ar||document.hidden||this.hooks&&this.hooks.css===!1||Gi(this.el))){var e=this.type||this.typeCache[t];if(e)return e;var i=this.el.style,n=window.getComputedStyle(this.el),r=i[Zo]||n[Zo];
if(r&&"0s"!==r)e=Qo;else{var s=i[Xo]||n[Xo];s&&"0s"!==s&&(e=Go)}return e&&(this.typeCache[t]=e),e}},ta.setupCssCb=function(t,e){this.pendingCssEvent=t;var i=this,n=this.el,r=this.pendingCssCb=function(s){s.target===n&&(ce(n,t,r),i.pendingCssEvent=i.pendingCssCb=null,!i.pendingJsCb&&e&&e())
};le(n,t,r)};var ea={priority:io,update:function(t,e){var i=this.el,n=Fe(this.vm.$options,"transitions",t);t=t||"v",e=e||"v",i.__v_trans=new Qi(i,t,n,this.vm),de(i,e+"-transition"),pe(i,t+"-transition")
}},ia={style:Oo,"class":Io,component:Mo,prop:Uo,transition:ea},na=/^v-bind:|^:/,ra=/^v-on:|^@/,sa=/^v-([^:]+)(?:$|:(.*)$)/,oa=/\.[^\.]+/g,aa=/^(v-bind:|:)?transition$/,ha=1e3,la=2e3;vn.terminal=!0;var ca=/[^\w\-:\.]/,ua=Object.freeze({compile:Zi,compileAndLinkProps:en,compileRoot:nn,transclude:Cn,resolveSlots:An}),fa=/^v-on:|^@/;
En.prototype._bind=function(){var t=this.name,e=this.descriptor;if(("cloak"!==t||this.vm._isCompiled)&&this.el&&this.el.removeAttribute){var i=e.attr||"v-"+t;this.el.removeAttribute(i)}var n=e.def;if("function"==typeof n?this.update=n:_(this,n),this._setupParams(),this.bind&&this.bind(),this._bound=!0,this.literal)this.update&&this.update(e.raw);
else if((this.expression||this.modifiers)&&(this.update||this.twoWay)&&!this._checkStatement()){var r=this;this._update=this.update?function(t,e){r._locked||r.update(t,e)}:jn;var s=this._preProcess?m(this._preProcess,this):null,o=this._postProcess?m(this._postProcess,this):null,a=this._watcher=new ai(this.vm,this.expression,this._update,{filters:this.filters,twoWay:this.twoWay,deep:this.deep,preProcess:s,postProcess:o,scope:this._scope});
this.afterBind?this.afterBind():this.update&&this.update(a.value)}},En.prototype._setupParams=function(){if(this.params){var t=this.params;this.params=Object.create(null);for(var e,i,n,r=t.length;r--;)e=d(t[r]),n=f(e),i=ie(this.el,e),null!=i?this._setupParamWatcher(n,i):(i=ee(this.el,e),null!=i&&(this.params[n]=""===i?!0:i))
}},En.prototype._setupParamWatcher=function(t,e){var i=this,n=!1,r=(this._scope||this.vm).$watch(e,function(e,r){if(i.params[t]=e,n){var s=i.paramWatchers&&i.paramWatchers[t];s&&s.call(i,e,r)}else n=!0
},{immediate:!0,user:!1});(this._paramUnwatchFns||(this._paramUnwatchFns=[])).push(r)},En.prototype._checkStatement=function(){var t=this.expression;if(t&&this.acceptStatement&&!ii(t)){var e=ei(t).get,i=this._scope||this.vm,n=function(t){i.$event=t,e.call(i,i),i.$event=null
};return this.filters&&(n=i._applyFilters(n,null,this.filters)),this.update(n),!0}},En.prototype.set=function(t){this.twoWay&&this._withLock(function(){this._watcher.set(t)})},En.prototype._withLock=function(t){var e=this;
e._locked=!0,t.call(e),fr(function(){e._locked=!1})},En.prototype.on=function(t,e,i){le(this.el,t,e,i),(this._listeners||(this._listeners=[])).push([t,e])},En.prototype._teardown=function(){if(this._bound){this._bound=!1,this.unbind&&this.unbind(),this._watcher&&this._watcher.teardown();
var t,e=this._listeners;if(e)for(t=e.length;t--;)ce(this.el,e[t][0],e[t][1]);var i=this._paramUnwatchFns;if(i)for(t=i.length;t--;)i[t]();this.vm=this.el=this._watcher=this._listeners=null}};var pa=/[^|]\|[^|]/;
We(Hn),Tn(Hn),Nn(Hn),Sn(Hn),Fn(Hn),Dn(Hn),Pn(Hn),Rn(Hn),Ln(Hn);var da={priority:ho,params:["name"],bind:function(){var t=this.params.name||"default",e=this.vm._slotContents&&this.vm._slotContents[t];e&&e.hasChildNodes()?this.compile(e.cloneNode(!0),this.vm._context,this.vm):this.fallback()
},compile:function(t,e,i){if(t&&e){if(this.el.hasChildNodes()&&1===t.childNodes.length&&1===t.childNodes[0].nodeType&&t.childNodes[0].hasAttribute("v-if")){var n=document.createElement("template");n.setAttribute("v-else",""),n.innerHTML=this.el.innerHTML,n._context=this.vm,t.appendChild(n)
}var r=i?i._scope:this._scope;this.unlink=e.$compile(t,i,r,this._frag)}t?he(this.el,t):oe(this.el)},fallback:function(){this.compile(ve(this.el,!0),this.vm)},unbind:function(){this.unlink&&this.unlink()
}},va={priority:so,params:["name"],paramWatchers:{name:function(t){uo.remove.call(this),t&&this.insert(t)}},bind:function(){this.anchor=ye("v-partial"),he(this.el,this.anchor),this.insert(this.params.name)
},insert:function(t){var e=Fe(this.vm.$options,"partials",t,!0);e&&(this.factory=new wi(this.vm,e),uo.insert.call(this))},unbind:function(){this.frag&&this.frag.destroy()}},ma={slot:da,partial:va},ga=co._postProcess,_a=/(\d{3})(?=\d)/g,ya={orderBy:Wn,filterBy:Mn,limitBy:In,json:{read:function(t,e){return"string"==typeof t?t:JSON.stringify(t,null,arguments.length>1?e:2)
},write:function(t){try{return JSON.parse(t)}catch(e){return t}}},capitalize:function(t){return t||0===t?(t=t.toString(),t.charAt(0).toUpperCase()+t.slice(1)):""},uppercase:function(t){return t||0===t?t.toString().toUpperCase():""
},lowercase:function(t){return t||0===t?t.toString().toLowerCase():""},currency:function(t,e,i){if(t=parseFloat(t),!isFinite(t)||!t&&0!==t)return"";e=null!=e?e:"$",i=null!=i?i:2;var n=Math.abs(t).toFixed(i),r=i?n.slice(0,-1-i):n,s=r.length%3,o=s>0?r.slice(0,s)+(r.length>3?",":""):"",a=i?n.slice(-1-i):"",h=0>t?"-":"";
return h+e+o+r.slice(s).replace(_a,"$1,")+a},pluralize:function(t){var e=g(arguments,1),i=e.length;if(i>1){var n=t%10-1;return n in e?e[n]:e[i-1]}return e[0]+(1===t?"":"s")},debounce:function(t,e){return t?(e||(e=300),C(t,e)):void 0
}};Vn(Hn),Hn.version="1.0.28",setTimeout(function(){Vr.devtools&&tr&&tr.emit("init",Hn)},0),i.exports=Hn});
;define("hiloan:node_modules/es6-promise/dist/es6-promise",function(t,e,n){var r=t("hiloan:node_modules/process/browser"),o="undefined"!=typeof o?o:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};
!function(t,r){"object"==typeof e&&"undefined"!=typeof n?n.exports=r():"function"==typeof define&&define.amd?define(r):t.ES6Promise=r()}(this,function(){"use strict";function e(t){var e=typeof t;return null!==t&&("object"===e||"function"===e)
}function n(t){return"function"==typeof t}function i(t){R=t}function s(t){V=t}function u(){return function(){return r.nextTick(h)}}function c(){return"undefined"!=typeof Q?function(){Q(h)}:l()}function a(){var t=0,e=new $(h),n=document.createTextNode("");
return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}function f(){var t=new MessageChannel;return t.port1.onmessage=h,function(){return t.port2.postMessage(0)}}function l(){var t=setTimeout;
return function(){return t(h,1)}}function h(){for(var t=0;J>t;t+=2){var e=ne[t],n=ne[t+1];e(n),ne[t]=void 0,ne[t+1]=void 0}J=0}function d(){try{var e=t,n=e("vertx");return Q=n.runOnLoop||n.runOnContext,c()
}catch(r){return l()}}function p(t,e){var n=arguments,r=this,o=new this.constructor(_);void 0===o[oe]&&D(o);var i=r._state;return i?!function(){var t=n[i-1];V(function(){return k(i,o,t,r._result)})}():P(r,o,t,e),o
}function v(t){var e=this;if(t&&"object"==typeof t&&t.constructor===e)return t;var n=new e(_);return j(n,t),n}function _(){}function y(){return new TypeError("You cannot resolve a promise with itself")
}function m(){return new TypeError("A promises callback cannot return that same promise.")}function w(t){try{return t.then}catch(e){return ce.error=e,ce}}function b(t,e,n,r){try{t.call(e,n,r)}catch(o){return o
}}function g(t,e,n){V(function(t){var r=!1,o=b(n,e,function(n){r||(r=!0,e!==n?j(t,n):T(t,n))},function(e){r||(r=!0,M(t,e))},"Settle: "+(t._label||" unknown promise"));!r&&o&&(r=!0,M(t,o))},t)}function A(t,e){e._state===se?T(t,e._result):e._state===ue?M(t,e._result):P(e,void 0,function(e){return j(t,e)
},function(e){return M(t,e)})}function S(t,e,r){e.constructor===t.constructor&&r===p&&e.constructor.resolve===v?A(t,e):r===ce?(M(t,ce.error),ce.error=null):void 0===r?T(t,e):n(r)?g(t,e,r):T(t,e)}function j(t,n){t===n?M(t,y()):e(n)?S(t,n,w(n)):T(t,n)
}function E(t){t._onerror&&t._onerror(t._result),C(t)}function T(t,e){t._state===ie&&(t._result=e,t._state=se,0!==t._subscribers.length&&V(C,t))}function M(t,e){t._state===ie&&(t._state=ue,t._result=e,V(E,t))
}function P(t,e,n,r){var o=t._subscribers,i=o.length;t._onerror=null,o[i]=e,o[i+se]=n,o[i+ue]=r,0===i&&t._state&&V(C,t)}function C(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r=void 0,o=void 0,i=t._result,s=0;s<e.length;s+=3)r=e[s],o=e[s+n],r?k(n,r,o,i):o(i);
t._subscribers.length=0}}function O(){this.error=null}function x(t,e){try{return t(e)}catch(n){return ae.error=n,ae}}function k(t,e,r,o){var i=n(r),s=void 0,u=void 0,c=void 0,a=void 0;if(i){if(s=x(r,o),s===ae?(a=!0,u=s.error,s.error=null):c=!0,e===s)return void M(e,m())
}else s=o,c=!0;e._state!==ie||(i&&c?j(e,s):a?M(e,u):t===se?T(e,s):t===ue&&M(e,s))}function Y(t,e){try{e(function(e){j(t,e)},function(e){M(t,e)})}catch(n){M(t,n)}}function F(){return fe++}function D(t){t[oe]=fe++,t._state=void 0,t._result=void 0,t._subscribers=[]
}function K(t,e){this._instanceConstructor=t,this.promise=new t(_),this.promise[oe]||D(this.promise),I(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?T(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&T(this.promise,this._result))):M(this.promise,L())
}function L(){return new Error("Array Methods must be provided an Array")}function N(t){return new K(this,t).promise}function U(t){var e=this;return new e(I(t)?function(n,r){for(var o=t.length,i=0;o>i;i++)e.resolve(t[i]).then(n,r)
}:function(t,e){return e(new TypeError("You must pass an array to race."))})}function W(t){var e=this,n=new e(_);return M(n,t),n}function q(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")
}function z(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function B(t){this[oe]=F(),this._result=this._state=void 0,this._subscribers=[],_!==t&&("function"!=typeof t&&q(),this instanceof B?Y(this,t):z())
}function G(){var t=void 0;if("undefined"!=typeof o)t=o;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")
}var n=t.Promise;if(n){var r=null;try{r=Object.prototype.toString.call(n.resolve())}catch(e){}if("[object Promise]"===r&&!n.cast)return}t.Promise=B}var H=void 0;H=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)
};var I=H,J=0,Q=void 0,R=void 0,V=function(t,e){ne[J]=t,ne[J+1]=e,J+=2,2===J&&(R?R(h):re())},X="undefined"!=typeof window?window:void 0,Z=X||{},$=Z.MutationObserver||Z.WebKitMutationObserver,te="undefined"==typeof self&&!1&&"[object process]"==={}.toString.call(r),ee="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,ne=new Array(1e3),re=void 0;
re=te?u():$?a():ee?f():void 0===X&&"function"==typeof t?d():l();var oe=Math.random().toString(36).substring(16),ie=void 0,se=1,ue=2,ce=new O,ae=new O,fe=0;return K.prototype._enumerate=function(t){for(var e=0;this._state===ie&&e<t.length;e++)this._eachEntry(t[e],e)
},K.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,r=n.resolve;if(r===v){var o=w(t);if(o===p&&t._state!==ie)this._settledAt(t._state,e,t._result);else if("function"!=typeof o)this._remaining--,this._result[e]=t;
else if(n===B){var i=new n(_);S(i,t,o),this._willSettleAt(i,e)}else this._willSettleAt(new n(function(e){return e(t)}),e)}else this._willSettleAt(r(t),e)},K.prototype._settledAt=function(t,e,n){var r=this.promise;
r._state===ie&&(this._remaining--,t===ue?M(r,n):this._result[e]=n),0===this._remaining&&T(r,this._result)},K.prototype._willSettleAt=function(t,e){var n=this;P(t,void 0,function(t){return n._settledAt(se,e,t)
},function(t){return n._settledAt(ue,e,t)})},B.all=N,B.race=U,B.resolve=v,B.reject=W,B._setScheduler=i,B._setAsap=s,B._asap=V,B.prototype={constructor:B,then:p,"catch":function(t){return this.then(null,t)
}},B.polyfill=G,B.Promise=B,B})});
;define("hiloan:node_modules/ua-device/lib/useragent-base",function(e,a,i){i.exports=function(){function e(e){return e="undefined"==typeof e?"":e,e=e.replace(/_TD$/,""),e=e.replace(/_CMCC$/,""),e=e.replace(/_/g," "),e=e.replace(/^\s+|\s+$/g,""),e=e.replace(/\/[^/]+$/,""),e=e.replace(/\/[^/]+ Android\/.*/,""),e=e.replace(/^tita on /,""),e=e.replace(/^Android on /,""),e=e.replace(/^Android for /,""),e=e.replace(/^ICS AOSP on /,""),e=e.replace(/^Full AOSP on /,""),e=e.replace(/^Full Android on /,""),e=e.replace(/^Full Cappuccino on /,""),e=e.replace(/^Full MIPS Android on /,""),e=e.replace(/^Full Android/,""),e=e.replace(/^Acer ?/i,""),e=e.replace(/^Iconia /,""),e=e.replace(/^Ainol /,""),e=e.replace(/^Coolpad ?/i,"Coolpad "),e=e.replace(/^ALCATEL /,""),e=e.replace(/^Alcatel OT-(.*)/,"one touch $1"),e=e.replace(/^YL-/,""),e=e.replace(/^Novo7 ?/i,"Novo7 "),e=e.replace(/^GIONEE /,""),e=e.replace(/^HW-/,""),e=e.replace(/^Huawei[ -]/i,"Huawei "),e=e.replace(/^SAMSUNG[ -]/i,""),e=e.replace(/^SonyEricsson/,""),e=e.replace(/^Lenovo Lenovo/,"Lenovo"),e=e.replace(/^LNV-Lenovo/,"Lenovo"),e=e.replace(/^Lenovo-/,"Lenovo "),e=e.replace(/^(LG)[ _\/]/,"$1-"),e=e.replace(/^(HTC.*)\s(?:v|V)?[0-9.]+$/,"$1"),e=e.replace(/^(HTC)[-\/]/,"$1 "),e=e.replace(/^(HTC)([A-Z][0-9][0-9][0-9])/,"$1 $2"),e=e.replace(/^(Motorola[\s|-])/,""),e=e.replace(/^(Moto|MOT-)/,""),e=e.replace(/-?(orange(-ls)?|vodafone|bouygues)$/i,""),e=e.replace(/http:\/\/.+$/i,""),e=e.replace(/^\s+|\s+$/g,"")
}function a(e){e=e.toString();var a=e.split("."),i=a.shift();return parseFloat(i+"."+a.join(""))}var i="Samsung",o="Sharp",t="Sony Ericsson",s="Motorola",n="LG",r="Huawei",l="HTC",c="Coolpad",m="Asus",T="Acer",h={SAMSUNG:{"GT-S3370C":[i,"Corby 3G"],"GT-S3650":[i,"Corby"],"GT-S3653":[i,"Corby"],"GT-S3850":[i,"Corby II"],"GT-S5230":[i,"Star"],"GT-S5230W":[i,"Star"],"GT-S5233":[i,"Star"],"GT-S5260":[i,"Star II"],"GT-S5560":[i,"Marvel"],"GT-S5620":[i,"Monte"],"GT-S7550":[i,"Blue Earth"],"GT-S8000":[i,"Jet"],"GT-S8003":[i,"Jet"],"SGH-F480":[i,"Tocco"],"SGH-T528g":[i,"Straight Talk"],"GT-B3410":[i,"Star Qwerty"],"GT-B5310":[i,"Corby Pro"],"GT-B7722":[i,"Star Duos"],"GT-C6712":[i,"Star II Duos"]}},S={SAMSUNG:{"GT- S5250":[i,"Wave 525"],"GT-S5250":[i,"Wave 525"],"GT-S5253":[i,"Wave 525"],"GT-S5330":[i,"Wave 533"],"GT-S5380":[i,"Wave Y"],"GT-S5380D":[i,"Wave Y"],"GT-S5380K":[i,"Wave Y"],"GT-S5750E":[i,"Wave 575"],"GT-S5753E":[i,"Wave 575"],"GT-S7230B":[i,"Wave 723"],"GT-S7230E":[i,"Wave 723"],"GT-S7233E":[i,"Wave 723"],"GT-S7250":[i,"Wave M"],"GT-S7250D":[i,"Wave M"],"GT-S8500":[i,"Wave"],"GT-S8500C":[i,"Wave"],"GT-S8500R":[i,"Wave"],"GT-S8500T":[i,"Wave"],"GT-S8530":[i,"Wave II"],"GT-S8600":[i,"Wave 3"],"SHW-M410":[i,"Wave 3"]}},d={SAMSUNG:{"GT-I9500":[i,"GT-I9500"]}},u={"Coolpad D508":[c,"D508"],"Coolpad E600":[c,"E600"],"SCH-F839":[i,"SCH-F839"]},G={DX900:[T,"Tempo DX900"],F900:[T,"Tempo F900"],"Coolpad F800":[c,"F800"],"garmin-asus-Nuvifone-M10":["Garmin-Asus","Nuvifone M10"],"HP iPAQ 510":["HP","iPAQ 510"],"HD mini T5555":[l,"HD mini"],"HTC HD mini":[l,"HD mini"],"HTC HD mini T5555":[l,"HD mini"],"HTC HD2":[l,"HD2"],"HTC HD2 T8585":[l,"HD2"],"HD2 T8585":[l,"HD2"],"T-Mobile LEO":[l,"HD2"],dopodT5588:[l,"Hengshan"],"HTC Mega-T3333":[l,"Mega"],"HTC Snap S521":[l,"Snap"],"HTC Touch2 T3320":[l,"Touch 2"],"HTC Touch2 T3333":[l,"Touch 2"],"HTC Touch2 T3335":[l,"Touch 2"],"HTC P3700":[l,"Touch Diamond"],"HTC Touch Diamond2 T5353":[l,"Touch Diamond 2"],"HTC Touch HD T8282":[l,"Touch HD"],"HTC Touch HD T8283":[l,"Touch HD"],"HTC Touch HD2 T8585":[l,"Touch HD2"],"HTC Touch Pro2 T7373":[l,"Touch Pro 2"],T7380:[l,"Touch Pro 2"],"HTC TyTN II":[l,"TyTN II"],"GT-B7300":[i,"Omnia Lite"],"GT-B7610":[i,"Omnia Pro"],"GT-i8000":[i,"Omnia 2"],"GT-I8000":[i,"Omnia 2"],"GT-I8000U":[i,"Omnia 2"],M1i:[t,"M1i Aspen"]},b={Acer:{Allegro:[T,"Allegro"],M310:[T,"Allegro"]},Asus:{Galaxy6:[m,"Galaxy 6"]},DELL:{"Venue Pro":["Dell","Venue Pro"]},FujitsuToshibaMobileCommun:{IS12T:["Fujitsu Toshiba","IS12T"]},HTC:{"7 Mozart":[l,"7 Mozart"],"7 Mozart T8698":[l,"7 Mozart"],T8697:[l,"7 Mozart"],T8698:[l,"7 Mozart"],PD67100:[l,"7 Mozart"],"Mozart T8698":[l,"7 Mozart"],Mozart:[l,"7 Mozart"],"USCCHTC-PC93100":[l,"Arrive"],Gold:[l,"Gold "],HD2:[l,"HD2"],HD7:[l,"HD7"],"HD7 T9292":[l,"HD7"],T9295:[l,"HD7"],T9296:[l,"HD7"],"HD7 Infinity":[l,"HD7"],T7575:[l,"7 Pro"],"7 Pro T7576":[l,"7 Pro"],mwp6985:[l,"Trophy"],"7 Trophy T8686":[l,"Trophy"],"7 Trophy":[l,"Trophy"],PC40100:[l,"Trophy"],"Touch-IT Trophy":[l,"Trophy"],Radar:[l,"Radar"],"Radar 4G":[l,"Radar"],"Radar C110e":[l,"Radar"],Mazaa:[l,"Mazaa"],Mondrian:[l,"Mondrian"],Schubert:[l,"Schubert"],"7 Schubert T9292":[l,"Schubert"],Spark:[l,"Spark"],T8788:[l,"Surround"],"TITAN X310e":[l,"Titan"],X310e:[l,"Titan"],PI39100:[l,"Titan"],PI86100:[l,"Titan II"],Ultimate:[l,"Ultimate"]},LG:{GW910:[n,"Optimus 7"],"LG E-900":[n,"Optimus 7 E900"],"LG-E900":[n,"Optimus 7 E900"],"LG-E900h":[n,"Optimus 7 E900"],"LG-C900":[n,"Optimus 7Q"],"LG-C900B":[n,"Quantum"],"LG-C900k":[n,"Quantum"]},nokia:{SeaRay:["Nokia","Lumia 800"],"800C":["Nokia","Lumia 800"]},NOKIA:{710:["Nokia","Lumia 710"],"Nokia 710":["Nokia","Lumia 710"],"Lumia 710":["Nokia","Lumia 710"],"Lumia 719":["Nokia","Lumia 719"],"Lumia 800":["Nokia","Lumia 800"],800:["Nokia","Lumia 800"],"Lumia 900":["Nokia","Lumia 900"],XXX:["Nokia","prototype"]},SAMSUNG:{"GT-I8350":[i,"Omnia W"],"GT-I8350T":[i,"Omnia W"],"SGH-i677":[i,"Focus Flash"],"SGH-i707":[i,"Taylor"],"SGH-i917":[i,"Omnia 7"],"SGH-I917":[i,"Omnia 7"],"SGH-i917.":[i,"Focus"],"SGH-i917R":[i,"Focus"],"SGH-i937":[i,"Focus S"],OMNIA7:[i,"Omnia 7"],OMINA7:[i,"Omnia 7"],Taylor:[i,"Taylor"]},TOSHIBA:{TSUNAGI:["Toshiba","Tsunagi"]}},H={Android:[null,null],"google sdk":[null,null],sdk:[null,null],generic:[null,null],"generic x86":[null,null],"amd brazos":["AMD","Fusionbased device"],"Amlogic M1 reference board":["Amlogic","M1 reference board"],AML8726M:["Amlogic","AML8726-Mbased device"],"vexpress a9":["ARM","Versatile Express development platform"],bcm7231:["Broadcom","BCM7231based device","television"],bcm7425:["Broadcom","BCM7425based device","television"],bcm7429:["Broadcom","BCM7429based device","television"],"imx50 rdp":["Freescale","i.MX50based device"],"imx51 bbg":["Freescale","i.MX51based device"],"imx53 loco":["Freescale","i.MX53based device"],"imx53 mp204f3":["Freescale","i.MX53based device"],"imx53 smd":["Freescale","i.MX53based device"],"imx53 yeagle":["Freescale","i.MX53based device"],imx6q:["Freescale","i.MX6Qbased device"],"ODROID-A":["Hardkernel","ODROID-A developer tablet","tablet"],"mfld dv10":["Intel","Medfieldbased device"],"mfld dv20":["Intel","Medfieldbased device"],"mfld lw00":["Intel","Medfieldbased device"],"mfld pr2":["Intel","Medfieldbased device"],"mfld pr3":["Intel","Medfieldbased device"],"berlin bg2":["Marvell","Armada 1000based device","television"],"MStar Amber3":["MStar","Amber3based device"],"Konka Amber3":["MStar","Amber3based device"],mt5396:["Mediatek","MT5396based device","television"],bird75v2:["Mediatek","MT6575based device"],"eagle75v1 2":["Mediatek","MT6575based device"],"MBX DVBT reference board (c03ref)":["MXB","DVBT reference board","television"],NS2816:["Nufront","NuSmart 2816based device"],Ventana:["nVidia","Tegra Ventana development kit"],Cardhu:["nVidia","Tegra 3based device"],Panda:["Pandaboard","Development Kit"],pandaboard:["Pandaboard","Development Kit"],PandaBoard:["Pandaboard","Development Kit"],MSM:["Qualcomm","Snapdragonbased device"],"msm7227 ffa":["Qualcomm","Snapdragon S1based device"],"msm7627 surf":["Qualcomm","Snapdragon S1based device"],msm7627a:["Qualcomm","Snapdragon S1based device"],"msm7627a sku1":["Qualcomm","Snapdragon S1based device"],"msm7627a sku3":["Qualcomm","Snapdragon S1based device"],"msm7630 fusion":["Qualcomm","Snapdragon S2based device"],"msm7630 surf":["Qualcomm","Snapdragon S2based device"],"msm8660 cougar":["Qualcomm","Snapdragon S3based device"],"msm8660 surf":["Qualcomm","Snapdragon S3based device"],msm8960:["Qualcomm","Snapdragon S4based device"],rk2808sdk:["Rockchip","RK2808based device"],RK2818:["Rockchip","RK2818based device"],rk2818sdk:["Rockchip","RK2818based device"],"Android-for-Rockchip-2818":["Rockchip","RK2818based device"],rk29sdk:["Rockchip","RK29based device"],Rk29sdk:["Rockchip","RK29based device"],rk30sdk:["Rockchip","RK30based device"],s3c6410:["Samsung","S3C6410based device"],smdk6410:["Samsung","S3C6410based device"],SMDKC110:["Samsung","Exynos 3110based device"],SMDKV210:["Samsung","Exynos 4210based device"],S5PV210:["Samsung","Exynos 4210based device"],"sec smdkc210":["Samsung","Exynos 4210based device"],SMDK4x12:["Samsung","Exynos 4212 or 4412based device"],smp86xx:["Sigma","SMP86xxbased device","television"],sv8860:["Skyviia","SV8860based device","television"],"ste u8500":["ST Ericsson","Novathor U8500based device"],"Telechips M801 Evaluation Board":["Telechips","M801based device","television"],"Telechips TCC8900 Evaluation Board":["Telechips","TCC8900based device","television"],"TCC8920 STB EV":["Telechips","TCC8920based device","television"],OMAP:["Texas Instruments","OMAPbased device"],"OMAP SS":["Texas Instruments","OMAPbased device"],"LogicPD Zoom2":["Texas Instruments","OMAPbased device"],omap3evm:["Texas Instruments","OMAP3based device"],Omap5sevm:["Texas Instruments","OMAP5based device"],"pnx8473 kiryung":["Trident","PNX8473based device","television"],crespo:["Google","Nexus S"],Crespo:["Google","Nexus S"],Crespo4G:["Google","Nexus S"],Passion:["Google","Nexus One"],Bravo:["HTC","Desire"],dream:["HTC","Dream"],Vogue:["HTC","Touch"],"Vendor Optimus":["LG","Optimus"],Stingray:["Motorola","XOOM","tablet"],Wingray:["Motorola","XOOM","tablet"],maguro:["Samsung","Galaxy Nexus"],Maguro:["Samsung","Galaxy Nexus"],"Toro-VZW":["Samsung","Galaxy Nexus"],blaze:["Texas Instruments","Blaze Tablet","tablet"],Blaze:["Texas Instruments","Blaze Tablet","tablet"],"Blaze Tablet":["Texas Instruments","Blaze Tablet","tablet"],BlueStacks:["BlueStacks","App Player","desktop"],"youwave custom":["Youwave","Android on PC","desktop"],A100:["Acer","Iconia Tab A100","tablet"],A101:["Acer","Iconia Tab A101","tablet"],A200:["Acer","Iconia Tab A200","tablet"],A500:["Acer","Iconia Tab A500","tablet"],A501:["Acer","Iconia Tab A501","tablet"],A510:["Acer","Iconia Tab A510","tablet"],A511:["Acer","Iconia Tab A511","tablet"],A700:["Acer","Iconia Tab A700","tablet"],"Acer A800":["Acer","Iconia Tab A800","tablet"],E110:["Acer","beTouch E110"],E120:["Acer","beTouch E120"],E130:["Acer","beTouch E130"],E140:["Acer","beTouch E140"],E210:["Acer","beTouch E210"],E310:["Acer","Liquid mini"],E320:["Acer","Liquid Express"],E330:["Acer","Liquid Glow"],E400:["Acer","beTouch E400"],G100W:["Acer","G100W"],S100:["Acer","Liquid"],S110:["Acer","Stream"],S120:["Acer","Liquid mt"],S300:["Acer","Iconia Smart"],S500:["Acer","CloudMobile"],TD600:["Acer","beTouch TD600"],Liquid:["Acer","Liquid"],"Liquid E":["Acer","Liquid E"],"Liquid Mt":["Acer","Liquid mt"],"Liquid MT":["Acer","Liquid mt"],"Liquid Metal":["Acer","Liquid mt"],Stream:["Acer","Stream"],N700:["aigo","N700","tablet"],M801:["aigo","M801","tablet"],Novo7:["Ainovo","Novo7","tablet"],"Novo7 Aurora":["Ainovo","Novo7 Aurora","tablet"],"Novo7 Advanced":["Ainovo","Novo7 Advanced","tablet"],"Novo7 Advanced2":["Ainovo","Novo7 Advanced 2","tablet"],"Novo7 Basic":["Ainovo","Novo7 Basic","tablet"],"Novo7 ELF":["Ainovo","Novo7 Elf","tablet"],"Novo7 PALADIN":["Ainovo","Novo7 Paladin","tablet"],"Novo8 Advanced":["Ainovo","Novo8 Advanced","tablet"],"one touch 890":["Alcatel","One Touch 890"],"one touch 890D":["Alcatel","One Touch 890"],"one touch 891":["Alcatel","One Touch 891"],"ONE TOUCH 903":["Alcatel","One Touch 903SHV-E170K"],"one touch 906":["Alcatel","One Touch 906"],"one touch 908":["Alcatel","One Touch 908"],"one touch 908F":["Alcatel","One Touch 908"],"one touch 908S":["Alcatel","One Touch 908"],"one touch 910":["Alcatel","One Touch 910"],"one touch 918":["Alcatel","One Touch 918"],"one touch 918D":["Alcatel","One Touch 918"],"ONE TOUCH 918D":["Alcatel","One Touch 918"],"one touch 918M":["Alcatel","One Touch 918"],"one touch 918N":["Alcatel","One Touch 918"],"one touch 980":["Alcatel","One Touch 980"],"one touch 980A":["Alcatel","One Touch 980"],"one touch 981A":["Alcatel","One Touch 981"],"one touch 986":["Alcatel","One Touch 986"],"one touch 990":["Alcatel","One Touch 990"],"one touch 990A":["Alcatel","One Touch 990"],"one touch 991":["Alcatel","One Touch 991"],"one touch 991D":["Alcatel","One Touch 991"],"ONE TOUCH 993":["Alcatel","One Touch 993"],"one touch 995":["Alcatel","One Touch 995"],"Telenor OneTouch":["Alcatel","One Touch 990"],"OT 918":["Alcatel","One Touch 918"],Venture:["Alcatel","Venture"],"Allwinner A10":["AllWinner","A10","tablet"],"97FC":["AllWinner","A10 97FC","tablet"],"Kindle Fire":["Amazon","Kindle Fire","tablet"],"Amazon Kindle Fire":["Amazon","Kindle Fire","tablet"],AMD120:["AnyDATA","AnyTAB AMD120","tablet"],MW0811:["AOC","Breeze MW0811","tablet"],"MW0821 V2.0":["AOC","Breeze MW0821","tablet"],MW0922:["AOC","Breeze MW0922","tablet"],"Apanda A60":["Apanda","A60"],"apanda-A60":["Apanda","A60"],A80KSC:["Archos","Arnova 8","tablet"],AN7CG2:["Archos","Arnova 7","tablet"],A101B:["Archos","Arnova 10","tablet"],AN10BG2DT:["Archos","Arnova 10 B","tablet"],AN10G2:["Archos","Arnova 10 G2","tablet"],A32:["Archos","32","media"],A35DE:["Archos","35 Smart Home Phone"],A43:["Archos","43","media"],Archos5:["Archos","5","media"],A70H:["Archos","7","tablet"],A70HB:["Archos","7","tablet"],A70BHT:["Archos","7","tablet"],A70CHT:["Archos","7C","tablet"],A70S:["Archos","70","tablet"],A7EB:["Archos","70B","tablet"],"ARCHOS 70it2":["Archos","70 IT 2","tablet"],"ARCHOS 80G9":["Archos","80 G9","tablet"],"ARCHOS 101G9":["Archos","101 G9","tablet"],A101IT:["Archos","101 IT","tablet"],ASTRI:["ASTRI","e-reader","ereader"],eeepc:["Asus","Eee Pc"],"asus laptop":["Asus","Eee Pc"],ME171:["Asus","Eee Pad MeMO","tablet"],"Slider SL101":["Asus","Eee Pad Slider","tablet"],EPAD:["Asus","Eee Pad Transformer","tablet"],TF101:["Asus","Eee Pad Transformer","tablet"],"Transformer TF101":["Asus","Eee Pad Transformer","tablet"],"Transformer TF101G":["Asus","Eee Pad Transformer","tablet"],TF201:["Asus","Eee Pad Transformer Prime","tablet"],"Transformer Prime TF201":["Asus","Eee Pad Transformer Prime","tablet"],"Transformer Prime":["Asus","Eee Pad Transformer Prime","tablet"],"Transformer Pad TF300T":["Asus","Transformer Pad 300","tablet"],"ASUS Transformer TF300T":["Asus","Transformer Pad 300","tablet"],"ASUS Transformer Pad TF300T":["Asus","Transformer Pad 300","tablet"],"ASUS Transformer Pad TF300TG":["Asus","Transformer Pad 300","tablet"],"ASUS Transformer Pad TF700T":["Asus","Transformer Pad Infinity 700","tablet"],"ASUS Transformer Pad TF700K":["Asus","Transformer Pad Infinity 700","tablet"],"ASUS Transformer TF700K":["Asus","Transformer Pad Infinity 700","tablet"],PadFone:["Asus","Padfone","tablet"],"OMS TTD":["Asus","Eee Pc T10"],"ASUS T20":["Asus","Eee Pc T20"],ETBW11AA:["Asus","Tough"],"AUX V900":["AUX","V900"],M910A:["AUX","M910"],"PICOpad-QGN":["Axioo","Picopad QGN","tablet"],NOOK:["Barnes & Noble","NOOK","ereader"],NookColor:["Barnes & Noble","NOOK Color","ereader"],"NOOK BNRV200":["Barnes & Noble","NOOK Color","ereader"],"NOOK BNRV300":["Barnes & Noble","NOOK Color","ereader"],NookTablet:["Barnes & Noble","NOOK Tablet","ereader"],"Nook Tablet":["Barnes & Noble","NOOK Tablet","ereader"],"NOOK BNTV250":["Barnes & Noble","NOOK Tablet","ereader"],"NOOK BNTV250A":["Barnes & Noble","NOOK Tablet","ereader"],BNTV250:["Barnes & Noble","NOOK Tablet","ereader"],BNTV250A:["Barnes & Noble","NOOK Tablet","ereader"],"NOOK Slate":["Barnes & Noble","NOOK Tablet","ereader"],"BenWee 5100":["BenWee","5100"],CA907AAC0G:["Besta","CA907AAC0G"],BM999:["Bmorn","BM999","tablet"],V11:["Bmorn","V11","tablet"],V99:["Bmorn","V99","tablet"],"bq DaVinci":["bq","DaVinci","tablet"],CT704:["Carrefour","CT704","tablet"],CT1002:["Carrefour","CT1002","tablet"],"Camangi-Mangrove7":["Camangi","Mangrove 7","tablet"],WS171:["Camangi","WebStation","tablet"],IS11CA:["Casio","GzOne IS11CA"],C771:["Casio","GzOne Commando"],"CAT NOVA":["Cat","NOVA","tablet"],ARMM3V:["chinaleap","ARMM3V","tablet"],"CIUS-7":["Cisco","Cius","tablet"],"CIUS-7-AT":["Cisco","Cius","tablet"],"CSL Spice MI300":["CSL","Spice MI300"],"CSL-MI410":["CSL","Spice MI410"],MID1024:["Coby","Kyros MID1024","tablet"],MID1125:["Coby","Kyros MID1125","tablet"],MID1126:["Coby","Kyros MID1126","tablet"],MID7010:["Coby","Kyros MID7010","tablet"],MID7012:["Coby","Kyros MID7012","tablet"],MID7015:["Coby","Kyros MID7015","tablet"],MID7015A:["Coby","Kyros MID7015","tablet"],MID7016:["Coby","Kyros MID7016","tablet"],MID7020:["Coby","Kyros MID7020","tablet"],MID7022:["Coby","Kyros MID7022","tablet"],MID7024:["Coby","Kyros MID7024","tablet"],MID7025:["Coby","Kyros MID7025","tablet"],MID7127:["Coby","Kyros MID7127","tablet"],MID8024:["Coby","Kyros MID8024","tablet"],MID8125:["Coby","Kyros MID8125","tablet"],MID8127:["Coby","Kyros MID8127","tablet"],Z71:["Commtiva","Z71"],"V-T100":["Commtiva","V-T100"],"FIH-FB0":["Commtiva","HD700"],"Coolpad D510":["Coolpad","D510"],"Coolpad 8020":["Coolpad","8020"],D530:["Coolpad","D530"],"Coolpad D530":["Coolpad","D530"],D539:["Coolpad","D539"],"Coolpad D539":["Coolpad","D539"],E239:["Coolpad","E239"],"Coolpad E239":["Coolpad","E239"],"Coolpad N930":["Coolpad","N930"],N930:["Coolpad","N930"],"Coolpad W706":["Coolpad","W706"],"Coolpad W706+":["Coolpad","W706"],"Coolpad W708":["Coolpad","W708"],W711:["Coolpad","W711"],"Coolpad 5010":["Coolpad","5010"],"Coolpad 5210":["Coolpad","5210"],"Coolpad 5820":["Coolpad","5820"],5832:["Coolpad","5832"],"Coolpad 5832":["Coolpad","5832"],5855:["Coolpad","5855"],"Coolpad 5860":["Coolpad","5860"],"Coolpad 5860+":["Coolpad","5860"],"Coolpad 5860s":["Coolpad","5860"],5860:["Coolpad","5860"],"5860A":["Coolpad","5860"],"Coolpad 5870":["Coolpad","5870"],5870:["Coolpad","5870"],"Coolpad 7005":["Coolpad","7005"],7260:["Coolpad","7260"],"Coolpad 7019":["Coolpad","7019"],"Coolpad 7260":["Coolpad","7260"],"Coolpad 8013":["Coolpad","8013"],"Coolpad 8809":["Coolpad","8809"],"Coolpad 8810":["Coolpad","8810"],8810:["Coolpad","8810"],8150:["Coolpad","8150"],"Coolpad 8150D":["Coolpad","8150"],"Coolpad 8811":["Coolpad","8811"],"Coolpad 9900":["Coolpad","9900"],"Coolpad 8050":["Coolpad","8050"],ZiiO7:["Creative","ZiiO 7","tablet"],"ZiiLABS ZiiO7":["Creative","ZiiO 7","tablet"],"ZiiLABS ZiiO10 ":["Creative","ZiiO 10","tablet"],"CUBE K8GT A":["Cube","K8GT A","tablet"],"CUBE K8GT B":["Cube","K8GT B","tablet"],"K8GT C":["Cube","K8GT C","tablet"],"K8GT H":["Cube","K8GT H","tablet"],"CUBE K8GT H":["Cube","K8GT H","tablet"],"K8GT W":["Cube","K8GT W","tablet"],"CUBE U8GT":["Cube","U8GT","tablet"],"CUBE U9GT":["Cube","U9GT","tablet"],"CUBE U9GT 2":["Cube","U9GT 2","tablet"],"Cube U9GT2":["Cube","U9GT 2","tablet"],U9GT:["Cube","U9GT","tablet"],"U9GT2 From moage.com":["Cube","U9GT 2","tablet"],"N90 From moage.com":["Cube","U9GT 2","tablet"],"U9GT S":["Cube","U9GT S","tablet"],"U9GT S A":["Cube","U9GT SA","tablet"],"U9GTS A":["Cube","U9GT SA","tablet"],"U10GT 2":["Cube","U10GT 2","tablet"],"U10GT S":["Cube","U10GT S","tablet"],"U30GT-H":["Cube","U30GT H","tablet"],"CUBE Q7PRO":["Cube","Q7 Pro","tablet"],"CUBE Q7PRO J":["Cube","Q7 Pro","tablet"],"Cydle M7 (v0005.04.03.12.ko)":["Cydle","M7 MultiPAD","tablet"],"Dell Aero":["Dell","Aero"],"Dell M01M":["Dell","Mini 5","tablet"],"Dell Streak":["Dell","Streak","tablet"],"001DL":["Dell","Streak","tablet"],"101DL":["Dell","Streak Pro","tablet"],GS01:["Dell","Streak Pro","tablet"],"Dell Streak Pro":["Dell","Streak Pro","tablet"],streak7:["Dell","Streak 7","tablet"],"Dell Streak 7":["Dell","Streak 7","tablet"],"Dell Streak 10 Pro":["Dell","Streak 10 Pro","tablet"],"Dell V04B":["Dell","Streak V04B","tablet"],"Dell Venue":["Dell","Venue"],"Dell XCD35":["Dell","XCD35"],XCD35:["Dell","XCD35"],iDx7:["Digma","iDx7","tablet"],iDx10:["Digma","iDx10","tablet"],"iDx10 3G":["Digma","iDx10","tablet"],DM009SH:["Disney Mobile","DM009SH"],DM010SH:["Disney Mobile","DM010SH"],DM012SH:["Disney Mobile","DM012SH"],"F-08D":["Disney Mobile","F-08D"],"P-05D":["Disney Mobile","P-05D"],"Tablet-P27":["DracoTek","P27 Tablet","tablet"],edgejr:["EnTourage","Pocket eDGe","tablet"],l97D:["EPad","l97D","tablet"],M4301:["Eston","MID M4301","media"],P10AN:["Exper","Easypad P10AN","tablet"],"FIH-F0X":["FIH","F0X"],"Fly IQ260":["Fly","IQ260 BlackBird"],ISW11F:["Fujitsu","Arrows Z"],ISW13F:["Fujitsu","Arrows Z"],IS12F:["Fujitsu","Arrows ES"],"F-01D":["Fujitsu","Arrows Tab LTE","tablet"],"F-03D":["Fujitsu","Arrows Kiss"],"F-05D":["Fujitsu","Arrows X LTE"],"F-07D":["Fujitsu","Arrows Ã�Â¼"],"F-10D":["Fujitsu","Arrows X F-10D"],"F-12C":["Fujitsu","Globetrotter"],f12arc:["Fujitsu","F12arc"],M532:["Fujitsu","Stylistic M532","tablet"],Garminfone:["Garmin-Asus","Garminfone"],"Garmin-Asus A10":["Garmin-Asus","Nuvifone A10"],"Garmin-Asus A50":["Garmin-Asus","Nuvifone A50"],TPA60W:["Gateway","TPA60W","tablet"],"Geeksphone ZERO":["Geeksphone","ZERO"],"gemei G2":["Gemei","G2","tablet"],"Gemei G2":["Gemei","G2","tablet"],"gemei G3":["Gemei","G3","tablet"],"Gemei G9":["Gemei","G9","tablet"],"GSmart G1317D":["Gigabyte","GSmart G1317D"],"Gigabyte TB100":["Gigabyte","TB100","tablet"],GN100:["Gionee","GN100"],GN105:["Gionee","GN105"],GN106:["Gionee","GN106"],GN200:["Gionee","GN200"],GN205:["Gionee","GN205"],GN700W:["Gionee","GN700W"],GN708W:["Gionee","GN708W"],"Google Ion":["Google","Ion"],"Nexus One":["Google","Nexus One"],NexusOne:["Google","Nexus One"],"HTC Nexus One":["Google","Nexus One"],"Nexus S":["Google","Nexus S"],"Google Nexus S":["Google","Nexus S"],"Nexus S 4G":["Google","Nexus S 4G"],"Dooderbutt-4.0.3-v1":["Google","Nexus S 4G"],"Nexus 7":["Google","Nexus 7","tablet"],"Haier HW-W910":["Haier","HW-W910"],SN10T1:["HANNspree","HANNSpad SN10T1","tablet"],SN10T2:["HANNspree","HANNSpad SN10T2","tablet"],HannsComb:["HANNspree","HANNSpad","tablet"],X1:["HCL","ME X1","tablet"],"MID Serails":["Herotab","C8","tablet"],"MID Serials":["Herotab","C8","tablet"],"COSMO DUO":["Hiscreen","Cosmo DUO","tablet"],"HS-U8":["Hisense","U8"],"HS-T92":["Hisense","T92"],"HS-E860":["Hisense","E860"],"HS-E910":["Hisense","E910"],"HS-E926":["Hisense","E926"],"HS-EG900":["Hisense","EG900"],"HS-ET919":["Hisense","ET919"],EG968B:["Hisense","EG968B"],"HKPHONE H8-3G":["HKPhone","H8 3G"],"HOSIN U2":["Hosin","U2"],Touchpad:["HP","TouchPad","tablet"],"HP Touchpad":["HP","TouchPad","tablet"],"cm tenderloin":["HP","TouchPad","tablet"],"aokp tenderloin":["HP","TouchPad","tablet"],"HTC Amaze 4G":["HTC","Amaze 4G"],"HTC Ruby":["HTC","Amaze 4G"],"HTC Amaze 4G(Ruby)":["HTC","Amaze 4G"],"Amaze 4G":["HTC","Amaze 4G"],"HTC Aria":["HTC","Aria"],"HTC Aria A6380":["HTC","Aria"],"HTC Liberty A6380":["HTC","Aria"],"HTC Liberty":["HTC","Aria"],"HTC A6366":["HTC","Aria"],"HTC Bee":["HTC","Bee"],"HTC ChaCha":["HTC","ChaCha"],"HTC ChaCha A810e":["HTC","ChaCha"],"HTC ChaChaCha A810e":["HTC","ChaCha"],"HTC A810e":["HTC","ChaCha"],"HTC A9188":["HTC","Tianxi"],"HTC Bravo":["HTC","Desire"],"HTC Desire":["HTC","Desire"],"HTC Desire A8181":["HTC","Desire"],"HTC Desire A8183":["HTC","Desire"],"HTC Desire Beats A8181":["HTC","Desire"],"HTC Desire CDMA":["HTC","Desire"],"HTC Desire SMS":["HTC","Desire"],"HTC Desire S.M.S":["HTC","Desire"],"HTC Desire C":["HTC","Desire C"],"HTC DesireHD":["HTC","Desire HD"],"HTC DesireHD A9191":["HTC","Desire HD"],"HTC DesireHD A9192":["HTC","Desire HD"],"HTC Desire HD A9191":["HTC","Desire HD"],"HTC A9191":["HTC","Desire HD"],"HTC A9191 for AT&T":["HTC","Desire HD"],"HTC A9192":["HTC","Desire HD"],"HTC Desire HD":["HTC","Desire HD"],"HTC Desire HD with Beats Audio":["HTC","Desire HD"],"HTC Desire S":["HTC","Desire S"],"HTC DesireS":["HTC","Desire S"],"HTC DesiresS":["HTC","Desire S"],"HTC DesireS S510e":["HTC","Desire S"],"HTC DesireS S510b":["HTC","Desire S"],"HTC Desire S S510e":["HTC","Desire S"],"HTC S510e":["HTC","Desire S"],"HTC Desire Saga":["HTC","Desire S"],"HTC Desire V":["HTC","Desire V"],"HTC T328w":["HTC","Desire V"],"HTC Desire VC":["HTC","Desire VC"],"HTC T328d":["HTC","Desire VC"],"HTC T328t":["HTC","Desire VT"],"HTC Desire Z":["HTC","Desire Z"],"HTC DesireZ":["HTC","Desire Z"],"HTC DesireZ A7272":["HTC","Desire Z"],"HTC Desire Z A7272":["HTC","Desire Z"],"HTC Vision":["HTC","Desire Z"],"HTC A7275":["HTC","Desire Z"],"HTC Dream":["HTC","Dream"],"HTC S710d":["HTC","Droid Incredible 2"],"HTC Incredible 2":["HTC","Droid Incredible 2"],"HTC X515d":["HTC","EVO 3D"],"HTC X515m":["HTC","EVO 3D"],"HTC X515C":["HTC","EVO 3D"],"HTC Evo 3D":["HTC","EVO 3D"],"HTC EVO 3D":["HTC","EVO 3D"],"HTC EVO 3D GSM":["HTC","EVO 3D"],"HTC EVO 3D X515a":["HTC","EVO 3D"],"HTC EVO 3D GSM X515m":["HTC","EVO 3D"],"HTC EVO 3D X515m":["HTC","EVO 3D"],"HTC EVO 3D X515M":["HTC","EVO 3D"],"HTC EVO3D X515a":["HTC","EVO 3D"],"HTC EVO3D X515m":["HTC","EVO 3D"],"HTC Evo 3D X515m":["HTC","EVO 3D"],"HTC Evo 3D with Beats Audio X515m":["HTC","EVO 3D"],"HTC Evo 4G":["HTC","EVO 4G"],"HTC EVO 4G":["HTC","EVO 4G"],"HTC X515E":["HTC","EVO 4G+"],"HTC EVO 4G+ For Sprint":["HTC","EVO 4G+"],"HTC EVO 4G++ For Sprint":["HTC","EVO 4G+"],"HTC C715c":["HTC","EVO Design 4G"],"HTC Design 4G":["HTC","EVO Design 4G"],"HTC EVO design 4G":["HTC","EVO Design 4G"],"HTC EVO Design 4G":["HTC","EVO Design 4G"],"HTC Evo Shift":["HTC","EVO Shift"],"HTC EVO Shift 4G":["HTC","EVO Shift"],"HTC A310e":["HTC","Explorer"],"HTC Explorer":["HTC","Explorer"],"HTC Explorer A310b":["HTC","Explorer"],"HTC Explorer A310e":["HTC","Explorer"],"HTC P510e":["HTC","Flyer","tablet"],"HTC Flyer":["HTC","Flyer","tablet"],"HTC Flyer P510e":["HTC","Flyer","tablet"],"HTC Flyer P512":["HTC","Flyer","tablet"],"HTC Flyer P512 NA":["HTC","Flyer","tablet"],"HTC P515E":["HTC","Flyer 4G","tablet"],"HTC Gratia A6380":["HTC","Gratia"],"HTC HD":["HTC","HD"],"HTC HD2":["HTC","HD2"],"HTC HD2 T8585":["HTC","HD2"],"HTC HD2(Leo)":["HTC","HD2"],"HTC HD7":["HTC","HD7"],"HTC T9299+":["HTC","HD7"],"HTC HD7 for Sprint":["HTC","HD7"],"HTC HD7 4G T9299 For AT&T":["HTC","HD7"],"HTC HD7 4G T9299+ For AT&T":["HTC","HD7"],"HTC T9299+ For AT&T":["HTC","HD7"],"HTC HD7S T9399+":["HTC","HD7s"],"HTC HD7S T9899+":["HTC","HD7s"],"HTC T9899+ For AT&T":["HTC","HD7s"],"VitMod ExtraLite 1.6.5.fullodex for HTC HD7 Pro":["HTC","HD7 Pro"],"HTC Hero":["HTC","Hero"],"HTC HERO":["HTC","Hero"],"HTC Hero CDMA":["HTC","Hero"],"HTC HERO CDMA":["HTC","Hero"],"HTC HERO200":["HTC","Hero 200"],"HTC Hero S":["HTC","Hero S"],"HTC IMAGIO":["HTC","Imagio"],"HTC Incredible":["HTC","Incredible"],"HTC Incredible S710E":["HTC","Incredible S"],"HTC S710e":["HTC","Incredible S"],"HTC Incredible S":["HTC","Incredible S"],"HTC Incredible S S710e":["HTC","Incredible S"],"HTC Incredible S s710e":["HTC","Incredible S"],"HTC IncredibleS S710e":["HTC","Incredible S"],"HTC Incredible S with Beats Audio":["HTC","Incredible S"],"HTC Vivo":["HTC","Incredible S"],"HTC Innovation":["HTC","Innovation"],"HTC Inspire 4G":["HTC","Inspire 4G"],"HTC HD7 Inspire 4G For Vodafone":["HTC","Inspire 4G"],"HTC P715a":["HTC","Jetstream","tablet"],"HTC Legend":["HTC","Legend"],"HTC Magic":["HTC","Magic"],"HTC Sapphire":["HTC","Magic"],"HTC Lexikon":["HTC","Merge"],"HTC One S":["HTC","One S"],"HTC Z520e":["HTC","One S"],"HTC One V":["HTC","One V"],"HTC T320e":["HTC","One V"],"HTC One X":["HTC","One X"],"HTC S720e":["HTC","One X"],"HTC Endeavour-LS":["HTC","One X"],"HTC One XL":["HTC","One XL"],"HTC X710a":["HTC","Raider 4G"],"HTC Raider":["HTC","Raider 4G"],"HTC Raider X710e":["HTC","Raider 4G"],"HTC Raider X710s":["HTC","Raider 4G"],"HTC Raider 4G X710e":["HTC","Raider 4G"],"HTC PH39100":["HTC","Raider 4G"],"HTC Holiday":["HTC","Raider 4G"],"HTC Velocity 4G X710s":["HTC","Raider 4G"],"HTC Rezound":["HTC","Rezound"],"HTC Rhyme S510b":["HTC","Rhyme"],"HTC S510b":["HTC","Rhyme"],"HTC Bliss":["HTC","Rhyme"],"HTC Bliss S510b":["HTC","Rhyme"],"HTC Salsa C510e":["HTC","Salsa"],"HTC C510e":["HTC","Salsa"],"HTC Z710a":["HTC","Sensation"],"HTC Z710e":["HTC","Sensation"],"HTC Z710t":["HTC","Sensation"],"HTC Sensation":["HTC","Sensation"],"HTC Sensation Z710":["HTC","Sensation"],"HTC Sensation Z710a":["HTC","Sensation"],"HTC Sensation Z710e":["HTC","Sensation"],"HTC Sensation Z710E":["HTC","Sensation"],"HTC Sensation Z710e For AT&T":["HTC","Sensation"],"HTC Sensation Z710e with Beats Audio":["HTC","Sensation"],"HTC Sensation with Beats Audio Z710e":["HTC","Sensation"],"HTC Sensation with Beats Audio":["HTC","Sensation"],"HTC Sensation Taste":["HTC","Sensation"],"HTC Pyramid":["HTC","Sensation"],"HTC Pyramid Z710a":["HTC","Sensation"],"HTC Pyramid Z710e":["HTC","Sensation"],"HTC Sensation 4G":["HTC","Sensation"],"HTC Sensation 4G with Beats Audio":["HTC","Sensation"],"HTC Sensation G14":["HTC","Sensation"],"HTC Sensation G14 for AT&T":["HTC","Sensation"],"HTC G14 sensation":["HTC","Sensation"],"HTC Z715e":["HTC","Sensation XE"],"HTC Sensation Z715e":["HTC","Sensation XE"],"HTC SensationXE Beats":["HTC","Sensation XE"],"HTC SensationXE Beats Z715a":["HTC","Sensation XE"],"HTC SensationXE Beats Z715e":["HTC","Sensation XE"],"HTC Sensation XE":["HTC","Sensation XE"],"HTC Sensation XE Z715e":["HTC","Sensation XE"],"HTC SensationXE Z715e":["HTC","Sensation XE"],"HTC Sensation XE Beats":["HTC","Sensation XE"],"HTC SensationXE with Beats Audio":["HTC","Sensation XE"],"HTC Sensation XE with Beats Audio":["HTC","Sensation XE"],"HTC Sensation XE with Beats Audio Z715a":["HTC","Sensation XE"],"HTC Sensation Juredroid XE Beats Audio":["HTC","Sensation XE"],"HTC Sensation XE with Beats Audio Z715e":["HTC","Sensation XE"],"HTC Sensation XE With Beats Audio Z715e":["HTC","Sensation XE"],"HTC Sensation 4G XE with Beats Audio":["HTC","Sensation XE"],"HTC Sensation with Beats Audio Z715e":["HTC","Sensation XE"],"HTC X315E":["HTC","Sensation XL"],"HTC SensationXL Beats X315b":["HTC","Sensation XL"],"HTC SensationXL Beats X315e":["HTC","Sensation XL"],"HTC Sensation XL with Beats Audio X315b":["HTC","Sensation XL"],"HTC Sensation XL with Beats Audio X315e":["HTC","Sensation XL"],"HTC Runnymede":["HTC","Sensation XL"],"HTC G21":["HTC","Sensation XL"],"HTC PH06130":["HTC","Status"],"HTC Status":["HTC","Status"],"HTC Tattoo":["HTC","Tattoo"],"HTC TATTOO A3288":["HTC","Tattoo"],"HTC click":["HTC","Tattoo"],"HTC X310e":["HTC","Titan"],"HTC T7373":["HTC","Touch Pro II"],"HTC ThunderBolt":["HTC","ThunderBolt"],"HTC Mecha":["HTC","ThunderBolt"],"HTC Velocity 4G":["HTC","Velocity 4G"],"HTC Wildfire":["HTC","Wildfire"],"HTC Wildfire A3333":["HTC","Wildfire"],"HTC A3366":["HTC","Wildfire"],"HTC A3380":["HTC","Wildfire"],"HTC WildfireS":["HTC","Wildfire S"],"HTC Wildfire S":["HTC","Wildfire S"],"Htc Wildfire s":["HTC","Wildfire S"],"HTC Wildfire S A510e":["HTC","Wildfire S"],"HTC Wildfire S A510b":["HTC","Wildfire S"],"HTC WildfireS A510e":["HTC","Wildfire S"],"HTC WildfireS A510b":["HTC","Wildfire S"],"htc wildfire s a510e":["HTC","Wildfire S"],"HTC Wildfire S A515c":["HTC","Wildfire S"],"HTC A510a":["HTC","Wildfire S"],"HTC A510e":["HTC","Wildfire S"],"HTC A510c":["HTC","Wildfire S"],HTCX06HT:["HTC","Desire"],"HTC A6390":["HTC","A6390"],"HTC A8180":["HTC","A8180"],"HTC PG762":["HTC","PG762"],"HTC S715e":["HTC","S715e"],"HTC S720t":["HTC","S720t"],"HTC Z510d":["HTC","Z510d"],"HTC Z560e":["HTC","Z560e"],"HTC VLE U":["HTC","One S"],"HTC VLE#U":["HTC","One S"],"HTC VIE U":["HTC","One S"],"HTC EVA UL":["HTC","One V"],"HTC ENR U":["HTC","One X"],"ENR U":["HTC","One X"],EndeavorU:["HTC","One X"],Liberty:["HTC","Aria"],Desire:["HTC","Desire"],"Desire A8181":["HTC","Desire"],"desire hd":["HTC","Desire HD"],"Desire HD":["HTC","Desire HD"],"Dedire HD":["HTC","Desire HD"],"Desire Hd (ace)":["HTC","Desire HD"],"Desire S":["HTC","Desire S"],DesireS:["HTC","Desire S"],"Desire Saga":["HTC","Desire S"],"Desire Z":["HTC","Desire Z"],Dream:["HTC","Dream"],"Droid Incredible":["HTC","Droid Incredible"],EVO:["HTC","EVO"],"Evo HD2":["HTC","EVO HD"],"Evo 3D Beats X515m":["HTC","EVO 3D"],"Evo 3D GSM":["HTC","EVO 3D"],"EVO 3D X515m":["HTC","EVO 3D"],"EVO3D X515m":["HTC","EVO 3D"],"Evo 4G":["HTC","EVO 4G"],"EVO 4G":["HTC","EVO 4G"],photon:["HTC","HD mini"],"GinDream/GinMagic":["HTC","Dream"],HD2:["HTC","HD2"],"HD7  Pro":["HTC","HD7 Pro"],Hero:["HTC","Hero"],"HERO CDMA":["HTC","Hero"],HERO200:["HTC","Hero 200"],Incredible:["HTC","Droid Incredible"],"Incredible 2":["HTC","Droid Incredible 2"],"Incredible S":["HTC","Incredible S"],"IncredibleS S710e":["HTC","Incredible S"],IncredibleS:["HTC","Incredible S"],"Inspire HD":["HTC","Inspire 4G"],"Inspire 4G":["HTC","Inspire 4G"],Legend:["HTC","Legend"],NexusHD2:["HTC","HD2"],"Nexus HD2":["HTC","HD2"],"Docomo HT-03A":["HTC","Magic"],"MIUI.us Sensation 4G":["HTC","Sensation 4G"],"SiRF Dream":["HTC","Dream"],Pyramid:["HTC","Sensation"],Sensation:["HTC","Sensation"],"Sensation Z710e":["HTC","Sensation"],"Sensation 4G":["HTC","Sensation"],"Sensation 4g":["HTC","Sensation"],"TripNiCE Pyramid":["HTC","Sensation"],"SensationXE Beats Z715e":["HTC","Sensation XE"],"SensationXL Beats X315e":["HTC","Sensation XL"],Click:["HTC","Tattoo"],Wildfire:["HTC","Wildfire"],"Wildfire S":["HTC","Wildfire S"],"Wildfire S A510e":["HTC","Wildfire S"],"Sprint APX515CKT":["HTC","EVO 3D"],"Sprint APA9292KT":["HTC","EVO 4G"],"Sprint APA7373KT":["HTC","EVO Shift 4G"],"Sprint APC715CKT":["HTC","EVO Design 4G"],A3380:["HTC","Wildfire"],A6277:["HTC","Hero"],a7272:["HTC","Desire Z"],"A7272+(HTC DesireZ)":["HTC","Desire Z"],S31HT:["HTC","Aria"],S710d:["HTC","Droid Incredible 2"],S710D:["HTC","Droid Incredible 2"],X06HT:["HTC","Desire"],"001HT":["HTC","Desire HD"],X325a:["HTC","One X"],Z520m:["HTC","One S"],Z710:["HTC","Sensation"],Z710e:["HTC","Sensation"],T9199h:["HTC","T9199h"],"HTC S610d":["HTC","S610d"],ADR6200:["HTC","Droid Eris"],ADR6300:["HTC","Droid Incredible"],ADR6325VW:["HTC","Merge"],ADR6330VW:["HTC","Rhyme"],ADR6350:["HTC","Droid Incredible 2"],ADR6400L:["HTC","Thunderbolt 4G"],"ADR6400L 4G":["HTC","Thunderbolt 4G"],"ADR6410LVW 4G":["HTC","Fireball"],ADR6425LVW:["HTC","Rezound"],"ADR6425LVW 4G":["HTC","Rezound"],"Coquettish Red":["HTC","Rezound"],PB99400:["HTC","Droid Incredible"],pcdadr6350:["HTC","Droid Incredible 2"],PC36100:["HTC","EVO 4G"],PG06100:["HTC","EVO Shift 4G"],PG41200:["HTC","EVO View 4G","tablet"],PG86100:["HTC","EVO 3D"],PG8610000:["HTC","EVO 3D"],PH44100:["HTC","EVO Design 4G"],PJ83100:["HTC","One X"],ISW11HT:["HTC","EVO 4G"],ISW12HT:["HTC","EVO 3D"],ISW13HT:["HTC","J"],"USCCADR6275US Carrier ID 45":["HTC","Desire"],USCCADR6285US:["HTC","Hero S"],"USCCADR6325US Carrier ID 45":["HTC","Merge"],MediaPad:["Huawei","MediaPad","tablet"],"Huawei MediaPad":["Huawei","MediaPad","tablet"],"HUAWEI MediaPad":["Huawei","MediaPad","tablet"],"Huawei S7-312u":["Huawei","MediaPad","tablet"],"MediaPad 10 FHD":["Huawei","MediaPad","tablet"],"Huawei C8500":["Huawei","C8500"],"Huawei C8500S":["Huawei","C8500"],"Huawei C8600":["Huawei","C8600"],"Huawei C8650":["Huawei","C8650"],"Huawei C8650+":["Huawei","C8650"],"Huawei C8800":["Huawei","IDEOS X5"],"Huawei C8810":["Huawei","Ascend G300"],"Huawei C8812":["Huawei","Ascend C8812"],"Huawei C8812E":["Huawei","Ascend C8812"],"Huawei C8825D":["Huawei","Ascend C8825D"],"Huawei C8860E":["Huawei","Honor"],"Huawei M835":["Huawei","M835"],"Huawei M860":["Huawei","Ascend"],"Huawei M921":["Huawei","M921"],"Huawei S8520":["Huawei","S8520"],"Huawei S8600":["Huawei","S8600"],"Huawei T8300":["Huawei","T8300"],"Huawei T8600":["Huawei","T8600"],"Huawei T8830":["Huawei","T8830"],T8830:["Huawei","T8830"],T8620:["Huawei","T8620"],"Huawei T8828":["Huawei","T8828"],"Huawei U8220":["Huawei","U8220"],"Huawei u8500":["Huawei","IDEOS X2"],"Huawei U8815":["Huawei","Ascend G300"],"Huawei U8825D":["Huawei","Ascend G330D"],"Huawei U8850":["Huawei","Vision"],"Huawei U8652":["Huawei","Sonic"],"Huawei U8800-51":["Huawei","IDEOS X5"],"Huawei U8818":["Huawei","Ascend G300"],"Huawei U9000":["Huawei","Ascend X"],"Huawei IDEOS U8500":["Huawei","IDEOS X2"],"Huawei IDEOS U8650":["Huawei","Sonic"],"Huawei IDEOS X3":["Huawei","IDEOS X3"],"Huawei Ideos X5":["Huawei","IDEOS X5"],"Huawei Ideos X5 1.12.9(ret4rt)":["Huawei","IDEOS X5"],"Huawei SONIC":["Huawei","Sonic"],"Huawei 8100-9":["Huawei","U8100"],FUSIONideos:["Huawei","IDEOS"],"Gnappo Ideos":["Huawei","IDEOS"],Ideos:["Huawei","IDEOS"],"IDEOS X5":["Huawei","IDEOS X5"],"Ideos S7":["Huawei","IDEOS S7","tablet"],"IDEOS S7":["Huawei","IDEOS S7","tablet"],"IDEOS S7 Slim":["Huawei","IDEOS S7","tablet"],"Huawei S7":["Huawei","IDEOS S7","tablet"],SONIC:["Huawei","Sonic"],"Kyivstar Aqua":["Huawei","Sonic"],"Lucky Ultra Sonic U8650":["Huawei","Sonic"],"Turkcell T20":["Huawei","Sonic"],"MTC 950":["Huawei","U8160"],"MTC 955":["Huawei","Sonic"],"MTC Evo":["Huawei","C8500"],"MTC Android":["Huawei","U8110"],S31HW:["Huawei","Pocket WiFi S"],S41HW:["Huawei","Pocket WiFi S II"],"007HW":["Huawei","Vision"],UM840:["Huawei","Evolution"],M860:["Huawei","Ascend"],M865:["Huawei","Ascend II"],M886:["Huawei","Glory"],C8150:["Huawei","IDEOS"],c8500:["Huawei","C8500"],C8500:["Huawei","C8500"],C8500S:["Huawei","C8500"],C8600:["Huawei","C8600"],c8650:["Huawei","C8650"],C8650:["Huawei","C8650"],c8800:["Huawei","C8800"],C8800:["Huawei","C8800"],c8810:["Huawei","Ascend G300C"],C8812:["Huawei","Ascend C8812"],S8600:["Huawei","S8600"],U8100:["Huawei","U8100"],U8110:["Huawei","U8110"],u8120:["Huawei","U8120"],U8120:["Huawei","U8120"],U8180:["Huawei","IDEOS X1"],U8220:["Huawei","Pulse"],U8300:["Huawei","U8300"],U8350:["Huawei","Boulder"],U8150:["Huawei","IDEOS"],U8160:["Huawei","U8160"],U8500:["Huawei","IDEOS X2"],"U8500 HiQQ":["Huawei","U8500 HiQQ Edition"],U8510:["Huawei","IDEOS X3"],u8650:["Huawei","Sonic"],U8650:["Huawei","Sonic"],"U8650-1":["Huawei","Sonic"],U8660:["Huawei","Sonic"],u8800:["Huawei","IDEOS X5"],U8800:["Huawei","IDEOS X5"],"U8800+":["Huawei","IDEOS X5"],U8800X:["Huawei","IDEOS X5"],U8800pro:["Huawei","IDEOS X5 Pro"],U8800PRO:["Huawei","IDEOS X5 Pro"],U8800Pro:["Huawei","IDEOS X5 Pro"],u8800pro:["Huawei","IDEOS X5 Pro"],"U8800 Pro":["Huawei","IDEOS X5 Pro"],U8818:["Huawei","Ascend G300"],U8850:["Huawei","Vision"],u8860:["Huawei","Honor"],U8860:["Huawei","Honor"],U9000:["Huawei","Ascend X"],U9200:["Huawei","Ascend P1"],"U9200-1":["Huawei","Ascend P1"],U9500:["Huawei","Ascend D1"],U9501L:["Huawei","Ascend D LTE"],U9510:["Huawei","Ascend D quad"],U9510E:["Huawei","Ascend D quad"],Comet:["Huawei","Comet"],GS02:["Huawei","Honor"],GS03:["Huawei","Ascend P1"],"DroniX-0.5":["Huawei","U8180"],"MTS-SP101":["Huawei","C8511"],TSP21:["Huawei","U8110"],"HYUNDAI H6":["Hyundai","Storm H6"],"iBall Slide i7011":["iBall","Slide i7011"],"NetTAB RUNE":["IconBit","NetTab Rune","tablet"],D70W:["Icoo","D70W","tablet"],D80:["Icoo","D80","tablet"],"INFOBAR A01":["iida","INFOBAR A01"],M009F:["Infotmic","M009F"],AZ210A:["Intel","AZ210A"],AZ210B:["Intel","AZ210B"],AZ510:["Intel","AZ510"],greenridge:["Intel","Green Ridge","tablet"],"INQ Cloud Touch":["INQ","Cloud Touch"],"ILT-MX100":["iRiver","Tab","tablet"],IVIO_DE38:["Ivio","DE38"],"JY-G2":["Jiayu","G2"],"JXD S601WIFI":["JXD","S601 WIFI","media"],A2:["KakaTech","A2"],D91:["KK","D91","tablet"],K080:["Kobo","K080","ereader"],A106:["koobee","A160"],"KPT A9":["KPT","A9"],"EV-S100":["Kttech","Take EV-S100"],"KM-S120":["Kttech","Take 2 KM-S120"],"KM-S200":["TAKE","Janus KM-S200"],"KM-S220":["Kttech","Take Tachy KM-S220"],"Kyobo mirasol eReader":["Kyobo","eReader","ereader"],ISW11K:["Kyocera","Digno"],"JC-KSP8000":["Kyocera","Echo"],KSP8000:["Kyocera","Echo"],Zio:["Kyocera","Zio"],C5155:["Kyocera","C5155"],C5170:["Kyocera","C5170"],M9300:["Kyocera","M9300"],E800:["K-Touch","E800"],W606:["K-Touch","W606"],"K-Touch T619":["K-Touch","T619"],"K-Touch W619":["K-Touch","W619"],"K-Touch W650":["K-Touch","W650"],W700:["K-Touch","W700"],W800:["K-Touch","W800"],W806:["K-Touch","W806"],W808:["K-Touch","W808"],W810:["K-Touch","W810"],X900:["Lava","XOLO X900"],"Lenovo A798t":["Lenovo","A798t"],"LENOVO-Lenovo-A288t":["Lenovo","LePhone A288"],"ThinkPad Tablet":["Lenovo","ThinkPad Tablet","tablet"],K1:["Lenovo","IdeaPad K1","tablet"],"Ideapad S10-3T":["Lenovo","IdeaPad S10-3T","tablet"],"S2005A-H":["Lenovo","S2005A"],"IdeaTab S2007A-D":["Lenovo","IdeaTab S2007A","tablet"],IdeaTabV2007A:["Lenovo","IdeaTab V2007A","tablet"],"IdeaTabV2007A-D-I":["Lenovo","IdeaTab V2007A","tablet"],IdeaTabV2010A:["Lenovo","IdeaTab V2010A","tablet"],"IdeaTab A2107A-H":["Lenovo","IdeaTab V2107A","tablet"],"A1 07":["Lenovo","LePad","tablet"],"lepad 001b":["Lenovo","LePad","tablet"],"lepad 001n":["Lenovo","LePad","tablet"],"3GC101":["Lenovo","LePhone 3GC101"],"Lenovo 3GC101":["Lenovo","LePhone 3GC101"],"3GW100":["Lenovo","LePhone 3GW100"],"Lenovo 3GW100":["Lenovo","LePhone 3GW100"],"3GW101":["Lenovo","LePhone 3GW101"],"Lenovo 3GW101":["Lenovo","LePhone 3GW101"],"Lephone 3GW101":["Lenovo","LePhone 3GW101"],"Lenovo A1-32AB0":["Lenovo","LePhone A1-32AB0"],"Lenovo S1-37AH0":["Lenovo","LePhone S1-37AH0"],"S1 37AHO":["Lenovo","LePhone S1-37AH0"],"Lenovo S2-38AH0":["Lenovo","LePhone S2-38AH0"],"Lenovo S2-38AT0":["Lenovo","LePhone S2-38AT0"],"Lenovo A288t":["Lenovo","LePhone A288"],"Lenovo A366t":["Lenovo","LePhone A366"],"Lenovo A390e":["Lenovo","LePhone A390"],"Lenovo A500":["Lenovo","LePhone A500"],"Lenovo A520":["Lenovo","LePhone A520"],"Lenovo A560e":["Lenovo","A560"],"Lenovo A668t":["Lenovo","LePhone A668"],"Lenovo A698t":["Lenovo","LePhone A698"],"Lenovo A750":["Lenovo","LePhone A750"],"Lenovo A780":["Lenovo","LePhone A780"],"Lenovo A789":["Lenovo","LePhone A789"],"Lenovo A790e":["Lenovo","LePhone A790"],"Lenovo P70":["Lenovo","LePhone P70"],"Lenovo P700":["Lenovo","LePhone P700"],"Lenovo S850e":["Lenovo","S850"],"Lenovo S880":["Lenovo","S880"],"Lenovo K860":["Lenovo","K860"],A30t:["Lenovo","A30t"],"Lenovo A60":["Lenovo","A60"],"Lenovo A65":["Lenovo","A65"],"Lenovo A66t":["Lenovo","A66t"],"Lenovo A68e":["Lenovo","A68e"],"Lenovo K800":["Lenovo","K800"],"IDEA TV T100":["Lenovo","IDEA TV","television"],"IDEA TV K91":["Lenovo","IDEA TV","television"],TC970:["Le Pan","TC970","tablet"],LePanII:["Le Pan","II","tablet"],"LG-C555":["LG","Optimus Chat"],"LG-C555-parrot":["LG","Optimus Chat"],"LG-C660h":["LG","Optimus Pro"],"LG-C729":["LG","DoublePlay"],"LG-C800G":["LG","Eclypse"],"LG-CX670":["LG","Optimus 3G"],"LG-E400":["LG","Optimus L3"],"LG-E400f":["LG","Optimus L3"],"LG-E510":["LG","Optimus Hub"],"LG-E510f":["LG","Optimus Hub"],"LG-E510g":["LG","Optimus Hub"],"LG-E610":["LG","Optimus L5"],"LG-E612":["LG","Optimus L5"],"LG-E612g":["LG","Optimus L5"],"LG-E615F":["LG","E615"],"LG-E617G":["LG","E617"],"LG-E720":["LG","Optimus Chic"],"LG-E720b":["LG","Optimus Chic"],"LG-E730":["LG","Optimus Sol"],"LG-E970":["LG","Shine"],"LG-F100L":["LG","Optimus Vu"],"LG-F100S":["LG","Optimus Vu"],"LG-F120K":["LG","Optimus LTE Tag"],"LG-F120L":["LG","Optimus LTE Tag"],"LG-F120S":["LG","Optimus LTE Tag"],"LG-F160K":["LG","Optimus LTE II"],"LG-F160L":["LG","Optimus LTE II"],"LG-F160S":["LG","Optimus LTE II"],"LG-F180L":["LG","F180L"],"LG-GT540":["LG","Optimus"],"LG-GT540f":["LG","Optimus"],"LG-GT540 Swift":["LG","Optimus"],"LG-GW620":["LG","GW620"],"LG-KH5200":["LG","Andro-1"],"LG-KU3700":["LG","Optimus One"],"LG-KU5400":["LG","PRADA 3.0"],"LG-KU5900":["LG","Optimus Black"],"LG-L40G":["LG","L40G"],"LG-LG855":["LG","Marquee"],"LG-LS670":["LG","Optimus S"],"LG-LS696":["LG","Optimus Elite"],"LG-LS840":["LG","Viper 4G"],"LG-LS855":["LG","Marquee"],"LG-LS860":["LG","'Cayenne'"],"LG-LS970":["LG","'Eclipse'"],"LG-LU3000":["LG","Optimus Mach"],"LG-LU3100":["LG","Optimus Chic"],"LG-LU3700":["LG","Optimus One"],"LG-LU5400":["LG","PRADA 3.0"],"LG-LU6200":["LG","Optimus Q2"],"LG-lu6200":["LG","Optimus Q2"],"LG-LU6500":["LG","Optimus Note"],"LG-LU6800":["LG","Optimus Big"],"LG-LU8300":["LG","Optimus Pad LTE"],"LG-LW690":["LG","Optimus C"],"LG-LW770":["LG","LW770"],"LG-MS690":["LG","Optimus M"],"LG-MS770":["LG","MS770"],"LG-MS840":["LG","Connect 4G"],"LG-MS910":["LG","Esteem"],"LG-MS695":["LG","Optimus M+"],"LG P350":["LG","Optimus Me"],"LG-P350":["LG","Optimus Me"],"LG-P350f":["LG","Optimus Me"],"LG-P350g":["LG","Optimus Me"],"LG-P355":["LG","P355"],"LG-P500":["LG","Optimus One"],"LG-P500h":["LG","Optimus One"],"LG-P500h-parrot":["LG","Optimus One"],"LG-P503":["LG","Optimus One"],"LG-P504":["LG","Optimus One"],"LG-P505":["LG","Phoenix"],"LG-P505R":["LG","Phoenix"],"LG-P506":["LG","Thrive"],"LG-P509":["LG","Optimus T"],"LG-P690":["LG","Optimus Net"],"LG-P693":["LG","P693"],"LG-P698":["LG","Optimus Net"],"LG-P698f":["LG","Optimus Net"],"LG-P700":["LG","Optimus L7"],"LG-P705":["LG","Optimus L7"],"LG-P705f":["LG","Optimus L7"],"LG-P705g":["LG","Optimus L7"],"LG-P708g":["LG","P708"],"LG-P720":["LG","Optimus Chic"],"LG-P720h":["LG","Optimus Chic"],"LG-P725":["LG","Optimus 3D Max"],"LG-P760":["LG","P760"],"LG-P769":["LG","P769"],"LG-P860":["LG","P860"],"LG-P870":["LG","P870"],"LG-P870F":["LG","P870"],"LG-P880":["LG","X3"],"LG-P880g":["LG","X3"],"LG-P895":["LG","P895"],"LG-P920":["LG","Optimus 3D"],"LG-P920h":["LG","Optimus 3D"],"LG-P925":["LG","Thrill"],"LG-P925g":["LG","Thrill"],"LG-P930":["LG","Nitro HD"],"LG-P936":["LG","Optimus LTE"],"LG-P940":["LG","PRADA 3.0"],"LG-P970":["LG","Optimus Black"],"LG-P970h":["LG","Optimus Black"],"LG-P990":["LG","Optimus 2X Speed"],"LG-P990h":["LG","Optimus 2X Speed"],"LG-P990hN":["LG","Optimus 2X Speed"],"LG-P990H":["LG","Optimus 2X Speed"],"LG-P993":["LG","Optimus 2X"],"LG-SU540":["LG","PRADA 3.0"],"LG-SU640":["LG","Optimus LTE"],"LG-SU660":["LG","Optimus 2X"],"LG-SU760":["LG","Optimus 3D"],"LG-SU760-Kust":["LG","Optimus 3D"],"LG-SU870":["LG","Optimus 3D Cube"],"LG-SU880":["LG","Optimus EX"],"LG-US670":["LG","Optimus U"],"LG-US730":["LG","US730"],"LG-V900":["LG","Optimus Pad","tablet"],"LG-V905R":["LG","Optimus G-Slate","tablet"],"LG-V909":["LG","Optimus G-Slate","tablet"],"LG-VM670":["LG","Optimus V"],"LG-VM696":["LG","Optimus Elite"],"LG-VM701":["LG","Optimus Slider"],"LG-VS660":["LG","Vortex"],"LG-VS700":["LG","Enlighten"],"LG-VS740":["LG","Ally"],"LG-VS840":["LG","Connect 4G"],"LG-VS910":["LG","Revolution"],"lgp-970":["LG","Optimus Black"],E900:["LG","Optimus 7"],GT540:["LG","Optimus GT540"],GW620:["LG","GW620"],KU9500:["LG","Optimus Z"],LGC660:["LG","Optimus Pro"],LGL45C:["LG","Optimus Net"],LGL55C:["LG","Optimus Q"],LU2300:["LG","Optimus Q"],LS670:["LG","Optimus S"],P940:["LG","PRADA 3.0"],P990:["LG","Optimus 2X Speed"],"USCC-US730":["LG","US730"],"USCC-US760":["LG","Genesis"],VM670:["LG","Optimus V"],"VS840 4G":["LG","Connect 4G"],"VS900-4G":["LG","VS900"],"VS910 4G":["LG","Revolution 4G"],"VS920 4G":["LG","Spectrum 4G"],"VS930 4G":["LG","VS930"],"VS950 4G":["LG","VS950"],"L-01D":["LG","Optimus LTE"],"L-02D":["LG","PRADA phone"],"L-04C":["LG","Optimus Chat"],"L-05D":["LG","Optimus it"],"L-06C":["LG","Optimus Pad","tablet"],"L-06D":["LG","Optimus Vu"],"L-07C":["LG","Optimus Bright"],"LG-Eve":["LG","Eve"],"LG-Optimus One P500":["LG","Optimus One"],"LG-Optimus 2X":["LG","Optimus 2X"],"LG-GT540 Optimus":["LG","Optimus"],"LG-Optimus Black":["LG","Optimus Black"],Ally:["LG","Ally"],Optimus:["LG","Optimus"],"Optimus Me":["LG","Optimus Me"],"optimus me p350":["LG","Optimus Me"],"Optimus 2X":["LG","Optimus 2X"],"Optimus 2x":["LG","Optimus 2X"],IS11LG:["LG","Optimus X"],Vortex:["LG","Vortex"],"LDK-ICK v1.4":["LG","Esteem"],T6:["Malata","Zpad T6","tablet"],"Malata SMBA1002":["Malata","Tablet SMB-A1002","tablet"],STM712HCZ:["Mediacom","SmartPad 712c","tablet"],STM803HC:["Mediacom","SmartPad 810c","tablet"],"Mediacom 810C":["Mediacom","SmartPad 810c","tablet"],Smartpad810c:["Mediacom","SmartPad 810c","tablet"],SmartPad810c:["Mediacom","SmartPad 810c","tablet"],MP810C:["Mediacom","SmartPad 810c","tablet"],MP907C:["Mediacom","SmartPad 907c","tablet"],MTK6516:["Mediatek","MTK6516"],"LIFETAB S9512":["Medion","Lifetab S9512","tablet"],"LIFETAB P9514":["Medion","Lifetab P9514","tablet"],"MD LIFETAB P9516":["Medion","Lifetab P9516","tablet"],"MEDION LIFE P4310":["Medion","Life P4310"],M8:["Meizu","M8"],M9:["Meizu","M9"],M040:["Meizu","M040"],"M9-unlocked":["Meizu","M9"],"meizu m9":["Meizu","M9"],"MEIZU M9":["Meizu","M9"],"MEIZU MX":["Meizu","MX"],M030:["Meizu","MX M030"],M031:["Meizu","MX M031"],M032:["Meizu","MX M032"],Slidepad:["Memup","Slidepad","tablet"],A45:["Micromax","A45 Punk"],"Micromax A50":["Micromax","A50 Ninja"],"Micromax A60":["Micromax","Andro A60"],"Micromax A70":["Micromax","Andro A70"],"P300(Funbook)":["Micromax","Funbook P300","tablet"],AT735:["Moinstone","AT735","tablet"],A853:["Motorola","Milestone"],A953:["Motorola","Milestone 2"],A1680:["Motorola","MOTO A1680"],ET1:["Motorola","ET1 Enterprise Tablet","tablet"],MB200:["Motorola","CLIQ"],MB300:["Motorola","BACKFLIP"],MB501:["Motorola","CLIQ XT"],MB502:["Motorola","CHARM"],MB511:["Motorola","FLIPOUT"],MB520:["Motorola","BRAVO"],MB525:["Motorola","DEFY"],"MB525+":["Motorola","DEFY"],"MB525 for me":["Motorola","DEFY"],MB526:["Motorola","DEFY+"],MB611:["Motorola","CLIQ 2"],MB612:["Motorola","XPRT"],MB632:["Motorola","PRO+"],MB855:["Motorola","PHOTON 4G"],MB860:["Motorola","ATRIX"],MB861:["Motorola","ATRIX"],mb861:["Motorola","ATRIX"],MB865:["Motorola","ATRIX 2"],MB870:["Motorola","Droid X2"],MB886:["Motorola","DINARA"],ME501:["Motorola","CLIQ XT"],ME511:["Motorola","FLIPOUT"],me525:["Motorola","MOTO ME525"],Me525:["Motorola","MOTO ME525"],ME525:["Motorola","MOTO ME525"],"ME525+":["Motorola","MOTO ME525"],ME600:["Motorola","BACKFLIP"],ME632:["Motorola","PRO+"],ME722:["Motorola","Milestone 2"],ME811:["Motorola","Droid X"],ME860:["Motorola","ATRIX"],ME863:["Motorola","Milestone 3"],ME865:["Motorola","ATRIX 2"],MT620:["Motorola","MOTO MT620"],MT620t:["Motorola","MOTO MT620"],MT716:["Motorola","MOTO MT716"],MT810:["Motorola","MOTO MT810"],MT870:["Motorola","MOTO MT870"],MT917:["Motorola","MT917"],MZ505:["Motorola","XOOM Family Edition","tablet"],MZ600:["Motorola","XOOM 4G LTE","tablet"],MZ601:["Motorola","XOOM 3G","tablet"],MZ602:["Motorola","XOOM 4G LTE","tablet"],MZ603:["Motorola","XOOM 3G","tablet"],MZ604:["Motorola","XOOM WiFi","tablet"],MZ605:["Motorola","XOOM 3G","tablet"],MZ606:["Motorola","XOOM WiFi","tablet"],MZ607:["Motorola","XOOM 2 WiFi Media Edition","tablet"],MZ609:["Motorola","Droid XYBOARD 8.2","tablet"],"MZ609 4G":["Motorola","Droid XYBOARD 8.2","tablet"],MZ615:["Motorola","XOOM 2 WiFi","tablet"],MZ617:["Motorola","Droid XYBOARD 10.1","tablet"],"MZ617 4G":["Motorola","Droid XYBOARD 10.1","tablet"],WX435:["Motorola","TRIUMPH WX435"],WX445:["Motorola","CITRUS WX445"],XT300:["Motorola","SPICE"],XT301:["Motorola","MOTO XT301"],XT311:["Motorola","FIRE"],XT316:["Motorola","MOTO XT316"],XT319:["Motorola","MOTO XT319"],XT390:["Motorola","MOTO XT390"],XT320:["Motorola","DEFY Mini"],XT321:["Motorola","DEFY Mini"],XT500:["Motorola","MOTO XT500"],"xt-500":["Motorola","MOTO XT500"],XT502:["Motorola","QUENCH XT5"],XT530:["Motorola","FIRE XT"],XT531:["Motorola","FIRE XT"],XT532:["Motorola","XT532"],XT535:["Motorola","DEFY"],XT550:["Motorola","XT550"],XT556:["Motorola","XT556"],XT603:["Motorola","ADMIRAL"],XT610:["Motorola","Droid Pro"],XT615:["Motorola","MOTO XT615"],XT626:["Motorola","MOTO XT626"],XT681:["Motorola","MOTO XT681"],XT682:["Motorola","Droid 3"],XT685:["Motorola","MOTO XT685"],XT687:["Motorola","ATRIX TV"],XT701:["Motorola","XT701"],XT702:["Motorola","MOTO XT702"],XT711:["Motorola","MOTO XT711"],XT720:["Motorola","Milestone"],XT875:["Motorola","Droid Bionic"],XT800:["Motorola","MOTO XT800"],"XT800+":["Motorola","MOTO XT800"],XT800W:["Motorola","MOTO Glam"],XT806:["Motorola","MOTO XT806"],XT860:["Motorola","Milestone 3"],XT862:["Motorola","Droid 3"],XT882:["Motorola","MOTO XT882"],XT883:["Motorola","Milestone 3"],XT889:["Motorola","XT889"],XT897:["Motorola","Droid 4"],XT901:["Motorola","RAZR"],XT910:["Motorola","RAZR"],XT910K:["Motorola","RAZR"],XT910S:["Motorola","RAZR"],"XT910 4G":["Motorola","RAZR"],XT912:["Motorola","Droid RAZR"],XT923:["Motorola","Droid RAZR HD"],XT925:["Motorola","Droid RAZR HD"],XT926:["Motorola","Droid RAZR"],"XT926 4G":["Motorola","Droid RAZR"],XT928:["Motorola","XT928"],"Atrix 2":["Motorola","ATRIX 2"],"Atrix 4g":["Motorola","ATRIX 4G"],"Atrix 4G":["Motorola","ATRIX 4G"],"Atrix 4G ME860":["Motorola","ATRIX 4G"],CLIQ:["Motorola","CLIQ"],"CLIQ XT":["Motorola","CLIQ XT"],CLIQ2:["Motorola","CLIQ 2"],Corvair:["Motorola","Corvair","tablet"],DEFY:["Motorola","DEFY"],"Defy+":["Motorola","DEFY+"],"Defy Plus":["Motorola","DEFY+"],Devour:["Motorola","Devour"],Dext:["Motorola","Dext"],Droid:["Motorola","Droid"],DROID:["Motorola","Droid"],DROID2:["Motorola","Droid 2"],"DROID2 GLOBAL":["Motorola","Droid 2"],"DROID2 Global":["Motorola","Droid 2"],Droid2Global:["Motorola","Droid 2"],"DROID 2":["Motorola","Droid 2"],DROID3:["Motorola","Droid 3"],DROID4:["Motorola","Droid 4"],"DROID4 4G":["Motorola","Droid 4"],"DROID Pro":["Motorola","Droid Pro"],"DROID BIONIC":["Motorola","Droid Bionic"],"DROID BIONIC 4G":["Motorola","Droid Bionic"],"DROID BIONIC XT875 4G":["Motorola","Droid Bionic"],DROIDRAZR:["Motorola","Droid RAZR"],"Droid Razr":["Motorola","Droid RAZR"],"DROID RAZR":["Motorola","Droid RAZR"],"DROID RAZR 4G":["Motorola","Droid RAZR"],"DROID SPYDER":["Motorola","Droid RAZR"],"DROID RAZR HD":["Motorola","Droid RAZR HD"],"DROID RAZR HD 4G":["Motorola","Droid RAZR HD"],DroidX:["Motorola","Droid X"],DROIDX:["Motorola","Droid X"],"droid x":["Motorola","Droid X"],"Droid X":["Motorola","Droid X"],"DROID X":["Motorola","Droid X"],"DROID X2":["Motorola","Droid X2"],Electrify:["Motorola","Electrify"],"Milestone XT720":["Motorola","Milestone"],"Milestone Xt720":["Motorola","Milestone"],Milestone:["Motorola","Milestone"],"A853 Milestone":["Motorola","Milestone"],"Milestone X":["Motorola","Milestone X"],"Milestone X2":["Motorola","Milestone X2"],MotoroiX:["Motorola","Droid X"],"Moto Backflip":["Motorola","BACKFLIP"],RAZR:["Motorola","RAZR"],Triumph:["Motorola","TRIUMPH"],"Opus One":["Motorola","i1"],Photon:["Motorola","PHOTON"],"Photon 4G":["Motorola","PHOTON 4G"],XOOM:["Motorola","XOOM","tablet"],Xoom:["Motorola","XOOM","tablet"],"XOOM 2":["Motorola","XOOM 2","tablet"],"XOOM 2 ME":["Motorola","XOOM 2","tablet"],"XOOM MZ606":["Motorola","XOOM WiFi","tablet"],ISW11M:["Motorola","PHOTON"],IS12M:["Motorola","RAZR"],MOTWX435KT:["Motorola","TRIUMPH"],"X3-Ice MIUI XT720 Memorila Classics":["Motorola","Milestone"],"NABI-A":["Nabi","Kids tablet","tablet"],Newpad:["Newsmy","Newpad","tablet"],"Newpad-K97":["Newsmy","Newpad K97","tablet"],"Newpad P9":["Newsmy","Newpad P9","tablet"],"M-PAD N8":["Newsmy","M-pad N8","tablet"],"LT-NA7":["NEC","LT-NA7"],"N-01D":["NEC","MEDIAS PP N-01D"],"N-04C":["NEC","MEDIAS N-04C"],"N-04D":["NEC","MEDIAS LTE N-04D"],"N-05D":["NEC","MEDIAS ES N-05D"],"N-06C":["NEC","MEDIAS WP N-06C"],"N-06D":["NEC","MEDIAS Tab N-06D","tablet"],"N-07D":["NEC","MEDIAS X N-07D"],"101N":["NEC","MEDIAS CH Softbank 101N"],IS11N:["NEC","MEDIAS BR IS11N"],"Nexian NX-A890":["Nexian","Journey"],"NX-A891":["Nexian","Ultra Journey"],M726HC:["Nextbook","Premium 7","ereader"],NXM726HN:["Nextbook","Premium 7","ereader"],NXM803HD:["Nextbook","Premium 8","ereader"],DATAM803HC:["Nextbook","Premium 8","ereader"],NXM901:["Nextbook","Next 3","ereader"],"NGM Vanity Smart":["NGM","Vanity Smart"],"Nokia N9":["Nokia","N9"],"Nokia N900":["Nokia","N900"],Lumia800:["Nokia","Lumia 800"],"Lumia 900":["Nokia","Lumia 900"],"Notion Ink ADAM":["Notion Ink","ADAM","tablet"],"P4D SIRIUS":["Nvsbl","P4D SIRIUS","tablet"],"P4D Sirius":["Nvsbl","P4D SIRIUS","tablet"],EFM710A:["Oblio","Mint 7x","tablet"],"ODYS-Xpress":["Odys","Xpress","tablet"],"Olivetti Olipad 100":["Olivetti","Olipad 100","tablet"],OP110:["Olivetti","Olipad 110","tablet"],"ONDA MID":["Onda","MID","tablet"],VX580A:["Onda","VX580A","tablet"],VX610A:["Onda","VX610A","tablet"],TQ150:["Onda","TQ150"],N2T:["ONN","N2T","tablet"],Renesas:["Opad","Renesas","tablet"],"renesas emev":["Opad","Renesas","tablet"],X903:["Oppo","Find Me X903"],X905:["Oppo","Find 3 X905"],R805:["Oppo","R805"],R801:["Oppo","R801"],R811:["Oppo","R811"],X909:["Oppo","X909"],OPPOR801:["Oppo","R801"],OPPOX905:["Oppo","Find 3 X905"],OPPOX907:["Oppo","Find 3 X907"],X907:["Oppo","Find 3 X907"],X9015:["Oppo","Find X9015"],OPPOX9017:["Oppo","Finder X9017"],OPPOU701:["Oppo","OPPOU701"],OPPOR807:["Oppo","Real R807"],OPPOR805:["Oppo","Real R805"],R807:["Oppo","Real R807"],OPPOT703:["Oppo","T703"],"P-01D":["Panasonic","P-01D"],"P-02D":["Panasonic","Lumix Phone"],"P-04D":["Panasonic","Eluga"],"P-07C":["Panasonic","P-07C"],dL1:["Panasonic","Eluga dL1"],"101P":["Panasonic","Lumix Phone"],"JT-H580VT":["Panasonic","BizPad 7","tablet"],"JT-H581VT":["Panasonic","BizPad 10","tablet"],"FZ-A1A":["Panasonic","Toughpad","tablet"],pandigital9hr:["Pandigital","9HR","tablet"],pandigital9hr2:["Pandigital","9HR2","tablet"],pandigitalopc1:["Pandigital","OPC1","tablet"],pandigitalopp1:["Pandigital","OPP1","tablet"],pandigitalp1hr:["Pandigital","p1hr","tablet"],"IM-A600S":["Pantech","SIRIUS Ã�Â±"],"IM-A630K":["Pantech","SKY Izar"],"IM-A690L":["Pantech","SKY"],"IM-A690S":["Pantech","SKY"],"IM-A710K":["Pantech","SKY Vega Xpress"],"IM-A720L":["Pantech","SKY Vega Xpress"],"IM-A725L":["Pantech","SKY Vega X+"],"IM-A730s":["Pantech","SKY Vega S"],"IM-A730S":["Pantech","SKY Vega S"],"IM-A750K":["Pantech","SKY Mirach A"],"IM-A760S":["Pantech","SKY Vega Racer"],"IM-A770K":["Pantech","SKY Vega Racer"],"IM-A780L":["Pantech","SKY Vega Racer"],"IM-A800S":["Pantech","SKY Vega LTE"],"IM-A810K":["Pantech","SKY Vega LTE M"],"IM-A810S":["Pantech","SKY Vega LTE M"],"IM-A820L":["Pantech","SKY Vega LTE EX"],"IM-A830K":["Pantech","SKY Vega Racer 2"],"IM-A830L":["Pantech","SKY Vega Racer 2"],"IM-A830S":["Pantech","SKY Vega Racer 2"],"IM-A840S":["Pantech","SKY Vega S5"],"IM-A850K":["Pantech","IM-A850K"],"IM-T100K":["Pantech","SKY Vega No. 5","tablet"],IS06:["Pantech","SIRIUS Ã�Â±"],ADR8995:["Pantech","Breakout"],"ADR8995 4G":["Pantech","Breakout"],"ADR910L 4G":["Pantech","ADR910L"],PantechP4100:["Pantech","Element","tablet"],PantechP8000:["Pantech","Crossover"],PantechP8010:["Pantech","P8010"],PantechP9060:["Pantech","Pocket"],PantechP9070:["Pantech","Burst"],"SKY IM-A600S":["Pantech","SIRIUS Ã�Â±"],"SKY IM-A630K":["Pantech","SKY Izar"],"SKY IM-A650S":["Pantech","SKY Vega"],IS11PT:["Pantech","Mirach IS11PT"],PAT712W:["Perfeo","PAT712W","tablet"],X7G:["Pearl","Touchlet X7G","tablet"],FWS810:["PHICOMM","FWS810"],"Philips PI5000":["Philips","PI5000","tablet"],PI7000:["Philips","PI7000","tablet"],"Philips W626":["Philips","W626"],"Philips W632":["Philips","W632"],MOMO:["Ployer","MOMO","tablet"],MOMO15:["Ployer","MOMO15","tablet"],"PocketBook A7":["PocketBook","A7","tablet"],"PocketBook A10":["PocketBook","A10","tablet"],"Mobii 7":["Point Of View","Mobii 7","tablet"],PMP3384BRU:["Prestigio","Multipad 3384","tablet"],TB07FTA:["Positivo","TB07FTA","tablet"],"QW TB-1207":["Qware","Pro3","tablet"],"W6HD ICS":["Ramos","W6HD","tablet"],w10:["Ramos","W10","tablet"],W10:["Ramos","W10","tablet"],"w10 v2.0":["Ramos","W10 v2.0","tablet"],"W10 V2.0":["Ramos","W10 v2.0","tablet"],T11AD:["Ramos","T11AD","tablet"],"T11AD.FE":["Ramos","T11AD","tablet"],PlayBook:["RIM","BlackBerry PlayBook","tablet"],"RBK-490":["Ritmix","RBK-490","tablet"],A8HD:["Saayi","Dropad A8HD","tablet"],"GT-S7568":["Samsung","S7568"],"Galaxy Nexus":["Samsung","Galaxy Nexus"],"GT-B5330":["Samsung","GT-B5330"],"GT-B5510":["Samsung","Galaxy Y Pro"],"GT-B5510B":["Samsung","Galaxy Y Pro"],"GT-B5510L":["Samsung","Galaxy Y Pro"],"GT-B5512":["Samsung","Galaxy Y Pro Duos"],"GT-B7510":["Samsung","Galaxy Pro"],"GT-B7510L":["Samsung","Galaxy Pro"],"GT-I5500":["Samsung","Galaxy 5"],"GT-I5500B":["Samsung","Galaxy 5"],"GT-I5500L":["Samsung","Galaxy 5"],"GT-I5500M":["Samsung","Galaxy 5"],"GT-I5500-MR3":["Samsung","Galaxy 5"],"GT-I5503":["Samsung","Galaxy 5"],"GT-I5508":["Samsung","Galaxy 5"],"GT-I5510":["Samsung","Galaxy 551"],"GT-I5510L":["Samsung","Galaxy 551"],"GT-I5510M":["Samsung","Galaxy 551"],"GT-I5510T":["Samsung","Galaxy 551"],"GT-I5700":["Samsung","Galaxy Spica"],"GT-I5700L":["Samsung","Galaxy Spica"],"GT-I5800":["Samsung","Galaxy Apollo"],"GT-I5800D":["Samsung","Galaxy Apollo"],"GT-I5800L":["Samsung","Galaxy Apollo"],"GT-I5801":["Samsung","Galaxy Apollo"],"GT-I6500U":["Samsung","Saturn"],"GT-I8000":["Samsung","Omnia 2"],"GT-I8150":["Samsung","Galaxy W"],"GT-I8150B":["Samsung","Galaxy W"],"GT-I8160":["Samsung","Galaxy Ace 2"],"GT-I8160L":["Samsung","Galaxy Ace 2"],"GT-I8160P":["Samsung","Galaxy Ace 2"],"GT-I8320":["Samsung","H1"],"GT-I8520":["Samsung","Galaxy Beam"],"GT-I8530":["Samsung","Galaxy Beam"],"GT-I8250":["Samsung","Galaxy Beam"],"GT-i9000":["Samsung","Galaxy S"],"GT-I9000":["Samsung","Galaxy S"],"GT-I9000B":["Samsung","Galaxy S"],"GT-I9000M":["Samsung","Galaxy S Vibrant"],"GT-I9000T":["Samsung","Galaxy S"],"GT-I9001":["Samsung","Galaxy S Plus"],"GT-I9003":["Samsung","Galaxy SL"],"GT-I9003L":["Samsung","Galaxy SL"],"GT-I9008":["Samsung","Galaxy S"],"GT-I9008L":["Samsung","Galaxy S"],"GT-I9010":["Samsung","Galaxy S Giorgio Armani"],"GT-I9018":["Samsung","Galaxy GT-I9018"],"GT-I9070":["Samsung","Galaxy S Advance"],"GT-I9070P":["Samsung","Galaxy S Advance"],"GT-I9082":["Samsung","Galaxy Grand DUOS"],"GT-I9088":["Samsung","Galaxy S"],"GT-i9100":["Samsung","Galaxy S II"],"GT-I9100":["Samsung","Galaxy S II"],"GT-I9100G":["Samsung","Galaxy S II"],"GT-I9100M":["Samsung","Galaxy S II"],"GT-I9100T":["Samsung","Galaxy S II"],"GT-I9100P":["Samsung","Galaxy S II"],"GT-I9103":["Samsung","Galaxy R"],"GT-I9108":["Samsung","Galaxy S II"],"GT-I9210":["Samsung","Galaxy S II LTE"],"GT-I9210T":["Samsung","Galaxy S II LTE"],"GT-I9220":["Samsung","Galaxy Note"],"GT-I9228":["Samsung","Galaxy Note"],"GT-I9250":["Samsung","Galaxy Nexus"],"GT-I9250 EUR XX":["Samsung","Galaxy Nexus"],"GT-I9260":["Samsung","Galaxy Premier"],"GT-I9300":["Samsung","Galaxy S III"],"GT-I9300T":["Samsung","Galaxy S III"],"GT-I9303T":["Samsung","Galaxy S III"],"GT-I9308":["Samsung","Galaxy S III"],"GT-I9500":["Samsung","Galaxy GT-I9500"],"GT-I9800":["Samsung","Galaxy GT-I9800"],"GT-N7000":["Samsung","Galaxy Note"],"GT-N7000B":["Samsung","Galaxy Note"],"GT-N7100":["Samsung","Galaxy Note II"],"GT-N7102":["Samsung","Galaxy Note II"],"GT-N8000":["Samsung","Galaxy Note 10.1"],"GT-N8010":["Samsung","Galaxy Note 10.1"],"GT-P1000":["Samsung","Galaxy Tab","tablet"],"GT-P1000L":["Samsung","Galaxy Tab","tablet"],"GT-P1000M":["Samsung","Galaxy Tab","tablet"],"GT-P1000N":["Samsung","Galaxy Tab","tablet"],"GT-P1000T":["Samsung","Galaxy Tab","tablet"],"GT-P1000 Tablet":["Samsung","Galaxy Tab","tablet"],"GT-P1010":["Samsung","Galaxy Tab","tablet"],"GT-P3100":["Samsung","Galaxy Tab 2 (7.0)","tablet"],"GT-P3100B":["Samsung","Galaxy Tab 2 (7.0)","tablet"],"GT-P3110":["Samsung","Galaxy Tab 2 (7.0)","tablet"],"GT-P3113":["Samsung","Galaxy Tab 2 (7.0)","tablet"],"GT-P5100":["Samsung","Galaxy Tab 2 (10.1)","tablet"],"GT-P5110":["Samsung","Galaxy Tab 2 (10.1)","tablet"],"GT-P5113":["Samsung","Galaxy Tab 2 (10.1)","tablet"],"GT-P6200":["Samsung","Galaxy Tab 7.0 Plus","tablet"],"GT-P6200L":["Samsung","Galaxy Tab 7.0 Plus","tablet"],"GT-P6201":["Samsung","Galaxy Tab 7.0 Plus N","tablet"],"GT-P6210":["Samsung","Galaxy Tab 7.0 Plus","tablet"],"GT-P6211":["Samsung","Galaxy Tab 7.0 Plus N","tablet"],"GT-P6800":["Samsung","Galaxy Tab 7.7","tablet"],"GT-P6810":["Samsung","Galaxy Tab 7.7","tablet"],"GT-P7100":["Samsung","Galaxy Tab 10.1V","tablet"],"GT-P7300":["Samsung","Galaxy Tab 8.9","tablet"],"GT-P7300B":["Samsung","Galaxy Tab 8.9","tablet"],"GT-P7310":["Samsung","Galaxy Tab 8.9","tablet"],"GT-P7320":["Samsung","Galaxy Tab 8.9","tablet"],"GT-P7320T":["Samsung","Galaxy Tab 8.9","tablet"],"GT-P7500":["Samsung","Galaxy Tab 10.1","tablet"],"GT-P7500D":["Samsung","Galaxy Tab 10.1","tablet"],"GT-P7500R":["Samsung","Galaxy Tab 10.1","tablet"],"GT-P7500V":["Samsung","Galaxy Tab 10.1","tablet"],"GT-P7501":["Samsung","Galaxy Tab 10.1N","tablet"],"GT-P7510":["Samsung","Galaxy Tab 10.1","tablet"],"GT-P7511":["Samsung","Galaxy Tab 10.1N","tablet"],"GT-S5300":["Samsung","Galaxy Pocket"],"GT-S5360":["Samsung","Galaxy Y"],"GT-S5360B":["Samsung","Galaxy Y"],"GT-S5360L":["Samsung","Galaxy Y"],"GT-S5363":["Samsung","Galaxy Y"],"GT-S5367":["Samsung","Galaxy Y TV"],"GT-S5368":["Samsung","GT-S5368"],"GT-S5369":["Samsung","Galaxy Y"],"GT-S5570":["Samsung","Galaxy Mini"],"GT-S5570B":["Samsung","Galaxy Mini"],"GT-S5570I":["Samsung","Galaxy Mini"],"GT-S5570L":["Samsung","Galaxy Mini"],"GT-S5578":["Samsung","Galaxy Mini"],"GT-S5660":["Samsung","Galaxy Gio"],"GT-S5660M":["Samsung","Galaxy Gio"],"GT-S5660V":["Samsung","Galaxy Gio"],"GT-S5670":["Samsung","Galaxy Fit"],"GT-S5670B":["Samsung","Galaxy Fit"],"GT-S5670L":["Samsung","Galaxy Fit"],"GT-S5690":["Samsung","Galaxy Xcover"],"GT-S5690L":["Samsung","Galaxy Xcover"],"GT-S5820":["Samsung","Galaxy Ace"],"GT-S5830":["Samsung","Galaxy Ace"],"GT-S5830B":["Samsung","Galaxy Ace"],"GT-S5830C":["Samsung","Galaxy Ace"],"GT-S5830D":["Samsung","Galaxy Ace"],"GT-S5830D-parrot":["Samsung","Galaxy Ace"],"GT-S5830i":["Samsung","Galaxy Ace"],"GT-S5830L":["Samsung","Galaxy Ace"],"GT-S5830M":["Samsung","Galaxy Ace"],"GT-S5830T":["Samsung","Galaxy Ace"],"GT-S5838":["Samsung","Galaxy Ace"],"GT-S5839i":["Samsung","Galaxy Ace"],"GT-S6102":["Samsung","Galaxy Y Duos"],"GT-S6102B":["Samsung","Galaxy Y Duos"],"GT-S6500":["Samsung","Galaxy Mini 2"],"GT-S6500D":["Samsung","Galaxy Mini 2"],"GT-S6702":["Samsung","GT-S6702"],"GT-S6802":["Samsung","Galaxy Ace Duos"],"GT-S7500":["Samsung","Galaxy Ace Plus"],"GT-S7500L":["Samsung","Galaxy Ace Plus"],"GT-S7500W":["Samsung","Galaxy Ace Plus"],"GT-T959":["Samsung","Galaxy S Vibrant"],"SCH-i509":["Samsung","Galaxy Y"],"SCH-i559":["Samsung","Galaxy Pop"],"SCH-i569":["Samsung","Galaxy Gio"],"SCH-i579":["Samsung","Galaxy Ace"],"SCH-i589":["Samsung","Galaxy Ace Duos"],"SCH-i705 4G":["Samsung","Galaxy Tab 2 (7.0)","tablet"],"SCH-i809":["Samsung","SCH-i809"],"SCH-i889":["Samsung","Galaxy Note"],"SCH-i909":["Samsung","Galaxy S"],"SCH-i919":["Samsung","SCH-i919"],"SCH-i929":["Samsung","SCH-i929"],"SCH-I100":["Samsung","Gem"],"SCH-I110":["Samsung","Illusion"],"SCH-I400":["Samsung","Continuum"],"SCH-I405":["Samsung","Stratosphere"],"SCH-I405 4G":["Samsung","Stratosphere"],"SCH-I500":["Samsung","Fascinate"],"SCH-I510":["Samsung","Stealth V"],"SCH-I510 4G":["Samsung","Droid Charge"],"SCH-I515":["Samsung","Galaxy Nexus"],"SCH-I535":["Samsung","Galaxy S III"],"SCH-I535 4G":["Samsung","Galaxy S III"],"SCH-I619":["Samsung","SCH-I619"],"SCH-I699":["Samsung","SCH-I699"],"SCH-I779":["Samsung","SCH-I779"],"SCH-I800":["Samsung","Galaxy Tab 7.0","tablet"],"SCH-I815":["Samsung","Galaxy Tab 7.7","tablet"],"SCH-I815 4G":["Samsung","Galaxy Tab 7.7","tablet"],"SCH-I905":["Samsung","Galaxy Tab 10.1","tablet"],"SCH-I905 4G":["Samsung","Galaxy Tab 10.1","tablet"],"SCH-I909":["Samsung","Galaxy S"],"SCH-I915":["Samsung","SCH-I915"],"SCH-I939":["Samsung","Galaxy S III"],"SCH-M828C":["Samsung","Galaxy Precedent"],"SCH-M828Carray(9096483449)":["Samsung","Galaxy Precedent"],"SCH-R530U":["Samsung","Galaxy S III"],"SCH-R680":["Samsung","Repp"],"SCH-R720":["Samsung","Admire"],"SCH-R730":["Samsung","Transfix"],"SCH-R760":["Samsung","Galaxy S II"],"SCH-R820":["Samsung","SCH-R820"],"SCH-R880":["Samsung","Acclaim"],"SCH-R910":["Samsung","Galaxy Indulge 4G"],"SCH-R915":["Samsung","Galaxy Indulge"],"SCH-R920":["Samsung","Galaxy Attain 4G"],"SCH-R930":["Samsung","Galaxy S Aviator"],"SCH-R940":["Samsung","Galaxy S Lightray"],"SCH-S720C":["Samsung","Galaxy Proclaim"],"SCH-S735C":["Samsung","SCH-S735"],"SCH-W899":["Samsung","SCH-W899"],"SCH-W999":["Samsung","SCH-W999"],"SGH-I547":["Samsung","SGH-I547"],"SGH-I717":["Samsung","Galaxy Note"],"SGH-I717D":["Samsung","Galaxy Note"],"SGH-I717M":["Samsung","Galaxy Note"],"SGH-I717R":["Samsung","Galaxy Note"],"SGH-I727":["Samsung","Galaxy S II Skyrocket"],"SGH-i727R":["Samsung","Galaxy S II"],"SGH-I727R":["Samsung","Galaxy S II"],"SGH-I747":["Samsung","Galaxy S III"],"SGH-I747M":["Samsung","Galaxy S III"],"SGH-I748":["Samsung","Galaxy S III"],"SGH-I757":["Samsung","Galaxy S II Skyrocket HD"],"SGH-I777":["Samsung","Galaxy S II"],"SGH-I9777":["Samsung","Galaxy S II"],"SGH-I896":["Samsung","Captivate"],"SGH-I897":["Samsung","Captivate"],"SGH-I927":["Samsung","Captivate Glide"],"SGH-I927R":["Samsung","Captivate Glide"],"SGH-I957":["Samsung","Galaxy Tab 8.9","tablet"],"SGH-I957D":["Samsung","Galaxy Tab 8.9","tablet"],"SGH-I957M":["Samsung","Galaxy Tab 8.9","tablet"],"SGH-I957R":["Samsung","Galaxy Tab 8.9","tablet"],"SGH-I987":["Samsung","Galaxy Tab 7.0","tablet"],"SGH-I997":["Samsung","Infuse 4G"],"SGH-I997R":["Samsung","Infuse 4G"],"SGH-I9000":["Samsung","Galaxy S"],"SGH-S730G":["Samsung","SGH-S730"],"SGH-T499":["Samsung","Dart"],"SGH-T499V":["Samsung","Galaxy Mini"],"SGH-T499Y":["Samsung","Galaxy Mini"],"SGH-T589":["Samsung","Gravity Smart"],"SGH-T589R":["Samsung","Gravity Smart"],"SGH-T679":["Samsung","Exhibit II 4G"],"SGH-T679M":["Samsung","Exhibit II 4G"],"SGH-T759":["Samsung","Exhibit 4G"],"SGH-T769":["Samsung","Galaxy S Blaze 4G"],"SGH-T839":["Samsung","T-Mobile Sidekick"],"SGH-T849":["Samsung","Galaxy Tab 7.0","tablet"],"SGH-T859":["Samsung","Galaxy Tab 10.1","tablet"],"SGH-T869":["Samsung","Galaxy Tab 7.0 Plus","tablet"],"SGH-T879":["Samsung","Galaxy Note"],"SGH-T959":["Samsung","Vibrant"],"SGH-T959D":["Samsung","Galaxy S Fascinate 3G+"],"SGH-T959P":["Samsung","Galaxy S Fascinate 4G"],"SGH-T959V":["Samsung","Galaxy S 4G"],"SGH-T989":["Samsung","Galaxy S II"],"SGH-T989D":["Samsung","Galaxy S II X"],"SGH-T999":["Samsung","Galaxy S Blaze 4G"],"SGH-T999V":["Samsung","Galaxy S Blaze 4G"],"SHV-E120K":["Samsung","Galaxy S II HD LTE"],"SHV-E120L":["Samsung","Galaxy S II HD LTE"],"SHV-E120S":["Samsung","Galaxy S II HD LTE"],"SHV-E110S":["Samsung","Galaxy S II LTE"],"SHV-E140S":["Samsung","Galaxy Tab 8.9","tablet"],"SHV-E150S":["Samsung","Galaxy Tab 7.7","tablet"],"SHV-E160K":["Samsung","Galaxy Note"],"SHV-E160L":["Samsung","Galaxy Note LTE"],"SHV-E160S":["Samsung","Galaxy Note LTE"],"SHV-E170K":["Samsung","SHV-E170K"],"SHV-E170L":["Samsung","SHV-E170L"],"SHV-E210K":["Samsung","Galaxy S III"],"SHV-E210L":["Samsung","Galaxy S III"],"SHV-E210S":["Samsung","Galaxy S III"],"SHW-M100S":["Samsung","Galaxy A"],"SHW-M110S":["Samsung","Galaxy S"],"SHW-M130L":["Samsung","Galaxy U"],"SHW-M130K":["Samsung","Galaxy K"],"SHW-M180K":["Samsung","Galaxy Tab","tablet"],"SHW-M180L":["Samsung","Galaxy Tab","tablet"],"SHW-M180S":["Samsung","Galaxy Tab","tablet"],"SHW-M180W":["Samsung","Galaxy Tab","tablet"],"SHW-M185S":["Samsung","Galaxy Tab","tablet"],"SHW-M190S":["Samsung","Galaxy S Hoppin"],"SHW-M220L":["Samsung","Galaxy Neo"],"SHW-M240S":["Samsung","Galaxy Ace"],"SHW-M250K":["Samsung","Galaxy S II"],"SHW-M250L":["Samsung","Galaxy S II"],"SHW-M250S":["Samsung","Galaxy S II"],"SHW-M300W":["Samsung","Galaxy Tab 10.1","tablet"],"SHW-M305W":["Samsung","Galaxy Tab 8.9","tablet"],"SHW-M340S":["Samsung","Galaxy M Style"],"SHW-M380K":["Samsung","Galaxy Tab 10.1","tablet"],"SHW-M380S":["Samsung","Galaxy Tab 10.1","tablet"],"SHW-M380W":["Samsung","Galaxy Tab 10.1","tablet"],"SHW-M440S":["Samsung","Galaxy S III"],"SMT-i9100":["Samsung","SMT-I9100","tablet"],"SPH-D600":["Samsung","Conquer 4G"],"SPH-D700":["Samsung","Epic 4G"],"SPH-D705":["Samsung","Epic 4G 2"],"SPH-D710":["Samsung","Epic 4G Touch"],"SPH-L700":["Samsung","Galaxy Nexus"],"SPH-L710":["Samsung","Galaxy S III"],"SPH-M820":["Samsung","Galaxy Prevail"],"SPH-M820-BST":["Samsung","Galaxy Prevail"],"SPH-M580":["Samsung","Replenish"],"SPH-M900":["Samsung","Moment"],"SPH-M910":["Samsung","Intercept"],"SPH-M920":["Samsung","Transform"],"SPH-M930":["Samsung","Transform Ultra"],"SPH-M930BST":["Samsung","Transform Ultra"],"SPH-P100":["Samsung","Galaxy Tab","tablet"],"YP-GB1":["Samsung","Galaxy Player","media"],"YP-GB70":["Samsung","Galaxy Player 70","media"],"YP-GB70D":["Samsung","Galaxy Player 70 Plus","media"],"YP-GS1":["Samsung","Galaxy S WiFi 3.6","media"],"YP-G1":["Samsung","Galaxy S WiFi 4.0","media"],"YP-GI1":["Samsung","Galaxy S WiFi 4.2","media"],"YP-G50":["Samsung","Galaxy Player","media"],"YP-G70":["Samsung","Galaxy S WiFi 5.0","media"],GT9100:["Samsung","Galaxy S II"],I897:["Samsung","Captivate"],I7500:["Samsung","Galaxy"],I9000:["Samsung","Galaxy S"],T959:["Samsung","Galaxy S Vibrant"],"Captivate-I897":["Samsung","Captivate"],Galaxy:["Samsung","Galaxy"],"Galaxy Note":["Samsung","Galaxy Note"],GalaxyS:["Samsung","Galaxy S"],"Galaxy S II":["Samsung","Galaxy S II"],"Galaxy X":["Samsung","Galaxy X"],"Galaxy Spica":["Samsung","Galaxy Spica"],"GALAXY Tab":["Samsung","Galaxy Tab","tablet"],"GALAXY NEXUS":["Samsung","Galaxy Nexus"],Vibrantmtd:["Samsung","Vibrant"],"SC-01C":["Samsung","Galaxy Tab","tablet"],"SC-01D":["Samsung","Galaxy Tab 10.1 LTE","tablet"],"SC-02B":["Samsung","Galaxy S"],"SC-02C":["Samsung","Galaxy S II"],"SC-02D":["Samsung","Galaxy Tab 7.0 Plus","tablet"],"SC-03D":["Samsung","Galaxy S II LTE"],"SC-04D":["Samsung","Galaxy Nexus"],"SC-05D":["Samsung","Galaxy Note LTE"],"SC-06D":["Samsung","Galaxy S III"],ISW11SC:["Samsung","Galaxy S II WiMAX"],"GT-S7562":["Samsung","GT-S7562"],"GT-S7562i":["Samsung","GT-S7562i"],A01SH:["Sharp","A01SH"],IS01:["Sharp","IS01"],IS03:["Sharp","IS03"],IS05:["Sharp","IS05"],IS11SH:["Sharp","Aquos IS11SH"],IS12SH:["Sharp","Aquos IS12SH"],IS13SH:["Sharp","Aquos IS13SH"],IS14SH:["Sharp","Aquos IS14SH"],ISW16SH:["Sharp","Aquos ISW16SH"],"EB-W51GJ":["Sharp","EB-W51GJ"],SBM003SH:["Sharp","Galapagos"],SBM005SH:["Sharp","Galapagos"],SBM006SH:["Sharp","Aquos"],SBM007SH:["Sharp","Aquos 007SH"],SBM009SH:["Sharp","Aquos 009SH"],SBM102SH:["Sharp","Aquos 102SH"],SBM103SH:["Sharp","Aquos 103SH"],SBM104SH:["Sharp","Aquos 104SH"],SBM107SH:["Sharp","Aquos 107SH"],SBM107SHB:["Sharp","Aquos 107SH"],"SH-01D":["Sharp","Aquos SH-01D"],"SH-02D":["Sharp","Aquos slider SH-02D"],"SH-03C":["Sharp","Lynx 3D"],"SH-06D":["Sharp","Aquos SH-06D"],"SH-09D":["Sharp","Aquos Zeta SH-09D"],"SH-10B":["Sharp","Lynx"],"SH-12C":["Sharp","Aquos"],"SH-13C":["Sharp","Aquos f SH-13C"],SH80F:["Sharp","Aquos SH80F"],SH72x8U:["Sharp","SH72x8U"],SH8118U:["Sharp","SH8118U"],SH8128U:["Sharp","SH8128U"],SH8158U:["Sharp","SH8158U"],SH8188U:["Sharp","SH8188U"],SH8268U:["Sharp","SH8268U"],"INFOBAR C01":["Sharp","INFOBAR C01"],"SPX-5":["Simvalley","SPX-5"],"SPX-5 3G":["Simvalley","SPX-5 3G"],"SmartQ G7":["SmartQ","G7","tablet"],SmartQT7:["SmartQ","T7","tablet"],SmartQT10:["SmartQ","T10","tablet"],SmartQT15:["SmartQ","T15","tablet"],SmartQT19:["SmartQ","T19","tablet"],SmartQT20:["SmartQ","T20","tablet"],"OMS1 6":["Sony Ericsson","A8i"],E10a:["Sony Ericsson","Xperia X10 Mini"],E10i:["Sony Ericsson","Xperia X10 Mini"],E10iv:["Sony Ericsson","Xperia X10 Mini"],E15:["Sony Ericsson","Xperia X8"],E15a:["Sony Ericsson","Xperia X8"],E15i:["Sony Ericsson","Xperia X8"],E15iv:["Sony Ericsson","Xperia X8"],"E15i-o":["Sony Ericsson","Xperia X8"],E16i:["Sony Ericsson","W8 Walkman"],LT11i:["Sony Ericsson","Xperia Neo V"],LT15:["Sony Ericsson","Xperia Arc"],LT15a:["Sony Ericsson","Xperia Arc"],LT15i:["Sony Ericsson","Xperia Arc"],LT15iv:["Sony Ericsson","Xperia Arc"],"LT15i-o":["Sony Ericsson","Xperia Arc"],LT18a:["Sony Ericsson","Xperia Arc S"],LT18i:["Sony Ericsson","Xperia Arc S"],LT18iv:["Sony Ericsson","Xperia Arc S"],"LT18i-o":["Sony Ericsson","Xperia Arc S"],LT22i:["Sony","Xperia P"],LT26i:["Sony","Xperia S"],LT26ii:["Sony","Xperia S"],"LT26i-o":["Sony","Xperia S"],LT28at:["Sony","Xperia Ion"],LT28h:["Sony","Xperia Ion"],LT28i:["Sony","Xperia Ion"],LT29i:["Sony","Xperia GX"],SonyLT29i:["Sony","Xperia GX"],SonyLT30a:["Sony","Xperia Mint"],SonyLT30p:["Sony","Xperia Mint"],MK16a:["Sony Ericsson","Xperia Pro"],MK16i:["Sony Ericsson","Xperia Pro"],MT11a:["Sony Ericsson","Xperia Neo V"],MT11i:["Sony Ericsson","Xperia Neo V"],MT11iv:["Sony Ericsson","Xperia Neo V"],"MT11i-o":["Sony Ericsson","Xperia Neo V"],MT15a:["Sony Ericsson","Xperia Neo"],MT15i:["Sony Ericsson","Xperia Neo"],MT15iv:["Sony Ericsson","Xperia Neo"],"MT15i-o":["Sony Ericsson","Xperia Neo"],MT25i:["Sony","Xperia Neo L"],MT27i:["Sony","Xperia Sola"],R800a:["Sony Ericsson","Xperia Play"],R800i:["Sony Ericsson","Xperia Play"],R800iv:["Sony Ericsson","Xperia Play"],R800at:["Sony Ericsson","Xperia Play"],R800x:["Sony Ericsson","Xperia Play"],SK17a:["Sony Ericsson","Xperia Mini Pro"],SK17i:["Sony Ericsson","Xperia Mini Pro"],SK17iv:["Sony Ericsson","Xperia Mini Pro"],"SK17i-o":["Sony Ericsson","Xperia Mini Pro"],ST15a:["Sony Ericsson","Xperia Mini"],ST15i:["Sony Ericsson","Xperia Mini"],ST17a:["Sony Ericsson","Xperia Active"],ST17i:["Sony Ericsson","Xperia Active"],ST18a:["Sony Ericsson","Xperia Ray"],ST18i:["Sony Ericsson","Xperia Ray"],ST18iv:["Sony Ericsson","Xperia Ray"],ST18av:["Sony Ericsson","Xperia Ray"],SonyST21:["Sony","'Tapioca'"],SonyST21i:["Sony","'Tapioca'"],SonyST21a2:["Sony","'Tapioca'"],ST21:["Sony","'Tapioca'"],ST21i:["Sony","'Tapioca'"],SonyST23i:["Sony","'Tapioca DS'"],ST25i:["Sony","Xperia U"],ST27i:["Sony","Xperia Go"],U20a:["Sony Ericsson","Xperia X10 Mini Pro"],U20i:["Sony Ericsson","Xperia X10 Mini Pro"],U20iv:["Sony Ericsson","Xperia X10 Mini Pro"],WT13i:["Sony Ericsson","Mix Walkman"],WT18i:["Sony Ericsson","Walkman"],WT19a:["Sony Ericsson","Live with Walkman"],WT19i:["Sony Ericsson","Live with Walkman"],WT19iv:["Sony Ericsson","Live with Walkman"],X8:["Sony Ericsson","Xperia X8"],X10:["Sony Ericsson","Xperia X10"],X10a:["Sony Ericsson","Xperia X10"],X10i:["Sony Ericsson","Xperia X10"],X10iv:["Sony Ericsson","Xperia X10"],X10S:["Sony Ericsson","Xperia X10"],X10mini:["Sony Ericsson","Xperia X10 Mini"],"X10 Mini":["Sony Ericsson","Xperia X10 Mini"],"X10 Mini Pro":["Sony Ericsson","Xperia X10 Mini Pro"],Z1i:["Sony Ericsson","Xperia Play"],S51SE:["Sony Ericsson","Xperia Mini"],IS11S:["Sony Ericsson","Xperia Acro"],IS12S:["Sony Ericsson","Xperia Acro HD"],"SO-01B":["Sony Ericsson","Xperia X10"],"SO-01C":["Sony Ericsson","Xperia Arc"],"SO-01D":["Sony Ericsson","Xperia Play"],"SO-02C":["Sony Ericsson","Xperia Acro"],"SO-02D":["Sony Ericsson","Xperia NX"],"SO-03C":["Sony Ericsson","Xperia Ray"],"SO-03D":["Sony Ericsson","Xperia Acro HD"],"SO-04D":["Sony","Xperia GX"],"SO-05D":["Sony","Xperia SX"],"XPERIA X8":["Sony Ericsson","Xperia X8"],"Xperia X8":["Sony Ericsson","Xperia X8"],"Xperia X10":["Sony Ericsson","Xperia X10"],"Xperia ray":["Sony Ericsson","Xperia Ray"],"Xperia Ray":["Sony Ericsson","Xperia Ray"],"Xperia Arc":["Sony Ericsson","Xperia Arc"],"Xperia Mini":["Sony Ericsson","Xperia Mini"],"Xperia neo":["Sony Ericsson","Xperia Neo"],"Xperia Neo":["Sony Ericsson","Xperia Neo"],"XPERIA NEO":["Sony Ericsson","Xperia Neo"],"Xperia NeoV":["Sony Ericsson","Xperia Neo V"],"Xperia Neo V":["Sony Ericsson","Xperia Neo V"],"Xperia Play":["Sony Ericsson","Xperia Play"],"Sony Ericsson Xperia X1":["Sony Ericsson","Xperia X1"],SonyHayabusa:["Sony","Xperia Ion"],Hayabusa:["Sony","Xperia Ion"],nozomi:["Sony","Xperia S"],"Sony Tablet P":["Sony","Tablet P","tablet"],"Sony Tablet S":["Sony","Tablet S","tablet"],"NWZ-Z1000Series":["Sony","Walkman Z","media"],"NW-Z1000Series":["Sony","Walkman Z","media"],"Spice Mi280":["Spice","Mi-280"],"Spice Mi300":["Spice","Mi-300"],"Spice Mi-310":["Spice","Mi-310"],"Spice Mi-425":["Spice","Mi-425"],"SPICE Mi-720":["Spice","Mi-720"],"A7272+":["Star","A7272+"],"e1109 v73 gq1002 ctp":["Star","X18i"],TS1004T:["Surf 3Q","TS1004T","tablet"],"SYTABEX7-2":["Sylvania","SYTABEX7","tablet"],"TCL A860":["TCL","A860"],"TCL A906":["TCL","A906"],"TCL A909":["TCL","A909"],"TCL A919":["TCL","A919"],"TCL A990":["TCL","A990"],"TCL A996":["TCL","A996"],"TCL A998":["TCL","A998"],"TCL GENESEE E708":["TCL","Genesee E708"],"A10t(5DM3)":["Teclast","A10T","tablet"],P72:["Teclast","P72","tablet"],P76TI:["Teclast","P76Ti","tablet"],P81HD:["Teclast","P81HD","tablet"],"P85(R8A1)":["Teclast","P85","tablet"],"T720 SE":["Teclast","T720","tablet"],"T760 from moage.com":["Teclast","T760","tablet"],tegav2:["Tegatech","TEGA v2","tablet"],"TM-7025":["teXet","TM-7025","tablet"],MoFing:["Thomson","MoFing","tablet"],Ultimate10:["Tomtec","Ultimate10","tablet"],"Thl V7":["THL","V7"],"ThL V7":["THL","V7"],"ThL V8":["THL","V8"],"ThL V9":["THL","V9"],"ThL V11":["THL","V11"],"TSB CLOUD COMPANION;TOSHIBA AC AND AZ":["Toshiba","Dynabook AZ","desktop"],"TOSHIBA AC AND AZ":["Toshiba","Dynabook AZ","desktop"],"TOSHIBA FOLIO AND A":["Toshiba","Folio 100","tablet"],"T-01C":["Toshiba","Regza T-01C"],"T-01D":["Toshiba","Regza T-01D"],IS04:["Toshiba","Regza IS04"],IS11T:["Toshiba","Regza IS11T"],AT1S0:["Toshiba","Regza AT1S0"],Tostab03:["Toshiba","Regza AT100","tablet"],AT100:["Toshiba","Regza AT100","tablet"],AT200:["Toshiba","Regza AT200","tablet"],AT470:["Toshiba","Regza AT470","tablet"],AT570:["Toshiba","Regza AT570","tablet"],AT830:["Toshiba","Regza AT830","tablet"],"Folio 100":["Toshiba","Folio 100","tablet"],folio100:["Toshiba","Folio 100","tablet"],THRiVE:["Toshiba","THRiVE","tablet"],"Fantastic T3":["TWM","Fantastic T3"],M70014:["United Star Technology","M70014","tablet"],PS47:["Velocity Micro","Cruz PS47","tablet"],T301:["Velocity Micro","Cruz T301","tablet"],"Vibo-A688":["FIH","Vibo A688"],"Videocon-V7500":["Videocon","V7500"],GTablet:["ViewSonic","gTablet","tablet"],GtabComb:["ViewSonic","gTablet","tablet"],"TeamDRH ICS for GTablet":["ViewSonic","gTablet","tablet"],ViewPad7:["ViewSonic","ViewPad 7","tablet"],"ViewPad 10e":["ViewSonic","ViewPad 10e","tablet"],VTAB1008:["Vizio","VTAB1008","tablet"],VTAB3010:["Vizio","VTAB3010","tablet"],"VOTO W5300":["VOTO","W5300"],"xPAD-70":["WayteQ","xPAD-70","tablet"],"xTAB-70":["WayteQ","xTAB-70","tablet"],"WellcoM-A99":["WellcoM","A99"],N12:["Window","N12","tablet"],N12R:["Window","N12R","tablet"],N50:["Window","N50","tablet"],N50DT:["Window","N50DT","tablet"],N50GT:["Window","N50GT","tablet"],"N50GT A":["Window","N50GT-A","tablet"],N70:["Window","N70","tablet"],"N70 DUAL CORE":["Window","N70 Dual Core","tablet"],N80:["Window","N80","tablet"],N90:["Window","N90","tablet"],"N90 DUAL CORE2 V12":["Window","N90 Dual Core","tablet"],N612:["Wishway","N612"],"AT-AS43D":["Wolfgang","AT-AS43D"],M12:["Wopad","M12","tablet"],WM8650:["WonderMedia","WM8650","tablet"],"MI-ONE":["Xiaomi","MI-ONE"],"MI-ONE C1":["Xiaomi","MI-ONE C1"],"MI-ONE Plus":["Xiaomi","MI-ONE Plus"],"MI 1S":["Xiaomi","MI-ONE Plus"],"MI 1SC":["Xiaomi","MI-ONE 1SC"],"mione plus":["Xiaomi","MI-ONE Plus"],"MI-TWO":["Xiaomi","MI-TWO"],"MI 2":["Xiaomi","MI-TWO"],"MI 2S":["Xiaomi","MI-TWO Plus"],"MI 2SC":["Xiaomi","MI-TWO Plus"],Q07CL01:["XVision","Q07","tablet"],N6:["Yarvik","210 Tablet","tablet"],EMR1879:["Yidong","EMR1879","tablet"],"yusun W702":["Yusun","W702"],"YX-YUSUN E80":["Yusun","E80"],zt180:["Zenithink","ZT-180","tablet"],Jaguar7:["ZiiLabs","Jaguar 7","tablet"],"Ziss Ranger HD":["Ziss","Ranger HD"],"ZTE Libra":["ZTE","Libra"],"ZTE-T T9":["ZTE","Light Tab T9","tablet"],V9:["ZTE","Light Tab V9","tablet"],"V9e+":["ZTE","Light Tab 2","tablet"],V9A:["ZTE","Light Tab 2","tablet"],"Light Tab 2W":["ZTE","Light Tab 2","tablet"],"Light Tab 2":["ZTE","Light Tab 2","tablet"],V9C:["ZTE","Light Tab 3","tablet"],V55:["ZTE","Optik","tablet"],Acqua:["ZTE","Acqua"],Blade:["ZTE","Blade"],"Blade-V880":["ZTE","Blade"],"ZTE-U V880":["ZTE","Blade"],"Blade-opda":["ZTE","Blade"],"ZTE-BLADE":["ZTE","Blade"],"ZTE Blade":["ZTE","Blade"],"ZTE V880":["ZTE","Blade"],"ZTE-U(V)880+":["ZTE","Blade"],V880:["ZTE","Blade"],a5:["ZTE","Blade"],Blade2:["ZTE","Blade 2"],"Blade S":["ZTE","Blade S"],X500:["ZTE","Score"],"ZTE-X500":["ZTE","Score"],Skate:["ZTE","Skate"],"ZTE Skate":["ZTE","Skate"],"ZTE-Skate":["ZTE","Skate"],"ZTE-SKATE":["ZTE","Skate"],"ZTE-V960":["ZTE","Skate"],"ZTE-U V960":["ZTE","Skate"],"ZTE Racer":["ZTE","Racer"],"ZTE-RACER":["ZTE","Racer"],"MTC 916":["ZTE","Racer"],Racer:["ZTE","Racer"],RacerII:["ZTE","Racer 2"],RACERII:["ZTE","Racer 2"],"ZTE Roamer":["ZTE","Roamer"],N860:["ZTE","Warp"],N880:["ZTE","Blade"],"ZTE-T U802":["ZTE","T-U802"],"ZTE-T U806":["ZTE","T-U806"],"ZTE-T U812":["ZTE","T-U812"],"ZTE-T U830":["ZTE","T-U830"],"ZTE-T U880":["ZTE","T-U880"],"ZTE T U880":["ZTE","T-U880"],"ZTE-TU880":["ZTE","T-U880"],"ZTE-TU900":["ZTE","T-U900"],"ZTE-T U960":["ZTE","T-U960"],"ZTE-TU960s":["ZTE","T-U960"],"ZTE-T U960s":["ZTE","T-U960"],"ZTE U N720":["ZTE","U-N720"],"ZTE-U V856":["ZTE","U-V856"],"ZTE-U V857":["ZTE","U-V857"],"ZTE-U V881":["ZTE","U-V881"],"ZTE-U X850":["ZTE","U-X850"],"ZTE-U X876":["ZTE","U-X876"],"ZTE-X876":["ZTE","U-X876"],"ZTE-C R750":["ZTE","C-R750"],"ZTE-C N600":["ZTE","C-N600"],"ZTE-C N600+":["ZTE","C-N600"],"ZTE-C N606":["ZTE","C-N606"],"ZTE-C N700":["ZTE","C-N700"],"ZTE-C N760":["ZTE","C-N760"],"ZTE-C N880":["ZTE","C-N880"],"ZTE-C N880S":["ZTE","C-N880"],"ZTE-C N880s":["ZTE","C-N880"],"ZTE-C X500":["ZTE","C-X500"],"ZTE-C X920":["ZTE","C-X920"],"ZXY-ZTE-C X920":["ZTE","C-X920"],"ZTE GV821":["ZTE","G-V821"],"ZTE N880E":["ZTE","N880E"],"ZTE-N880E":["ZTE","N880E"],"MIUI N880S":["ZTE","N880S"],"ZTE N882E":["ZTE","N882E"],"ZTE N855D":["ZTE","N855D"],"ZTE-N910":["ZTE","N910"],E810:["ZTE","E810"],u880:["ZTE","U880"],"ZTE U880E":["ZTE","U880E"],U880:["ZTE","U880"],"ZTE U970":["ZTE","U970"],"ZTE V768":["ZTE","V768"],"ZTE-V856":["ZTE","V856"],"ZTE V877b":["ZTE","V877"],"ZTE V889D":["ZTE","V889"],"ZTE-Z990":["ZTE","Z990"],ZTEU790:["ZTE","U790"],"003Z":["ZTE","Softbank 003Z"],"008Z":["ZTE","Softbank 008Z"],"009Z":["ZTE","Softbank Star7"],"i-mobile i691":["i-Mobile","i691"],"i-mobile i695":["i-Mobile","i695"],"i-mobile i858":["i-Mobile","i858"],"i-mobile 3G 8500":["i-Mobile","3G 8500"],"i-mobile I-Note":["i-Mobile","i-Note","tablet"],"Optimus Boston":["Optimus","Boston"],"Optimus San Francisco":["Optimus","San Francisco"],"Optimus Monte Carlo":["Optimus","Monte Carlo"],"Orange Boston":["Orange","Boston"],"Orange Monte Carlo":["Orange","Monte Carlo"],"San Francisco":["Orange","San Francisco"],"San Francisco for Orange":["Orange","San Francisco"],"Orange San Francisco":["Orange","San Francisco"],MOVE:["T-Mobile","MOVE"],"T-Mobile G1":["T-Mobile","G1"],"T-Mobile G2":["T-Mobile","G2"],"T-Mobile G2 Touch":["T-Mobile","G2"],"LG-P999":["T-Mobile","G2x"],"LG-E739":["T-Mobile","myTouch"],"T-Mobile myTouch 3G":["T-Mobile","myTouch 3G"],"T-Mobile myTouch 3G Slide":["T-Mobile","myTouch 3G Slide"],"T-Mobile Espresso":["T-Mobile","myTouch 3G Slide"],"HTC myTouch 3G Slide":["T-Mobile","myTouch 3G Slide"],"T-Mobile myTouch 4G":["T-Mobile","myTouch 4G"],"HTC Glacier":["T-Mobile","myTouch 4G"],"HTC Panache":["T-Mobile","myTouch 4G"],myTouch4G:["T-Mobile","myTouch 4G"],"My Touch 4G":["T-Mobile","myTouch 4G"],"HTC Mytouch 4G":["T-Mobile","myTouch 4G"],"HTC My Touch 4G":["T-Mobile","myTouch 4G"],"HTC mytouch4g":["T-Mobile","myTouch 4G"],"HTC myTouch 4G Slide":["T-Mobile","myTouch 4G Slide"],"myTouch 4G Slide":["T-Mobile","myTouch 4G Slide"],"T-Mobile myTouch Q":["T-Mobile","myTouch Q"],"LG-C800":["T-Mobile","myTouch Q"],"Pulse Mini":["T-Mobile","Pulse Mini"],"Vodafone 845":["Vodafone","845 Nova"],"Vodafone 858":["Vodafone","858 Smart"],"Vodafone 945":["Vodafone","945"],"Vodafone Smart II":["Vodafone","Smart II"],SmartTab10:["Vodafone","SmartTab 10","tablet"],"SCH-N719":["Samsung","Galaxy Note II"],"Coolpad 8190":["Coolpad","8190"],U705T:["Oppo","Ulike2"],"Coolpad 8020+":["Coolpad","8020"],"Huawei Y310-5000":["Huawei","Y310"],"GT-S7572":["Samsung","Galaxy Trend Duos II"],"Lenovo A278t":["Lenovo","A278t"],"Lenovo A690":["Lenovo","A690"],"GT-I8262D":["Samsung","LePhone I8262D"],"Lenovo A278t":["Lenovo","A278t"],"MI 2C":["Xiaomi","MI-TWO"],"Coolpad 8070":["Coolpad","8070"],R813T:["Oppo","R813T"],"ZTE U930":["ZTE","U930"],"Lenovo A360":["Lenovo","LePhone A360"],"SCH-N719":["Samsung","Galaxy Note II"],"Coolpad 8010":["Coolpad","8010"],"LENOVO-Lenovo-A288t":["Lenovo","A288t"],U701T:["Oppo","U701T"],ZTEU795:["Coolpad","U795"],"Haier-HT-I617":["Haier","I617"],ZTEU880s:["ZTE","T-U880"],"GT-S6352":["Samsung","GT-S6352"],"GT-S7568":["Samsung","GT-S7568"],"K-Touch T619+":["K-Touch","T619"],"MI 2A":["Xiaomi","MI-TWO A"],"GT-N7108":["Samsung","Galaxy Note II"],"K-Touch T621":["K-Touch","T621"],"LENOVO-Lenovo-A298t":["Lenovo","A298"],"Coolpad 8150":["Coolpad","8150"],"5860S":["Coolpad","5860"],ZTEU807:["ZTE","U807"],"SCH-I739":["Samsung","SCH-I739"],"SCH-I829":["Samsung","SCH-I829"],"HS-E830":["Hisense","E830"],"HS-E920":["Hisense","E920"],"Lenovo S720":["Lenovo","S720"],"MI 2C":["Xiaomi","MI-TWO"],"OPPO R813T":["Oppo","R813"],"SCH-I879":["Samsung","Galaxy Note"],"GT-S6102E":["Samsung","Galaxy Y Duos"]},C={9600:"Bold",9650:"Bold",9700:"Bold",9780:"Bold",9790:"Bold",9900:"Bold",9930:"Bold",8300:"Curve",8310:"Curve",8320:"Curve",8330:"Curve","8350i":"Curve",8520:"Curve",8530:"Curve",8900:"Curve",9220:"Curve",9300:"Curve",9330:"Curve",9350:"Curve",9360:"Curve",9370:"Curve",9380:"Curve",8100:"Pearl",8110:"Pearl",8120:"Pearl",8130:"Pearl",8220:"Pearl",8230:"Pearl",9100:"Pearl",9105:"Pearl",9530:"Storm",9550:"Storm",9670:"Style",9800:"Torch",9810:"Torch",9850:"Torch",9860:"Torch",9630:"Tour",9981:"Porsche P"},v=function(){this.initialize.apply(this,Array.prototype.slice.call(arguments))
};v.prototype={initialize:function(e){this.original=e.value||null,this.alias=e.alias||null}};var p=function(){this.initialize.apply(this,arguments)};return p.prototype={initialize:function(e,a){this.options={useFeatures:a&&a.useFeatures||!1,detectCamouflage:a&&a.detectCamouflage||!0},this.browser={stock:!0,hidden:!1,channel:""},this.engine={},this.os={},this.device={type:"desktop",identified:!1},this.camouflage=!1,this.features=[],this.detect(e)
},detect:function(c){if(c.match("Unix")&&(this.os.name="Unix"),c.match("FreeBSD")&&(this.os.name="FreeBSD"),c.match("OpenBSD")&&(this.os.name="OpenBSD"),c.match("NetBSD")&&(this.os.name="NetBSD"),c.match("SunOS")&&(this.os.name="Solaris"),c.match("Linux")&&(this.os.name="Linux",c.match("CentOS")&&(this.os.name="CentOS",(match=/CentOS\/[0-9\.\-]+el([0-9_]+)/.exec(c))&&(this.os.version=new v({value:match[1].replace(/_/g,".")}))),c.match("Debian")&&(this.os.name="Debian"),c.match("Fedora")&&(this.os.name="Fedora",(match=/Fedora\/[0-9\.\-]+fc([0-9]+)/.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Gentoo")&&(this.os.name="Gentoo"),c.match("Kubuntu")&&(this.os.name="Kubuntu"),c.match("Mandriva Linux")&&(this.os.name="Mandriva",(match=/Mandriva Linux\/[0-9\.\-]+mdv([0-9]+)/.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Mageia")&&(this.os.name="Mageia",(match=/Mageia\/[0-9\.\-]+mga([0-9]+)/.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Red Hat")&&(this.os.name="Red Hat",(match=/Red Hat[^\/]*\/[0-9\.\-]+el([0-9_]+)/.exec(c))&&(this.os.version=new v({value:match[1].replace(/_/g,".")}))),c.match("Slackware")&&(this.os.name="Slackware"),c.match("SUSE")&&(this.os.name="SUSE"),c.match("Turbolinux")&&(this.os.name="Turbolinux"),c.match("Ubuntu")&&(this.os.name="Ubuntu",(match=/Ubuntu\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})))),c.match("iPhone( Simulator)?;")||c.match("iPad;")||c.match("iPod;")||c.match(/iPhone\s*\d*s?[cp]?;/i)?(this.os.name="iOS",this.os.version=new v({value:"1.0"}),(match=/OS (.*) like Mac OS X/.exec(c))&&(this.os.version=new v({value:match[1].replace(/_/g,".")})),c.match("iPhone Simulator;")?this.device.type="emulator":c.match("iPod;")?(this.device.type="media",this.device.manufacturer="Apple",this.device.model="iPod Touch"):c.match("iPhone;")||c.match(/iPhone\s*\d*s?[cp]?;/i)?(this.device.type="mobile",this.device.manufacturer="Apple",this.device.model="iPhone"):(this.device.type="tablet",this.device.manufacturer="Apple",this.device.model="iPad"),this.device.identified=!0):c.match("Mac OS X")&&(this.os.name="Mac OS X",(match=/Mac OS X (10[0-9\._]*)/.exec(c))&&(this.os.version=new v({value:match[1].replace(/_/g,".")}))),c.match("Windows")){if(this.os.name="Windows",match=/Windows NT ([0-9]\.[0-9])/.exec(c))switch(this.os.version=a(match[1]),match[1]){case"6.2":this.os.version=new v({value:match[1],alias:"8"});
break;case"6.1":this.os.version=new v({value:match[1],alias:"7"});break;case"6.0":this.os.version=new v({value:match[1],alias:"Vista"});break;case"5.2":this.os.version=new v({value:match[1],alias:"Server 2003"});
break;case"5.1":this.os.version=new v({value:match[1],alias:"XP"});break;case"5.0":this.os.version=new v({value:match[1],alias:"2000"});break;default:this.os.version=new v({value:match[1],alias:"NT "+this.os.version})
}if((c.match("Windows 95")||c.match("Win95")||c.match("Win 9x 4.00"))&&(this.os.version=new v({value:"4.0",alias:"95"})),(c.match("Windows 98")||c.match("Win98")||c.match("Win 9x 4.10"))&&(this.os.version=new v({value:"4.1",alias:"98"})),(c.match("Windows ME")||c.match("WinME")||c.match("Win 9x 4.90"))&&(this.os.version=new v({value:"4.9",alias:"ME"})),(c.match("Windows XP")||c.match("WinXP"))&&(this.os.name=new v({value:"5.1",alias:"XP"})),c.match("WP7")&&(this.os.name="Windows Phone",this.os.version=new v({value:"7.0",details:2}),this.device.type="mobile",this.browser.mode="desktop"),(c.match("Windows CE")||c.match("WinCE")||c.match("WindowsCE"))&&(c.match(" IEMobile")?(this.os.name="Windows Mobile",c.match(" IEMobile 8")&&(this.os.version=new v({value:"6.5",details:2})),c.match(" IEMobile 7")&&(this.os.version=new v({value:"6.1",details:2})),c.match(" IEMobile 6")&&(this.os.version=new v({value:"6.0",details:2}))):(this.os.name="Windows CE",(match=/WindowsCEOS\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1],details:2})),(match=/Windows CE ([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1],details:2}))),this.device.type="mobile"),c.match("Windows Mobile")&&(this.os.name="Windows Mobile",this.device.type="mobile"),(match=/WindowsMobile\/([0-9.]*)/.exec(c))&&(this.os.name="Windows Mobile",this.os.version=new v({value:match[1],details:2}),this.device.type="mobile"),c.match("Windows Phone [0-9]")&&(this.os.name="Windows Mobile",this.os.version=new v({value:c.match(/Windows Phone ([0-9.]*)/)[1],details:2}),this.device.type="mobile"),c.match("Windows Phone OS")){this.os.name="Windows Phone",this.os.version=new v({value:c.match(/Windows Phone OS ([0-9.]*)/)[1],details:2}),this.os.version<7&&(this.os.name="Windows Mobile"),(match=/IEMobile\/[^;]+; ([^;]+); ([^;]+)[;|\)]/.exec(c))&&(this.device.manufacturer=match[1],this.device.model=match[2]),this.device.type="mobile";
var m=this.device.manufacturer,T=e(this.device.model);"undefined"!=typeof b[m]&&"undefined"!=typeof b[m][T]&&(this.device.manufacturer=b[m][T][0],this.device.model=b[m][T][1],this.device.identified=!0),"Microsoft"===m&&"XDeviceEmulator"===T&&(this.device.manufacturer=null,this.device.model=null,this.device.type="emulator",this.device.identified=!0)
}}if(c.match("Android")){if(this.os.name="Android",this.os.version=null,(match=/Android(?: )?(?:AllPhone_|CyanogenMod_)?(?:\/)?v?([0-9.]+)/.exec(c.replace("-update",".")))&&(this.os.version=new v({value:match[1],details:3})),c.match("Android Eclair")&&(this.os.version=new v({value:"2.0",details:3})),this.device.type="mobile",this.os.version>=3&&(this.device.type="tablet"),this.os.version>=4&&c.match("Mobile")&&(this.device.type="mobile"),(match=/Eclair; (?:[a-zA-Z][a-zA-Z](?:[-_][a-zA-Z][a-zA-Z])?) Build\/([^\/]*)\//.exec(c))?this.device.model=match[1]:(match=/; ([^;]*[^;\s])\s+Build/.exec(c))?this.device.model=match[1]:(match=/[a-zA-Z][a-zA-Z](?:[-_][a-zA-Z][a-zA-Z])?; ([^;]*[^;\s]);\s+Build/.exec(c))?this.device.model=match[1]:(match=/\(([^;]+);U;Android\/[^;]+;[0-9]+\*[0-9]+;CTC\/2.0\)/.exec(c))?this.device.model=match[1]:(match=/;\s?([^;]+);\s?[0-9]+\*[0-9]+;\s?CTC\/2.0/.exec(c))?this.device.model=match[1]:(match=/zh-cn;\s*(.*?)(\/|build)/i.exec(c))?this.device.model=match[1]:(match=/Android [^;]+; (?:[a-zA-Z][a-zA-Z](?:[-_][a-zA-Z][a-zA-Z])?; )?([^)]+)\)/.exec(c))?c.match(/[a-zA-Z][a-zA-Z](?:[-_][a-zA-Z][a-zA-Z])?/)||(this.device.model=match[1]):(match=/^(.+?)\/\S+/i.exec(c))&&(this.device.model=match[1]),this.device.model&&"Android"===this.device.model.substring(0,7)&&(this.device.model=null),this.device.model){var T=e(this.device.model);
"undefined"!=typeof H[T]&&(this.device.manufacturer=H[T][0],this.device.model=H[T][1],"undefined"!=typeof H[T][2]&&(this.device.type=H[T][2]),this.device.identified=!0),("Emulator"===T||"x86 Emulator"===T||"x86 VirtualBox"===T||"vm"===T)&&(this.device.manufacturer=null,this.device.model=null,this.device.type="emulator",this.device.identified=!0)
}c.match("HP eStation")&&(this.device.manufacturer="HP",this.device.model="eStation",this.device.type="tablet",this.device.identified=!0),c.match("Pre/1.0")&&(this.device.manufacturer="Palm",this.device.model="Pre",this.device.identified=!0),c.match("Pre/1.1")&&(this.device.manufacturer="Palm",this.device.model="Pre Plus",this.device.identified=!0),c.match("Pre/1.2")&&(this.device.manufacturer="Palm",this.device.model="Pre 2",this.device.identified=!0),c.match("Pre/3.0")&&(this.device.manufacturer="HP",this.device.model="Pre 3",this.device.identified=!0),c.match("Pixi/1.0")&&(this.device.manufacturer="Palm",this.device.model="Pixi",this.device.identified=!0),c.match("Pixi/1.1")&&(this.device.manufacturer="Palm",this.device.model="Pixi Plus",this.device.identified=!0),c.match("P160UN?A?/1.0")&&(this.device.manufacturer="HP",this.device.model="Veer",this.device.identified=!0)
}if(c.match("GoogleTV")&&(this.os.name="Google TV",c.match("Chrome/5.")&&(this.os.version=new v({value:"1"})),c.match("Chrome/11.")&&(this.os.version=new v({value:"2"})),this.device.type="television"),c.match("WoPhone")&&(this.os.name="WoPhone",(match=/WoPhone\/([0-9\.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.type="mobile"),c.match("BlackBerry")&&(this.os.name="BlackBerry OS",c.match("Opera")?this.device.model="BlackBerry":((match=/BlackBerry([0-9]*)\/([0-9.]*)/.exec(c))&&(this.device.model=match[1],this.os.version=new v({value:match[2],details:2})),(match=/; BlackBerry ([0-9]*);/.exec(c))&&(this.device.model=match[1]),(match=/Version\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1],details:2})),this.os.version>=10&&(this.os.name="BlackBerry"),this.device.model="undefined"!=typeof this.device.model?"undefined"!=typeof C[this.device.model]?"BlackBerry "+C[this.device.model]+" "+this.device.model:"BlackBerry "+this.device.model:"BlackBerry"),this.device.manufacturer="RIM",this.device.type="mobile",this.device.identified=!0),c.match("RIM Tablet OS")?(this.os.name="BlackBerry Tablet OS",this.os.version=new v({value:c.match(/RIM Tablet OS ([0-9.]*)/)[1],details:2}),this.device.manufacturer="RIM",this.device.model="BlackBerry PlayBook",this.device.type="tablet",this.device.identified=!0):c.match("PlayBook")&&(match=/Version\/(10[0-9.]*)/.exec(c))&&(this.os.name="BlackBerry",this.os.version=new v({value:match[1],details:2}),this.device.manufacturer="RIM",this.device.model="BlackBerry PlayBook",this.device.type="tablet",this.device.identified=!0),c.match("(?:web|hpw)OS")&&(this.os.name="webOS",this.os.version=new v({value:c.match(/(?:web|hpw)OS\/([0-9.]*)/)[1]}),this.device.type=c.match("tablet")?"tablet":"mobile",this.device.manufacturer=c.match("hpwOS")?"HP":"Palm",c.match("Pre/1.0")&&(this.device.model="Pre"),c.match("Pre/1.1")&&(this.device.model="Pre Plus"),c.match("Pre/1.2")&&(this.device.model="Pre2"),c.match("Pre/3.0")&&(this.device.model="Pre3"),c.match("Pixi/1.0")&&(this.device.model="Pixi"),c.match("Pixi/1.1")&&(this.device.model="Pixi Plus"),c.match("P160UN?A?/1.0")&&(this.device.model="Veer"),c.match("TouchPad/1.0")&&(this.device.model="TouchPad"),(c.match("Emulator/")||c.match("Desktop/"))&&(this.device.type="emulator",this.device.manufacturer=null,this.device.model=null),this.device.identified=!0),(c.match("Symbian")||c.match("Series[ ]?60")||c.match("S60"))&&(this.os.name="Series60",c.match("SymbianOS/9.1")&&!c.match("Series60")&&(this.os.version=new v({value:"3.0"})),(match=/Series60\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),(match=/Nokia([^\/;]+)[\/|;]/.exec(c))&&"Browser"!==match[1]&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.identified=!0),(match=/Vertu([^\/;]+)[\/|;]/.exec(c))&&(this.device.manufacturer="Vertu",this.device.model=match[1],this.device.identified=!0),(match=/Symbian; U; ([^;]+); [a-z][a-z]\-[a-z][a-z]/i.exec(c))&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.identified=!0),(match=/Samsung\/([^;]*);/.exec(c))&&(this.device.manufacturer=i,this.device.model=match[1],this.device.identified=!0),this.device.type="mobile"),c.match("Series40")&&(this.os.name="Series40",(match=/Nokia([^\/]+)\//.exec(c))&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.identified=!0),this.device.type="mobile"),c.match("MeeGo")&&(this.os.name="MeeGo",this.device.type="mobile",(match=/Nokia([^\)]+)\)/.exec(c))&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.identified=!0)),c.match("Maemo")&&(this.os.name="Maemo",this.device.type="mobile",(match=/(N[0-9]+)/.exec(c))&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.identified=!0)),c.match("Tizen")&&(this.os.name="Tizen",(match=/Tizen[\/ ]([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.type="mobile",(match=/\(([^;]+); ([^\/]+)\//.exec(c))&&"Linux"!==match[1]&&(this.device.manufacturer=match[1],this.device.model=match[2],"undefined"!=typeof d[this.device.manufacturer]&&"undefined"!=typeof d[this.device.manufacturer][this.device.model]))){var m=this.device.manufacturer,T=e(this.device.model);
this.device.manufacturer=d[m][T][0],this.device.model=d[m][T][1],this.device.identified=!0}if(c.match("[b|B]ada")&&(this.os.name="Bada",(match=/[b|B]ada\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.type="mobile",(match=/\(([^;]+); ([^\/]+)\//.exec(c))&&(this.device.manufacturer=match[1],this.device.model=e(match[2])),"undefined"!=typeof S[this.device.manufacturer]&&"undefined"!=typeof S[this.device.manufacturer][this.device.model])){var m=this.device.manufacturer,T=e(this.device.model);
this.device.manufacturer=S[m][T][0],this.device.model=S[m][T][1],this.device.identified=!0}if((c.match(/BREW/i)||c.match("BMP; U"))&&(this.os.name="Brew",this.device.type="mobile",(match=/BREW; U; ([0-9.]*)/i.exec(c))?this.os.version=new v({value:match[1]}):(match=/;BREW\/([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]})),(match=/\(([^;]+);U;REX\/[^;]+;BREW\/[^;]+;(?:.*;)?[0-9]+\*[0-9]+;CTC\/2.0\)/.exec(c))&&(this.device.model=match[1]),this.device.model)){var T=e(this.device.model);
"undefined"!=typeof u[T]&&(this.device.manufacturer=u[T][0],this.device.model=u[T][1],this.device.identified=!0)}if(c.match(/\(MTK;/)&&(this.os.name="MTK",this.device.type="mobile"),c.match("CrOS")&&(this.os.name="Chrome OS",this.device.type="desktop"),c.match("Joli OS")&&(this.os.name="Joli OS",this.device.type="desktop",(match=/Joli OS\/([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Haiku")&&(this.os.name="Haiku",this.device.type="desktop"),c.match("QNX")&&(this.os.name="QNX",this.device.type="mobile"),c.match("OS/2; Warp")&&(this.os.name="OS/2 Warp",this.device.type="desktop",(match=/OS\/2; Warp ([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Grid OS")&&(this.os.name="Grid OS",this.device.type="tablet",(match=/Grid OS ([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match(/AmigaOS/i)&&(this.os.name="AmigaOS",this.device.type="desktop",(match=/AmigaOS ([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match(/MorphOS/i)&&(this.os.name="MorphOS",this.device.type="desktop",(match=/MorphOS ([0-9.]*)/i.exec(c))&&(this.os.version=new v({value:match[1]}))),c.match("Kindle")&&!c.match("Fire")&&(this.os.name="",this.device.manufacturer="Amazon",this.device.model="Kindle",this.device.type="ereader",c.match("Kindle/2.0")&&(this.device.model="Kindle 2"),c.match("Kindle/3.0")&&(this.device.model="Kindle 3 or later"),this.device.identified=!0),c.match("nook browser")&&(this.os.name="Android",this.device.manufacturer="Barnes & Noble",this.device.model="NOOK",this.device.type="ereader",this.device.identified=!0),c.match("bookeen/cybook")&&(this.os.name="",this.device.manufacturer="Bookeen",this.device.model="Cybook",this.device.type="ereader",c.match("Orizon")&&(this.device.model="Cybook Orizon"),this.device.identified=!0),c.match("EBRD1101")&&(this.os.name="",this.device.manufacturer="Sony",this.device.model="Reader",this.device.type="ereader",this.device.identified=!0),c.match("Iriver ;")&&(this.os.name="",this.device.manufacturer="iRiver",this.device.model="Story",this.device.type="ereader",c.match("EB07")&&(this.device.model="Story HD EB07"),this.device.identified=!0),c.match("Nintendo Wii")&&(this.os.name="",this.device.manufacturer="Nintendo",this.device.model="Wii",this.device.type="gaming",this.device.identified=!0),c.match("Nintendo DSi")&&(this.os.name="",this.device.manufacturer="Nintendo",this.device.model="DSi",this.device.type="gaming",this.device.identified=!0),c.match("Nintendo 3DS")&&(this.os.name="",this.device.manufacturer="Nintendo",this.device.model="3DS",this.device.type="gaming",(match=/Version\/([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.identified=!0),c.match("PlayStation Portable")&&(this.os.name="",this.device.manufacturer="Sony",this.device.model="Playstation Portable",this.device.type="gaming",this.device.identified=!0),c.match("PlayStation Vita")&&(this.os.name="",(match=/PlayStation Vita ([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.manufacturer="Sony",this.device.model="PlayStation Vita",this.device.type="gaming",this.device.identified=!0),c.match(/PlayStation 3/i)&&(this.os.name="",(match=/PLAYSTATION 3;? ([0-9.]*)/.exec(c))&&(this.os.version=new v({value:match[1]})),this.device.manufacturer="Sony",this.device.model="Playstation 3",this.device.type="gaming",this.device.identified=!0),c.match("Viera")&&(this.os.name="",this.device.manufacturer="Panasonic",this.device.model="Smart Viera",this.device.type="television",this.device.identified=!0),(c.match("AQUOSBrowser")||c.match("AQUOS-AS"))&&(this.os.name="",this.device.manufacturer=o,this.device.model="Aquos TV",this.device.type="television",this.device.identified=!0),c.match("SMART-TV")&&(this.os.name="",this.device.manufacturer=i,this.device.model="Smart TV",this.device.type="television",this.device.identified=!0,(match=/Maple([0-9]*)/.exec(c))&&(this.device.model+=" "+match[1])),c.match("SonyDTV|SonyBDP|SonyCEBrowser")&&(this.os.name="",this.device.manufacturer="Sony",this.device.model="Internet TV",this.device.type="television",this.device.identified=!0),c.match("NETTV/")&&(this.os.name="",this.device.manufacturer="Philips",this.device.model="Net TV",this.device.type="television",this.device.identified=!0),(match=/LG NetCast\.(?:TV|Media)-([0-9]*)/.exec(c))&&(this.os.name="",this.device.manufacturer=n,this.device.model="NetCast TV "+match[1],this.device.type="television",this.device.identified=!0),(match=/LGSmartTV/.exec(c))&&(this.os.name="",this.device.manufacturer=n,this.device.model="Smart TV",this.device.type="television",this.device.identified=!0),(c.match("Toshiba_?TP/")||c.match("TSBNetTV/"))&&(this.os.name="",this.device.manufacturer="Toshiba",this.device.model="Smart TV",this.device.type="television",this.device.identified=!0),(match=/mbxtWebKit\/([0-9.]*)/.exec(c))&&(this.os.name="",this.browser.name="MachBlue XT",this.browser.version=new v({value:match[1],details:2}),this.device.type="television"),(match=/\(ADB; ([^\)]+)\)/.exec(c))&&(this.os.name="",this.device.manufacturer="ADB",this.device.model=("Unknown"!==match[1]?match[1].replace("ADB","")+" ":"")+"IPTV receiver",this.device.type="television",this.device.identified=!0),c.match(/Mstar;OWB/)&&(this.os.name="",this.device.manufacturer="MStar",this.device.model="PVR",this.device.type="television",this.device.identified=!0,this.browser.name="Origyn Web Browser"),(match=/\TechniSat ([^;]+);/.exec(c))&&(this.os.name="",this.device.manufacturer="TechniSat",this.device.model=match[1],this.device.type="television",this.device.identified=!0),(match=/\Technicolor_([^;]+);/.exec(c))&&(this.os.name="",this.device.manufacturer="Technicolor",this.device.model=match[1],this.device.type="television",this.device.identified=!0),(match=/Winbox Evo2/.exec(c))&&(this.os.name="",this.device.manufacturer="Winbox",this.device.model="Evo2",this.device.type="television",this.device.identified=!0),match=/^Roku\/DVP-([0-9]+)/.exec(c)){switch(this.device.manufacturer="Roku",this.device.type="television",match[1]){case"2000":this.device.model="HD";
break;case"2050":this.device.model="XD";break;case"2100":this.device.model="XDS";break;case"2400":this.device.model="LT";break;case"3000":this.device.model="2 HD";break;case"3050":this.device.model="2 XD";
break;case"3100":this.device.model="2 XS"}this.device.identified=!0}if(match=/HbbTV\/1.1.1 \([^;]*;\s*([^;]*)\s*;\s*([^;]*)\s*;/.exec(c)){var p=match[1].trim(),M=match[2].trim();if(!this.device.manufacturer&&""!==p&&"vendorName"!==p){switch(p){case"LGE":this.device.manufacturer="LG";
break;case"TOSHIBA":this.device.manufacturer="Toshiba";break;case"smart":this.device.manufacturer="Smart";break;case"tv2n":this.device.manufacturer="TV2N";break;default:this.device.manufacturer=p}if(!this.device.model&&""!==M&&"modelName"!==M){switch(M){case"GLOBAL_PLAT3":this.device.model="NetCast TV";
break;case"SmartTV2012":this.device.model="Smart TV 2012";break;case"videoweb":this.device.model="Videoweb";break;default:this.device.model=M}"Humax"===p&&(this.device.model=this.device.model.toUpperCase()),this.device.identified=!0,this.os.name=""
}}this.device.type="television"}if(c.match("InettvBrowser")&&(this.device.type="television"),c.match("MIDP")&&(this.device.type="mobile"),!this.device.model&&!this.device.manufacturer){var A=[];c.match(/^(Mozilla|Opera)/)||(match=/^(?:MQQBrowser\/[0-9\.]+\/)?([^\s]+)/.exec(c))&&(match[1]=match[1].replace(/_TD$/,""),match[1]=match[1].replace(/_CMCC$/,""),match[1]=match[1].replace(/[_ ]Mozilla$/,""),match[1]=match[1].replace(/ Linux$/,""),match[1]=match[1].replace(/ Opera$/,""),match[1]=match[1].replace(/\/[0-9].*$/,""),A.push(match[1])),(match=/[0-9]+x[0-9]+; ([^;]+)/.exec(c))&&A.push(match[1]),(match=/[0-9]+X[0-9]+ ([^;\/\(\)]+)/.exec(c))&&A.push(match[1]),(match=/Windows NT 5.1; ([^;]+); Windows Phone/.exec(c))&&A.push(match[1]),(match=/\) PPC; (?:[0-9]+x[0-9]+; )?([^;\/\(\)]+)/.exec(c))&&A.push(match[1]),(match=/\(([^;]+); U; Windows Mobile/.exec(c))&&A.push(match[1]),(match=/Vodafone\/1.0\/([^\/]+)/.exec(c))&&A.push(match[1]),(match=/\ ([^\s]+)$/.exec(c))&&A.push(match[1]);
for(var y=0;y<A.length;y++){if(!this.device.model&&!this.device.manufacturer){var T=e(A[y]),E=!1;"Android"===this.os.name&&"undefined"!=typeof H[T]&&(this.device.manufacturer=H[T][0],this.device.model=H[T][1],"undefined"!=typeof H[T][2]&&(this.device.type=H[T][2]),this.device.identified=!0,E=!0),this.os.name&&"Windows"!==this.os.name&&"Windows Mobile"!==this.os.name&&"Windows CE"!==this.os.name||"undefined"!=typeof G[T]&&(this.device.manufacturer=G[T][0],this.device.model=G[T][1],this.device.type="mobile",this.device.identified=!0,"Windows Mobile"!==this.os.name&&(this.os.name="Windows Mobile",this.os.version=null),E=!0)
}if(!E&&((match=/^GIONEE-([^\s]+)/.exec(A[y]))&&(this.device.manufacturer="Gionee",this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/^HTC_?([^\/_]+)(?:\/|_|$)/.exec(A[y]))&&(this.device.manufacturer=l,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/^HUAWEI-([^\/]*)/.exec(A[y]))&&(this.device.manufacturer=r,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/(?:^|\()LGE?(?:\/|-|_|\s)([^\s]*)/.exec(A[y]))&&(this.device.manufacturer=n,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/^MOT-([^\/_]+)(?:\/|_|$)/.exec(A[y]))&&(this.device.manufacturer=s,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/^Motorola_([^\/_]+)(?:\/|_|$)/.exec(A[y]))&&(this.device.manufacturer=s,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),(match=/^Nokia([^\/]+)(?:\/|$)/.exec(A[y]))&&(this.device.manufacturer="Nokia",this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0,this.os.name||(this.os.name="Series40")),(match=/^SonyEricsson([^\/_]+)(?:\/|_|$)/.exec(A[y]))&&(this.device.manufacturer=t,this.device.model=e(match[1]),this.device.type="mobile",this.device.identified=!0),match=/^SAMSUNG-([^\/_]+)(?:\/|_|$)/.exec(A[y])))if(this.device.manufacturer=i,this.device.model=e(match[1]),this.device.type="mobile","Bada"===this.os.name){var m="SAMSUNG",T=e(this.device.model);
"undefined"!=typeof S[m]&&"undefined"!=typeof S[m][T]&&(this.device.manufacturer=S[m][T][0],this.device.model=S[m][T][1],this.device.identified=!0)}else if(match=/Jasmine\/([0-9.]*)/.exec(c)){var I=match[1],m="SAMSUNG",T=e(this.device.model);
"undefined"!=typeof h[m]&&"undefined"!=typeof h[m][T]&&(this.device.manufacturer=h[m][T][0],this.device.model=h[m][T][1],this.device.identified=!0,this.os.name="Touchwiz",this.os.version=new v({value:"2.0"}))
}else if(match=/Dolfin\/([0-9.]*)/.exec(c)){var I=match[1],m="SAMSUNG",T=e(this.device.model);if("undefined"!=typeof S[m]&&"undefined"!=typeof S[m][T])switch(this.device.manufacturer=S[m][T][0],this.device.model=S[m][T][1],this.device.identified=!0,this.os.name="Bada",I){case"2.0":this.os.version=new v({value:"1.0"});
break;case"2.2":this.os.version=new v({value:"1.2"});break;case"3.0":this.os.version=new v({value:"2.0"})}if("undefined"!=typeof h[m]&&"undefined"!=typeof h[m][T])switch(this.device.manufacturer=h[m][T][0],this.device.model=h[m][T][1],this.device.identified=!0,this.os.name="Touchwiz",I){case"1.0":this.os.version=new v({value:"1.0"});
break;case"1.5":this.os.version=new v({value:"2.0"});break;case"2.0":this.os.version=new v({value:"3.0"})}}}}if((match=/\((?:LG[-|\/])(.*) (?:Browser\/)?AppleWebkit/.exec(c))&&(this.device.manufacturer=n,this.device.model=match[1],this.device.type="mobile",this.device.identified=!0),(match=/^Mozilla\/5.0 \((?:Nokia|NOKIA)(?:\s?)([^\)]+)\)UC AppleWebkit\(like Gecko\) Safari\/530$/.exec(c))&&(this.device.manufacturer="Nokia",this.device.model=match[1],this.device.type="mobile",this.device.identified=!0,this.os.name="Series60"),c.match("Safari")&&("iOS"===this.os.name&&(this.browser.stock=!0,this.browser.hidden=!0,this.browser.name="Safari",this.browser.version=null),("Mac OS X"===this.os.name||"Windows"===this.os.name)&&(this.browser.name="Safari",this.browser.stock="Mac OS X"===this.os.name,(match=/Version\/([0-9\.]+)/.exec(c))&&(this.browser.version=new v({value:match[1]})),c.match(/AppleWebKit\/[0-9\.]+\+/)&&(this.browser.name="WebKit Nightly Build",this.browser.version=null))),c.match("MSIE")&&(this.browser.name="Internet Explorer",(c.match("IEMobile")||c.match("Windows CE")||c.match("Windows Phone")||c.match("WP7"))&&(this.browser.name="Mobile Internet Explorer"),(match=/MSIE ([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match(/Opera/i)&&(this.browser.stock=!1,this.browser.name="Opera",(match=/Opera[\/| ]([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),(match=/Version\/([0-9.]*)/.exec(c))&&(this.browser.version=parseFloat(match[1])>=10?new v({value:match[1]}):null),this.browser.version&&c.match("Edition Labs")&&(this.browser.version.type="alpha",this.browser.channel="Labs"),this.browser.version&&c.match("Edition Next")&&(this.browser.version.type="alpha",this.browser.channel="Next"),c.match("Opera Tablet")&&(this.browser.name="Opera Mobile",this.device.type="tablet"),c.match("Opera Mobi")&&(this.browser.name="Opera Mobile",this.device.type="mobile"),(match=/Opera Mini;/.exec(c))&&(this.browser.name="Opera Mini",this.browser.version=null,this.browser.mode="proxy",this.device.type="mobile"),(match=/Opera Mini\/(?:att\/)?([0-9.]*)/.exec(c))&&(this.browser.name="Opera Mini",this.browser.version=new v({value:match[1],details:-1}),this.browser.mode="proxy",this.device.type="mobile"),"Opera"===this.browser.name&&"mobile"===this.device.type&&(this.browser.name="Opera Mobile",c.match(/BER/)&&(this.browser.name="Opera Mini",this.browser.version=null)),c.match("InettvBrowser")&&(this.device.type="television"),(c.match("Opera TV")||c.match("Opera-TV"))&&(this.browser.name="Opera",this.device.type="television"),c.match("Linux zbov")&&(this.browser.name="Opera Mobile",this.browser.mode="desktop",this.device.type="mobile",this.os.name=null,this.os.version=null),c.match("Linux zvav")&&(this.browser.name="Opera Mini",this.browser.version=null,this.browser.mode="desktop",this.device.type="mobile",this.os.name=null,this.os.version=null)),c.match("Firefox")&&(this.browser.stock=!1,this.browser.name="Firefox",(match=/Firefox\/([0-9ab.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),"alpha"===this.browser.version.type&&(this.browser.channel="Aurora"),"beta"===this.browser.version.type&&(this.browser.channel="Beta"),c.match("Fennec")&&(this.device.type="mobile"),c.match("Mobile; rv")&&(this.device.type="mobile"),c.match("Tablet; rv")&&(this.device.type="tablet"),("mobile"===this.device.type||"tablet"===this.device.type)&&(this.browser.name="Firefox Mobile")),c.match("Namoroka")&&(this.browser.stock=!1,this.browser.name="Firefox",(match=/Namoroka\/([0-9ab.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),this.browser.channel="Namoroka"),c.match("Shiretoko")&&(this.browser.stock=!1,this.browser.name="Firefox",(match=/Shiretoko\/([0-9ab.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),this.browser.channel="Shiretoko"),c.match("Minefield")&&(this.browser.stock=!1,this.browser.name="Firefox",(match=/Minefield\/([0-9ab.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),this.browser.channel="Minefield"),c.match("Firebird")&&(this.browser.stock=!1,this.browser.name="Firebird",(match=/Firebird\/([0-9ab.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("SeaMonkey")&&(this.browser.stock=!1,this.browser.name="SeaMonkey",(match=/SeaMonkey\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("Netscape")&&(this.browser.stock=!1,this.browser.name="Netscape",(match=/Netscape[0-9]?\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("[k|K]onqueror/")&&(this.browser.name="Konqueror",(match=/[k|K]onqueror\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),match=/(?:Chrome|CrMo|CriOS)\/([0-9.]*)/.exec(c))if(this.browser.stock=!1,this.browser.name="Chrome",this.browser.version=new v({value:match[1]}),"Android"===this.os.name)switch(match[1].split(".",3).join(".")){case"16.0.912":this.browser.channel="Beta";
break;case"18.0.1025":this.browser.version.details=1;break;default:this.browser.channel="Nightly"}else switch(match[1].split(".",3).join(".")){case"0.2.149":case"0.3.154":case"0.4.154":case"1.0.154":case"2.0.172":case"3.0.195":case"4.0.249":case"4.1.249":case"5.0.375":case"6.0.472":case"7.0.517":case"8.0.552":case"9.0.597":case"10.0.648":case"11.0.696":case"12.0.742":case"13.0.782":case"14.0.835":case"15.0.874":case"16.0.912":case"17.0.963":case"18.0.1025":case"19.0.1084":case"20.0.1132":case"21.0.1180":this.browser.version.details=0===this.browser.version.minor?1:2;
break;default:this.browser.channel="Nightly"}if(c.match("chromeframe")&&(this.browser.stock=!1,this.browser.name="Chrome Frame",(match=/chromeframe\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("Chromium")&&(this.browser.stock=!1,this.browser.channel="",this.browser.name="Chromium",(match=/Chromium\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("BrowserNG")&&(this.browser.name="Nokia Browser",(match=/BrowserNG\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1],details:3,builds:!1}))),c.match("NokiaBrowser")&&(this.browser.name="Nokia Browser",(match=/NokiaBrowser\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1],details:3}))),c.match("Maemo[ |_]Browser")&&(this.browser.name="MicroB",(match=/Maemo[ |_]Browser[ |_]([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1],details:3}))),c.match("NetFront")&&(this.browser.name="NetFront",this.device.type="mobile",(match=/NetFront\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),c.match("InettvBrowser")&&(this.device.type="television")),c.match("Silk")&&c.match("Silk-Accelerated")&&(this.browser.name="Silk",(match=/Silk\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1],details:2})),this.device.manufacturer="Amazon",this.device.model="Kindle Fire",this.device.type="tablet",this.device.identified=!0,"Android"!==this.os.name&&(this.os.name="Android",this.os.version=null)),c.match("Dolfin")&&(this.browser.name="Dolfin",(match=/Dolfin\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("Iris")&&(this.browser.name="Iris",this.device.type="mobile",this.device.model=null,this.device.manufacturer=null,this.os.name="Windows Mobile",this.os.version=null,(match=/Iris\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]})),(match=/ WM([0-9]) /.exec(c))?this.os.version=new v({value:match[1]+".0"}):this.browser.mode="desktop"),c.match("Jasmine")&&(this.browser.name="Jasmine",(match=/Jasmine\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("Boxee")&&(this.browser.name="Boxee",this.device.type="television",(match=/Boxee\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),c.match("Espial")&&(this.browser.name="Espial",this.os.name="",this.os.version=null,"television"!==this.device.type&&(this.device.type="television",this.device.model=null,this.device.manufacturer=null),(match=/Espial\/([0-9.]*)/.exec(c))&&(this.browser.version=new v({value:match[1]}))),(match=/ANTGalio\/([0-9.]*)/.exec(c))&&(this.browser.name="ANT Galio",this.browser.version=new v({value:match[1],details:3}),this.device.type="television"),(match=/NX\/([0-9.]*)/.exec(c))&&(this.browser.name="NetFront NX",this.browser.version=new v({value:match[1],details:2}),this.device.type=(match=/DTV/i.exec(c))?"television":(match=/mobile/i.exec(c))?"mobile":"desktop",this.os.name=null,this.os.version=null),c.match(/Obigo/i)&&(this.browser.name="Obigo",(match=/Obigo\/([0-9.]*)/i.exec(c))&&(this.browser.version=new v({value:match[1]})),(match=/Obigo\/([A-Z])([0-9.]*)/i.exec(c))&&(this.browser.name="Obigo "+match[1],this.browser.version=new v({value:match[2]})),(match=/Obigo-([A-Z])([0-9.]*)\//i.exec(c))&&(this.browser.name="Obigo "+match[1],this.browser.version=new v({value:match[2]}))),c.match("UCWEB")&&(this.browser.stock=!1,this.browser.name="UC Browser",(match=/UCWEB([0-9]*[.][0-9]*)/.exec(c))&&(this.browser.version=new v({value:match[1],details:3})),"Linux"===this.os.name&&(this.os.name=""),this.device.type="mobile",(match=/^IUC \(U;\s?iOS ([0-9\.]+);/.exec(c))&&(this.os.name="iOS",this.os.version=new v({value:match[1]})),match=/^JUC \(Linux; U; ([0-9\.]+)[^;]*; [^;]+; ([^;]*[^\s])\s*; [0-9]+\*[0-9]+\)/.exec(c))){var T=e(match[2]);
this.os.name="Android",this.os.version=new v({value:match[1]}),"undefined"!=typeof H[T]&&(this.device.manufacturer=H[T][0],this.device.model=H[T][1],"undefined"!=typeof H[T][2]&&(this.device.type=H[T][2]),this.device.identified=!0)
}if(c.match(/\) UC /)&&(this.browser.stock=!1,this.browser.name="UC Browser"),(match=/UCBrowser\/([0-9.]*)/.exec(c))&&(this.browser.stock=!1,this.browser.name="UC Browser",this.browser.version=new v({value:match[1],details:2})),(match=/Ninesky(?:-android-mobile(?:-cn)?)?\/([0-9.]*)/.exec(c))&&(this.browser.name="NineSky",this.browser.version=new v({value:match[1]}),"Android"!==this.os.name&&(this.os.name="Android",this.os.version=null,this.device.manufacturer=null,this.device.model=null)),(match=/Skyfire\/([0-9.]*)/.exec(c))&&(this.browser.name="Skyfire",this.browser.version=new v({value:match[1]}),this.device.type="mobile",this.os.name="Android",this.os.version=null),(match=/DolphinHDCN\/([0-9.]*)/.exec(c))&&(this.browser.name="Dolphin",this.browser.version=new v({value:match[1]}),this.device.type="mobile","Android"!==this.os.name&&(this.os.name="Android",this.os.version=null)),(match=/Dolphin\/INT/.exec(c))&&(this.browser.name="Dolphin",this.device.type="mobile"),match=/(M?QQBrowser)\/([0-9.]*)/.exec(c)){this.browser.name="QQ Browser";
var I=match[2];I.match(/^[0-9][0-9]$/)&&(I=I[0]+"."+I[1]),this.browser.version=new v({value:I,details:2}),this.browser.channel="",this.os.name||"QQBrowser"!==match[1]||(this.os.name="Windows")}if(match=/(iBrowser)\/([0-9.]*)/.exec(c)){this.browser.name="iBrowser";
var I=match[2];I.match(/[0-9][0-9]/)&&(I=I[0]+"."+I[1]),this.browser.version=new v({value:I,details:2}),this.browser.channel=""}(match=/Puffin\/([0-9.]*)/.exec(c))&&(this.browser.name="Puffin",this.browser.version=new v({value:match[1],details:2}),this.device.type="mobile","Linux"===this.os.name&&(this.os.name=null,this.os.version=null)),c.match("360EE")&&(this.browser.stock=!1,this.browser.name="360 Extreme Explorer",this.browser.version=null),(match=/Midori\/([0-9.]*)/.exec(c))&&(this.browser.name="Midori",this.browser.version=new v({value:match[1]}),"Linux"!==this.os.name&&(this.os.name="Linux",this.os.version=null),this.device.manufacturer=null,this.device.model=null,this.device.type="desktop");
for(var g=[{name:"AdobeAIR",regexp:/AdobeAIR\/([0-9.]*)/},{name:"Awesomium",regexp:/Awesomium\/([0-9.]*)/},{name:"Canvace",regexp:/Canvace Standalone\/([0-9.]*)/},{name:"Ekioh",regexp:/Ekioh\/([0-9.]*)/},{name:"JavaFX",regexp:/JavaFX\/([0-9.]*)/},{name:"GFXe",regexp:/GFXe\/([0-9.]*)/},{name:"LuaKit",regexp:/luakit/},{name:"Titanium",regexp:/Titanium\/([0-9.]*)/},{name:"OpenWebKitSharp",regexp:/OpenWebKitSharp/},{name:"Prism",regexp:/Prism\/([0-9.]*)/},{name:"Qt",regexp:/Qt\/([0-9.]*)/},{name:"QtEmbedded",regexp:/QtEmbedded/},{name:"QtEmbedded",regexp:/QtEmbedded.*Qt\/([0-9.]*)/},{name:"RhoSimulator",regexp:/RhoSimulator/},{name:"UWebKit",regexp:/UWebKit\/([0-9.]*)/},{name:"PhantomJS",regexp:/PhantomJS\/([0-9.]*)/},{name:"Google Web Preview",regexp:/Google Web Preview/},{name:"Google Earth",regexp:/Google Earth\/([0-9.]*)/},{name:"EA Origin",regexp:/Origin\/([0-9.]*)/},{name:"SecondLife",regexp:/SecondLife\/([0-9.]*)/},{name:"Valve Steam",regexp:/Valve Steam/},{name:"Songbird",regexp:/Songbird\/([0-9.]*)/},{name:"Thunderbird",regexp:/Thunderbird\/([0-9.]*)/},{name:"Abrowser",regexp:/Abrowser\/([0-9.]*)/},{name:"arora",regexp:/[Aa]rora\/([0-9.]*)/},{name:"Baidu Browser",regexp:/M?BaiduBrowser\/([0-9.]*)/i},{name:"Camino",regexp:/Camino\/([0-9.]*)/},{name:"Canure",regexp:/Canure\/([0-9.]*)/,details:3},{name:"CometBird",regexp:/CometBird\/([0-9.]*)/},{name:"Comodo Dragon",regexp:/Comodo_Dragon\/([0-9.]*)/,details:2},{name:"Conkeror",regexp:/[Cc]onkeror\/([0-9.]*)/},{name:"CoolNovo",regexp:/(?:CoolNovo|CoolNovoChromePlus)\/([0-9.]*)/,details:3},{name:"ChromePlus",regexp:/ChromePlus(?:\/([0-9.]*))?$/,details:3},{name:"Daedalus",regexp:/Daedalus ([0-9.]*)/,details:2},{name:"Demobrowser",regexp:/demobrowser\/([0-9.]*)/},{name:"Dooble",regexp:/Dooble(?:\/([0-9.]*))?/},{name:"DWB",regexp:/dwb(?:-hg)?(?:\/([0-9.]*))?/},{name:"Epiphany",regexp:/Epiphany\/([0-9.]*)/},{name:"FireWeb",regexp:/FireWeb\/([0-9.]*)/},{name:"Flock",regexp:/Flock\/([0-9.]*)/,details:3},{name:"Galeon",regexp:/Galeon\/([0-9.]*)/,details:3},{name:"Helium",regexp:/HeliumMobileBrowser\/([0-9.]*)/},{name:"iCab",regexp:/iCab\/([0-9.]*)/},{name:"Iceape",regexp:/Iceape\/([0-9.]*)/},{name:"IceCat",regexp:/IceCat ([0-9.]*)/},{name:"Iceweasel",regexp:/Iceweasel\/([0-9.]*)/},{name:"InternetSurfboard",regexp:/InternetSurfboard\/([0-9.]*)/},{name:"Iron",regexp:/Iron\/([0-9.]*)/,details:2},{name:"Isis",regexp:/BrowserServer/},{name:"Jumanji",regexp:/jumanji/},{name:"Kazehakase",regexp:/Kazehakase\/([0-9.]*)/},{name:"KChrome",regexp:/KChrome\/([0-9.]*)/,details:3},{name:"K-Meleon",regexp:/K-Meleon\/([0-9.]*)/},{name:"Leechcraft",regexp:/Leechcraft(?:\/([0-9.]*))?/,details:2},{name:"Lightning",regexp:/Lightning\/([0-9.]*)/},{name:"Lunascape",regexp:/Lunascape[\/| ]([0-9.]*)/,details:3},{name:"iLunascape",regexp:/iLunascape\/([0-9.]*)/,details:3},{name:"Maxthon",regexp:/Maxthon[\/ ]([0-9.]*)/,details:3},{name:"MiniBrowser",regexp:/MiniBr?owserM\/([0-9.]*)/},{name:"MiniBrowser",regexp:/MiniBrowserMobile\/([0-9.]*)/},{name:"MixShark",regexp:/MixShark\/([0-9.]*)/},{name:"Motorola WebKit",regexp:/MotorolaWebKit\/([0-9.]*)/,details:3},{name:"NetFront LifeBrowser",regexp:/NetFrontLifeBrowser\/([0-9.]*)/},{name:"Netscape Navigator",regexp:/Navigator\/([0-9.]*)/,details:3},{name:"Odyssey",regexp:/OWB\/([0-9.]*)/},{name:"OmniWeb",regexp:/OmniWeb/},{name:"Orca",regexp:/Orca\/([0-9.]*)/},{name:"Origyn",regexp:/Origyn Web Browser/},{name:"Palemoon",regexp:/Pale[mM]oon\/([0-9.]*)/},{name:"Phantom",regexp:/Phantom\/V([0-9.]*)/},{name:"Polaris",regexp:/Polaris\/v?([0-9.]*)/i,details:2},{name:"QtCreator",regexp:/QtCreator\/([0-9.]*)/},{name:"QtQmlViewer",regexp:/QtQmlViewer/},{name:"QtTestBrowser",regexp:/QtTestBrowser\/([0-9.]*)/},{name:"QtWeb",regexp:/QtWeb Internet Browser\/([0-9.]*)/},{name:"QupZilla",regexp:/QupZilla\/([0-9.]*)/},{name:"Roccat",regexp:/Roccat\/([0-9]\.[0-9.]*)/},{name:"Raven for Mac",regexp:/Raven for Mac\/([0-9.]*)/},{name:"rekonq",regexp:/rekonq/},{name:"RockMelt",regexp:/RockMelt\/([0-9.]*)/,details:2},{name:"Sleipnir",regexp:/Sleipnir\/([0-9.]*)/,details:3},{name:"SMBrowser",regexp:/SMBrowser/},{name:"Sogou Explorer",regexp:/SE 2.X MetaSr/},{name:"Snowshoe",regexp:/Snowshoe\/([0-9.]*)/,details:2},{name:"Sputnik",regexp:/Sputnik\/([0-9.]*)/i,details:3},{name:"Stainless",regexp:/Stainless\/([0-9.]*)/},{name:"SunChrome",regexp:/SunChrome\/([0-9.]*)/},{name:"Surf",regexp:/Surf\/([0-9.]*)/},{name:"TaoBrowser",regexp:/TaoBrowser\/([0-9.]*)/,details:2},{name:"TaomeeBrowser",regexp:/TaomeeBrowser\/([0-9.]*)/,details:2},{name:"TazWeb",regexp:/TazWeb/},{name:"Viera",regexp:/Viera\/([0-9.]*)/},{name:"Villanova",regexp:/Villanova\/([0-9.]*)/,details:3},{name:"Wavelink Velocity",regexp:/Wavelink Velocity Browser\/([0-9.]*)/,details:2},{name:"WebPositive",regexp:/WebPositive/},{name:"WebRender",regexp:/WebRender/},{name:"Wyzo",regexp:/Wyzo\/([0-9.]*)/,details:3},{name:"Zetakey",regexp:/Zetakey Webkit\/([0-9.]*)/},{name:"Zetakey",regexp:/Zetakey\/([0-9.]*)/}],L=0;L<g.length;L++)(match=g[L].regexp.exec(c))&&(this.browser.name=g[L].name,this.browser.channel="",this.browser.stock=!1,this.browser.version=match[1]?new v({value:match[1],details:g[L].details||null}):null);
if((match=/WebKit\/([0-9.]*)/i.exec(c))&&(this.engine.name="Webkit",this.engine.version=new v({value:match[1]})),(match=/Browser\/AppleWebKit([0-9.]*)/i.exec(c))&&(this.engine.name="Webkit",this.engine.version=new v({value:match[1]})),(match=/KHTML\/([0-9.]*)/.exec(c))&&(this.engine.name="KHTML",this.engine.version=new v({value:match[1]})),/Gecko/.exec(c)&&!/like Gecko/i.exec(c)&&(this.engine.name="Gecko",(match=/; rv:([^\)]+)\)/.exec(c))&&(this.engine.version=new v({value:match[1]}))),(match=/Presto\/([0-9.]*)/.exec(c))&&(this.engine.name="Presto",this.engine.version=new v({value:match[1]})),(match=/Trident\/([0-9.]*)/.exec(c))&&(this.engine.name="Trident",this.engine.version=new v({value:match[1]}),"Internet Explorer"===this.browser.name&&(6===a(this.engine.version)&&parseFloat(this.browser.version)<10&&(this.browser.version=new v({value:"10.0"}),this.browser.mode="compat"),5===a(this.engine.version)&&parseFloat(this.browser.version)<9&&(this.browser.version=new v({value:"9.0"}),this.browser.mode="compat"),4===a(this.engine.version)&&parseFloat(this.browser.version)<8&&(this.browser.version=new v({value:"8.0"}),this.browser.mode="compat")),"Windows Phone"===this.os.name&&5===a(this.engine.version)&&parseFloat(this.os.version)<7.5&&(this.os.version=new v({value:"7.5"}))),"Android"===this.os.name&&this.browser.stock&&(this.browser.hidden=!0),"iOS"===this.os.name&&"Opera Mini"===this.browser.name&&(this.os.version=null),"Midori"===this.browser.name&&"Webkit"!==this.engine.name&&(this.engine.name="Webkit",this.engine.version=null),"television"===this.device.type&&"Opera"===this.browser.name){switch(this.browser.name="Opera Devices",!0){case this.engine.version.is("2.10"):this.browser.version=new v({value:3.2});
break;case this.engine.version.is("2.9"):this.browser.version=new v({value:3.1});break;case this.engine.version.is("2.8"):this.browser.version=new v({value:3});break;case this.engine.version.is("2.7"):this.browser.version=new v({value:2.9});
break;case this.engine.version.is("2.6"):this.browser.version=new v({value:2.8});break;case this.engine.version.is("2.4"):this.browser.version=new v({value:10.3});break;case this.engine.version.is("2.3"):this.browser.version=new v({value:10});
break;case this.engine.version.is("2.2"):this.browser.version=new v({value:9.7});break;case this.engine.version.is("2.1"):this.browser.version=new v({value:9.6});break;default:this.browser.version=null
}this.os.name=null,this.os.version=null}if(this.options.detectCamouflage){if(match=/Mac OS X 10_6_3; ([^;]+); [a-z]{2}-(?:[a-z]{2})?\)/.exec(c)){this.browser.name="",this.browser.version=null,this.browser.mode="desktop",this.os.name="Android",this.os.version=null,this.engine.name="Webkit",this.engine.version=null,this.device.model=match[1],this.device.type="mobile";
var T=e(this.device.model);"undefined"!=typeof H[T]&&(this.device.manufacturer=H[T][0],this.device.model=H[T][1],"undefined"!=typeof H[T][2]&&(this.device.type=H[T][2]),this.device.identified=!0),this.features.push("foundDevice")
}if(match=/Linux Ventana; [a-z]{2}-[a-z]{2}; (.+) Build/.exec(c)){this.browser.name="",this.browser.version=null,this.browser.mode="desktop",this.os.name="Android",this.os.version=null,this.engine.name="Webkit",this.engine.version=null,this.device.model=match[1],this.device.type="mobile";
var T=e(this.device.model);"undefined"!=typeof H[T]&&(this.device.manufacturer=H[T][0],this.device.model=H[T][1],"undefined"!=typeof H[T][2]&&(this.device.type=H[T][2]),this.device.identified=!0),this.features.push("foundDevice")
}"Safari"===this.browser.name&&("iOS"!==this.os.name&&/AppleWebKit\/([0-9]+.[0-9]+)/i.exec(c)[1]!==/Safari\/([0-9]+.[0-9]+)/i.exec(c)[1]&&(this.features.push("safariMismatch"),this.camouflage=!0),"iOS"!==this.os.name||c.match(/^Mozilla/)||(this.features.push("noMozillaPrefix"),this.camouflage=!0),/Version\/[0-9\.]+/.exec(c)||(this.features.push("noVersion"),this.camouflage=!0)),"Chrome"===this.browser.name&&(/(?:Chrome|CrMo|CriOS)\/([0-9]{1,2}\.[0-9]\.[0-9]{3,4}\.[0-9]+)/.exec(c)||(this.features.push("wrongVersion"),this.camouflage=!0)),this.options.useFeatures&&(window.ActiveXObject&&(this.features.push("trident"),"undefined"!=typeof this.engine.name&&"Trident"!==this.engine.name&&(this.camouflage="undefined"==typeof this.browser.name||"Maxthon"!==this.browser.name)),window.opera&&(this.features.push("presto"),"undefined"!=typeof this.engine.name&&"Presto"!==this.engine.name&&(this.camouflage=!0),"Internet Explorer"===this.browser.name&&(this.camouflage=!0)),("getBoxObjectFor"in document||"mozInnerScreenX"in window)&&(this.features.push("gecko"),"undefined"!=typeof this.engine.name&&"Gecko"!==this.engine.name&&(this.camouflage=!0),"Internet Explorer"===this.browser.name&&(this.camouflage=!0)),("WebKitCSSMatrix"in window||"WebKitPoint"in window||"webkitStorageInfo"in window||"webkitURL"in window)&&(this.features.push("webkit"),"undefined"!=typeof this.engine.name&&"Webkit"!==this.engine.name&&(this.camouflage=!0),"Internet Explorer"===this.browser.name&&(this.camouflage=!0)),"Webkit"===this.engine.name&&-1==={}.toString.toString().indexOf("\n")&&(this.features.push("v8"),null!==this.browser&&"Safari"===this.browser.name&&(this.camouflage=!0)),"iPad"===this.device.model&&0!==screen.width&&0!==screen.height&&768!==screen.width&&1024!==screen.height&&1024!==screen.width&&768!==screen.height&&(this.features.push("sizeMismatch"),this.camouflage=!0),("iPhone"===this.device.model||"iPod"===this.device.model)&&0!==screen.width&&0!==screen.height&&320!==screen.width&&480!==screen.height&&480!==screen.width&&320!==screen.height&&(this.features.push("sizeMismatch"),this.camouflage=!0),"iOS"===this.os.name&&this.os.version&&(this.os.version.isOlder("4.0")&&"sandbox"in document.createElement("iframe")&&(this.features.push("foundSandbox"),this.camouflage=!0),this.os.version.isOlder("4.2")&&"WebSocket"in window&&(this.features.push("foundSockets"),this.camouflage=!0),this.os.version.isOlder("5.0")&&window.Worker&&(this.features.push("foundWorker"),this.camouflage=!0),this.os.version.isNewer("2.1")&&!window.applicationCache&&(this.features.push("noAppCache"),this.camouflage=!0)),"iOS"!==this.os.name&&"Safari"===this.browser.name&&this.browser.version&&(this.browser.version.isOlder("4.0")&&window.applicationCache&&(this.features.push("foundAppCache"),this.camouflage=!0),this.browser.version.isOlder("4.1")&&window.history&&history.pushState&&(this.features.push("foundHistory"),this.camouflage=!0),this.browser.version.isOlder("5.1")&&document.documentElement.webkitRequestFullScreen&&(this.features.push("foundFullscreen"),this.camouflage=!0),this.browser.version.isOlder("5.2")&&"FileReader"in window&&(this.features.push("foundFileReader"),this.camouflage=!0)))
}}},p}()});
;define("hiloan:node_modules/ua-device/lib/ua-device",function(e,i,a){var d=e("hiloan:node_modules/ua-device/lib/useragent-base");a.exports=function(e){var i,a,r=new d(e);if("mobile"===r.device.type||"tablet"===r.device.type){if((i=e.match(/(ZTE|Samsung|Motorola|HTC|Coolpad|Huawei|Lenovo|LG|Sony Ericsson|Oppo|TCL|Vivo|Sony|Meizu|Nokia)/i))&&(r.device.manufacturer=i[1],r.device.model&&r.device.model.indexOf(i[1])>=0&&(r.device.model=r.device.model.replace(i[1],""))),i=e.match(/(iPod|iPad|iPhone)/i))r.device.manufacturer="Apple",r.device.model=i[1];
else if(i=e.match(/[-\s](Galaxy[\s-_]nexus|Galaxy[\s-_]\w*[\s-_]\w*|Galaxy[\s-_]\w*|SM-\w*|GT-\w*|s[cgp]h-\w*|shw-\w*|ATIV|i9070|omnia|s7568|A3000|A3009|A5000|A5009|A7000|A7009|A8000|C101|C1116|C1158|E400|E500F|E7000|E7009|G3139D|G3502|G3502i|G3508|G3508J|G3508i|G3509|G3509i|G3558|G3559|G3568V|G3586V|G3589W|G3606|G3608|G3609|G3812|G388F|G5108|G5108Q|G5109|G5306W|G5308W|G5309W|G550|G600|G7106|G7108|G7108V|G7109|G7200|G720NO|G7508Q|G7509|G8508S|G8509V|G9006V|G9006W|G9008V|G9008W|G9009D|G9009W|G9198|G9200|G9208|G9209|G9250|G9280|I535|I679|I739|I8190N|I8262|I879|I879E|I889|I9000|I9060|I9082|I9082C|I9082i|I9100|I9100G|I9108|I9128|I9128E|I9128i|I9152|I9152P|I9158|I9158P|I9158V|I9168|I9168i|I9190|I9192|I9195|I9195I|I9200|I9208|I9220|I9228|I9260|I9268|I9300|I9300i|I9305|I9308|I9308i|I939|I939D|I939i|I9500|I9502|I9505|I9507V|I9508|I9508V|I959|J100|J110|J5008|J7008|N7100|N7102|N7105|N7108|N7108D|N719|N750|N7505|N7506V|N7508V|N7509V|N900|N9002|N9005|N9006|N9008|N9008S|N9008V|N9009|N9100|N9106W|N9108V|N9109W|N9150|N916|N9200|P709|P709E|P729|S6358|S7278|S7278U|S7562C|S7562i|S7898i|b9388)[\s\)]/i))r.device.manufacturer="Samsung",r.device.model=i[1].replace(/Galaxy S VI/i,"Galaxy S6").replace(/Galaxy S V/i,"Galaxy S5").replace(/Galaxy S IV/i,"Galaxy S4").replace(/Galaxy s III/i,"Galaxy S3").replace(/Galaxy S II/i,"Galaxy S2").replace(/Galaxy S I/i,"Galaxy S1").replace(/([a-z]+[0-9]{3})[0-9]?[a-z]*/i,"$1");
else if(r.device.manufacturer&&"samsung"===r.device.manufacturer.toLowerCase()&&r.device.model)r.device.model=r.device.model.replace(/Galaxy S VI/i,"Galaxy S6").replace(/Galaxy S V/i,"Galaxy S5").replace(/Galaxy S IV/i,"Galaxy S4").replace(/Galaxy s III/i,"Galaxy S3").replace(/Galaxy S II/i,"Galaxy S2").replace(/Galaxy S I/i,"Galaxy S1").replace(/([a-z]+[0-9]{3})[0-9]?[a-z]*/i,"$1");
else if(i=e.match(/(Huawei[\s-_](\w*[-_]?\w*)|\s(7D-\w*|ALE-\w*|ATH-\w*|CHE-\w*|CHM-\w*|Che1-\w*|Che2-\w*|D2-\w*|G616-\w*|G620S-\w*|G621-\w*|G660-\w*|G750-\w*|GRA-\w*|Hol-\w*|MT2-\w*|MT7-\w*|PE-\w*|PLK-\w*|SC-\w*|SCL-\w*|H60-\w*|H30-\w*)[\s\)])/i))r.device.manufacturer="Huawei",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3]),(i=r.device.model.match(/(\w*)[\s-_]+[a-z0-9]+/i))&&(r.device.model=i[1]);
else if(i=e.match(/;\s(mi|m1|m2|m3|m4|hm)(\s*\w*)[\s\)]/i))(a=e.match(/(meitu|MediaPad)/i))?(r.device.manufacturer=a[1],r.device.model=""):i[2].length>0&&!/\s/.test(i[2])?(a=i[2].match(/(\d)/i))&&(r.device.model=i[1]+"-"+a[1]):(r.device.manufacturer="Xiaomi",i[2]&&i[2].length>0?(i[2]=i[2].replace(/\s/,""),r.device.model=(i[1].substr(i[1].length-2)+"-"+i[2]).replace(/m(\d)-/i,"MI-$1")):r.device.model=i[1].substr(i[1].length-2).replace(/m(\d)/i,"MI-$1"),/(mi|hm)(-\d)/i.test(r.device.model)&&((i=r.device.model.match(/(mi|hm)(-\ds)/i))?r.device.model=i[1]+i[2]:(i=r.device.model.match(/(mi|hm)(-\d{2})/i))?r.device.model=i[1]:(i=r.device.model.match(/(mi|hm)(-\d)[A-Z]/i))&&(r.device.model=i[1]+i[2])),(i=r.device.model.match(/(mi|hm)(-\dg)/i))&&(r.device.model=i[1]));
else if(/build\/HM\d{0,7}\)/i.test(e))r.device.manufacturer="Xiaomi",r.device.model="HM";else if(i=e.match(/redmi\s?(\d+)?/i))r.device.manufacturer="Xiaomi",r.device.model="HM-"+i[1];else if(r.device.manufacturer&&"xiaomi"===r.device.manufacturer.toLowerCase()&&r.device.model)(i=r.device.model.match(/mi-one/i))?r.device.model="MI-1":(i=r.device.model.match(/mi-two/i))?r.device.model="MI-2":(i=r.device.model.match(/\d{6}/i))?r.device.model="":(i=r.device.model.match(/redmi/i))?r.device.model=r.device.model.toUpperCase().replace(/redmi/i,"HM"):(i=r.device.model.match(/(m\d)[\s-_](s?)/i))?r.device.model=i[1].replace(/m/,"MI-")+i[2]:(i=r.device.model.match(/(hm|mi)[\s-_](\d?)[a-rt-z]/i))?r.device.model=(a=r.device.model.match(/(mi|hm)[\s-_](note|pad)(\d?s?)/i))?a[1]+"-"+a[2]+a[3]:i[2]?i[1]+"-"+i[2]:i[1]:(i=r.device.model.match(/hm/i))&&(r.device.model=(i=r.device.model.match(/(hm)[\s-_](\d{2})/i))?"HM":(i=r.device.model.match(/(hm)[\s-_](\ds)/i))?"HM-"+i[2]:(i=r.device.model.match(/(hm)[\s-_](\d)[a-z]/i))?"HM-"+i[2]:"HM",/hm-\dg/.test(r.device.model)&&(r.device.model="HM"));
else if(i=e.match(/(vivo[\s-_](\w*)|\s(E1\w?|E3\w?|E5\w?|V1\w?|V2\w?|S11\w?|S12\w?|S1\w?|S3\w?|S6\w?|S7\w?|S9\w?|X1\w?|X3\w?|X520\w?|X5\w?|X5Max|X5Max+|X5Pro|X5SL|X710F|X710L|Xplay|Xshot|Xpaly3S|Y11\w?|Y11i\w?|Y11i\w?|Y13\w?|Y15\w?|Y17\w?|Y18\w?|Y19\w?|Y1\w?|Y20\w?|Y22\w?|Y22i\w?|Y23\w?|Y27\w?|Y28\w?|Y29\w?|Y33\w?|Y37\w?|Y3\w?|Y613\w?|Y622\w?|Y627\w?|Y913\w?|Y923\w?|Y927\w?|Y928\w?|Y929\w?|Y937\w?)[\s\)])/i))r.device.manufacturer="Vivo",r.device.model=i[1],r.device.model=r.device.model.replace(/(viv[\s-_]|vivo[\s-_]|bbg[\s-_])/i,""),(i=r.device.model.match(/([a-z]+[0-9]+)i?[a-z]?[\s-_]?/i))&&(r.device.model=i[1]);
else if(i=e.match(/(Oppo[\s-_](\w*)|\s(1100|1105|1107|3000|3005|3007|6607|A100|A103|A105|A105K|A109|A109K|A11|A113|A115|A115K|A121|A125|A127|A129|A201|A203|A209|A31|A31c|A31t|A31u|A51kc|A520|A613|A615|A617|E21W|Find|Mirror|N5110|N5117|N5207|N5209|R2010|R2017|R6007|R7005|R7007|R7c|R7t|R8000|R8007|R801|R805|R807|R809T|R8107|R8109|R811|R811W|R813T|R815T|R815W|R817|R819T|R8200|R8205|R8207|R821T|R823T|R827T|R830|R830S|R831S|R831T|R833T|R850|Real|T703|U2S|U521|U525|U529|U539|U701|U701T|U705T|U705W|X9000|X9007|X903|X905|X9070|X9077|X909|Z101|R829T)[\s\)])/i))r.device.manufacturer="Oppo",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3]),(i=r.device.model.match(/([a-z]+[0-9]+)-?(plus)/i))?r.device.model=i[1]+"-"+i[2]:(i=r.device.model.match(/(\w*-?[a-z]+[0-9]+)/i))&&(r.device.model=i[1]);
else if(r.device.manufacturer&&"oppo"===r.device.manufacturer.toLowerCase()&&r.device.model)(i=r.device.model.match(/([a-z]+[0-9]+)-?(plus)/i))?r.device.model=i[1]+"-"+i[2]:(i=r.device.model.match(/(\w*-?[a-z]+[0-9]+)/i))&&(r.device.model=i[1]);
else if(i=e.match(/(Lenovo[\s-_](\w*[-_]?\w*)|\s(A3580|A3860|A5500|A5600|A5860|A7600|A806|A800|A808T|A808T-I|A936|A938t|A788t|K30-E|K30-T|K30-W|K50-T3s|K50-T5|K80M|K910|K910e|K920|S90-e|S90-t|S90-u|S968T|X2-CU|X2-TO|Z90-3|Z90-7)[\s\)])/i))r.device.manufacturer="Lenovo",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3]),(i=r.device.model.match(/([a-z]+[0-9]+)/i))&&(r.device.model=i[1]);
else if(i=e.match(/(Coolpad[\s-_](\w*)|\s(7295C|7298A|7620L|8908|8085|8970L|9190L|Y80D)[\s\)])/i))r.device.manufacturer="Coolpad",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3]),(i=r.device.model.match(/([a-z]?[0-9]+)/i))&&(r.device.model=i[1]);
else if(r.device.manufacturer&&"coolpad"===r.device.manufacturer.toLowerCase()&&r.device.model)(i=r.device.model.match(/([a-z]?[0-9]+)/i))&&(r.device.model=i[1]);else if(i=e.match(/\s(mx\d*\w*|mz-(\w*))\s(\w*)\s*\w*\s*build/i)){r.device.manufacturer="Meizu";
var o=i[2]?i[2]:i[1];r.device.model=i[3]?o+"-"+i[3]:o+""}else(i=e.match(/M463C|M35\d/i))?(r.device.manufacturer="Meizu",r.device.model=i[1]):(i=e.match(/(Htc[-_\s](\w*)|\s(601e|606w|608t|609d|610t|6160|619d|620G|626d|626s|626t|626w|709d|801e|802d|802t|802w|809D|816d|816e|816t|816v|816w|826d|826s|826t|826w|828w|901e|919d|A310e|A50AML|A510e|A620d|A620e|A620t|A810e|A9191|Aero|C620d|C620e|C620t|D316d|D516d|D516t|D516w|D820mt|D820mu|D820t|D820ts|D820u|D820us|E9pt|E9pw|E9sw|E9t|HD7S|M8Et|M8Sd|M8St|M8Sw|M8d|M8e|M8s|M8si|M8t|M8w|M9W|M9ew|Phablet|S510b|S510e|S610d|S710d|S710e|S720e|S720t|T327t|T328d|T328t|T328w|T329d|T329t|T329w|T528d|T528t|T528w|T8698|WF5w|X315e|X710e|X715e|X720d|X920e|Z560e|Z710e|Z710t|Z715e)[\s\)])/))?(r.device.manufacturer="Htc",r.device.model=i[1]):(i=e.match(/(Gionee[\s-_](\w*)|\s(GN\d+\w*)[\s\)])/i))?(r.device.manufacturer="Gionee",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3])):(i=e.match(/(LG[-_](\w*)|\s(D728|D729|D802|D855|D856|D857|D858|D859|E985T|F100L|F460|H778|H818|H819|P895|VW820)[\s\)])/i))?(r.device.manufacturer="Lg",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3])):(i=e.match(/(Tcl[\s-_](\w*)|\s(H916T|P588L|P618L|P620M|P728M)[\s\)])/))?(r.device.manufacturer="Tcl",r.device.model=i[1]):(i=e.match(/(V9180|N918)/i))?(r.device.manufacturer="Zte",r.device.model=i[1]):r.device.manufacturer&&"zte"===r.device.manufacturer.toLowerCase()&&r.device.model?(i=r.device.model.match(/([a-z]?[0-9]+)/i))&&(r.device.model=i[1]):(i=e.match(/(UIMI\w*|umi\w*)[\s-_](\w*)\s*\w*\s*build/i))?(r.device.manufacturer="Uimi",r.device.model=i[2]?i[1]+"-"+i[2]:i[1]+""):(i=e.match(/eton[\s-_](\w*)/i))?(r.device.manufacturer="Eton",r.device.model=i[1]):(i=e.match(/(SM705|SM701|YQ601|YQ603)/i))?(r.device.manufacturer="Smartisan",r.device.model={SM705:"T1",SM701:"T1",YQ601:"U1",YQ603:"U1"}[i[1]]||i[1]):(i=e.match(/(Asus[\s-_](\w*)|\s(A500CG|A500KL|A501CG|A600CG|PF400CG|PF500KL|T001|X002|X003|ZC500TG|ZE550ML|ZE551ML)[\s\)])/i))?(r.device.manufacturer="Asus",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3])):(i=e.match(/(Nubia[-_\s](\w*)|(NX501|NX505J|NX506J|NX507J|NX503A|nx\d+\w*)[\s\)])/i))?(r.device.manufacturer="Nubia",i[2]?r.device.model=i[2]:i[3]&&(r.device.model=i[3])):(i=e.match(/(HT-\w*)|Haier[\s-_](\w*-?\w*)/i))?(r.device.manufacturer="Haier",i[1]?r.device.model=i[1]:i[2]&&(r.device.model=i[2])):(i=e.match(/K-Touch[\s-_](tou\s?ch\s?(\d)|\w*)/i))?(r.device.manufacturer="K-Touch",r.device.model=i[2]?"Ktouch"+i[2]:i[1]):(i=e.match(/Doov[\s-_](\w*)/i))?(r.device.manufacturer="Doov",r.device.model=i[1]):/koobee/i.test(e)?r.device.manufacturer="koobee":/C69/i.test(e)?r.device.manufacturer="Sony":/N787|N818S/i.test(e)?r.device.manufacturer="Haojixing":(i=e.match(/(hs-|Hisense[\s-_])(\w*)/i))&&(r.device.manufacturer="Hisense",r.device.model=i[2]);
r.device.manufacturer&&(r.device.manufacturer=r.device.manufacturer.substr(0,1).toUpperCase()+r.device.manufacturer.substr(1).toLowerCase()),r.device.model&&(r.device.model=r.device.model.toUpperCase().replace(/-+|_+|\s+/g," "),r.device.model=r.device.model.match(/\s*(\w*\s*\w*)/)[1].replace(/\s+/,"-"),"Samsung"===r.device.manufacturer?r.device.model={"SCH-I95":"GT-I950","SCH-I93":"GT-I930","SCH-I86":"GT-I855","SCH-N71":"GT-N710","SCH-I73":"GT-S789","SCH-P70":"GT-I915"}[r.device.model]||r.device.model:"Huawei"===r.device.manufacturer&&(r.device.model={CHE1:"CHE",CHE2:"CHE",G620S:"G621",C8817D:"G621"}[r.device.model]||r.device.model)),r.device.manufacturer&&"Xiaomi"===r.device.manufacturer&&((i=r.device.model.match(/(hm|mi)-(note)/i))?r.device.model=i[1]+"-"+i[2]:(i=r.device.model.match(/(hm|mi)-(\ds?)/i))?r.device.model=i[1]+"-"+i[2]:(i=r.device.model.match(/(hm|mi)-(\d)[a-rt-z]/i))&&(r.device.model=i[1]+"-"+i[2]))
}if("desktop"===r.device.type?(i=/360se(?:[ \/]([\w.]+))?/i.exec(e))?(r.browser.name="360 security Explorer",r.browser.version={original:i[1]}):(i=/the world(?:[ \/]([\w.]+))?/i.exec(e))?(r.browser.name="the world",r.browser.version={original:i[1]}):(i=/tencenttraveler ([\w.]+)/i.exec(e))?(r.browser.name="tencenttraveler",r.browser.version={original:i[1]}):(i=/LBBROWSER/i.exec(e))&&(r.browser.name="LBBROWSER"):("mobile"===r.device.type||"tablet"===r.device.type)&&((i=/BaiduHD\s+([\w.]+)/i.exec(e))?(r.browser.name="BaiduHD",r.browser.version={original:i[1]}):(i=/360.s*aphone\s*browser\s*\(version\s*([\w.]+)\)/i.exec(e))?(r.browser.name="360 Browser",r.browser.version={original:i[1]}):(i=/flyflow\/([\w.]+)/i.exec(e))?(r.browser.name="Baidu Browser",r.browser.version={original:i[1]}):(i=/baiduhd ([\w.]+)/i.exec(e))?(r.browser.name="Baidu HD",r.browser.version={original:i[1]}):(i=/baidubrowser\/([\d\.]+)\s/i.exec(e))?(r.browser.name="baidubrowser",r.browser.version={original:i[1]}):(i=/LieBaoFast\/([\w.]+)/i.exec(e))?(r.browser.name="LieBao Fast",r.browser.version={original:i[1]}):(i=/LieBao\/([\w.]+)/i.exec(e))?(r.browser.name="LieBao",r.browser.version={original:i[1]}):(i=/Sogou\w+\/([0-9\.]+)/i.exec(e))?(r.browser.name="SogouMobileBrowser",r.browser.version={original:i[1]}):(i=/bdbrowser\w+\/([0-9\.]+)/i.exec(e))?(r.browser.name="百度国际",r.browser.version={original:i[1]}):"Android"===r.os.name&&/safari/i.test(e)&&(i=/chrome\/([0-9\.]+)/i.exec(e))?(a=e.match(/\s+(\w+Browser)\/?([\d\.]*)/))?(r.browser.name=a[1],r.browser.version=a[2]?{original:a[2]}:{original:i[1]}):(r.browser.name="Android Chrome",r.browser.version={original:i[1]}):"Android"===r.os.name&&/safari/i.test(e)&&(i=/version\/([0-9\.]+)/i.exec(e))?(a=e.match(/\s+(\w+Browser)\/?([\d\.]*)/))?(r.browser.name=a[1],r.browser.version=a[2]?{original:a[2]}:{original:i[1]}):(r.browser.name="Android Browser",r.browser.version={original:i[1]}):/(ipad|iphone).* applewebkit\/.* mobile/i.test(e)&&(r.browser.name="Safari")),(i=e.match(/baiduboxapp\/?([\d\.]*)/i))?(r.browser.name="百度框",i[1]&&(r.browser.version={original:i[1]})):/BaiduLightAppRuntime/i.test(e)?r.browser.name="轻应用runtime":/Weibo/i.test(e)?r.browser.name="微博":/MQQ/i.test(e)?r.browser.name="手机QQ":/hao123/i.test(e)&&(r.browser.name="hao123"),i=/MicroMessenger\/([\w.]+)/i.exec(e)){r.browser.name="微信";
var c=i[1].replace(/_/g,".");a=/(\d+\.\d+\.\d+\.\d+)/.exec(c),a&&(c=a[1]),r.browser.version={original:c}}return(i=/UCBrowser\/([\w.]+)/i.exec(e))&&(r.browser.name="UC Browser",r.browser.version={original:i[1]}),(i=/OPR\/([\w.]+)/i.exec(e))?(r.browser.name="Opera",r.browser.version={original:i[1]}):(i=/OPiOS\/([\w.]+)/i.exec(e))?(r.browser.name="Opera",r.browser.version={original:i[1]}):/Trident\/7/i.test(e)&&/rv:11/i.test(e)?(r.browser.name="Internet Explorer",r.browser.version={major:"11",original:"11"}):/Edge\/12/i.test(e)&&/Windows Phone|Windows NT/i.test(e)?(r.browser.name="Microsoft Edge",r.browser.version={major:"12",original:"12"}):(i=/miuibrowser\/([\w.]+)/i.exec(e))&&(r.browser.name="miui browser",r.browser.version={original:i[1]}),r.browser.name||(i=/Safari\/([\w.]+)/i.exec(e)&&/Version/i.test(e))&&(r.browser.name="Safari"),r.browser.name&&!r.browser.version&&(i=/Version\/([\w.]+)/i.exec(e))&&(r.browser.version={original:i[1]}),"Windows"===r.os.name||/Windows/i.test(e)?(r.os.name="Windows",/NT 6.3/i.test(e)?r.os.version={alias:"8.1",original:"8.1"}:(/NT 6.4/i.test(e)||/NT 10.0/i.test(e))&&(r.os.version={alias:"10",original:"10"})):"Mac OS X"===r.os.name?(r.os.name="Mac OS X",r.os.version=(i=/Mac OS X[\s\_\-\/](\d+[\.\-\_]\d+[\.\-\_]?\d*)/i.exec(e))?{alias:i[1].replace(/_/g,"."),original:i[1].replace(/_/g,".")}:{alias:"",original:""}):/Android/i.test(r.os.name)&&(i=e.match(/Android[\s\_\-\/i686]?[\s\_\-\/](\d+[\.\-\_]\d+[\.\-\_]?\d*)/i))&&(r.os.version={alias:i[1],original:i[1]}),r
}});
;define("hiloan:node_modules/ua-device/index",function(e,d,i){i.exports=e("hiloan:node_modules/ua-device/lib/ua-device")});
;define("hiloan:node_modules/whatwg-fetch/fetch",function(){!function(t){"use strict";function e(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(t))throw new TypeError("Invalid character in header field name");
return t.toLowerCase()}function r(t){return"string"!=typeof t&&(t=String(t)),t}function o(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return m.iterable&&(e[Symbol.iterator]=function(){return e
}),e}function n(t){this.map={},t instanceof n?t.forEach(function(t,e){this.append(e,t)},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function i(t){return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)
}function s(t){return new Promise(function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}})}function a(t){var e=new FileReader,r=s(e);return e.readAsArrayBuffer(t),r}function u(t){var e=new FileReader,r=s(e);
return e.readAsText(t),r}function f(t){for(var e=new Uint8Array(t),r=new Array(e.length),o=0;o<e.length;o++)r[o]=String.fromCharCode(e[o]);return r.join("")}function h(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);
return e.set(new Uint8Array(t)),e.buffer}function d(){return this.bodyUsed=!1,this._initBody=function(t){if(this._bodyInit=t,t)if("string"==typeof t)this._bodyText=t;else if(m.blob&&Blob.prototype.isPrototypeOf(t))this._bodyBlob=t;
else if(m.formData&&FormData.prototype.isPrototypeOf(t))this._bodyFormData=t;else if(m.searchParams&&URLSearchParams.prototype.isPrototypeOf(t))this._bodyText=t.toString();else if(m.arrayBuffer&&m.blob&&v(t))this._bodyArrayBuffer=h(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer]);
else{if(!m.arrayBuffer||!ArrayBuffer.prototype.isPrototypeOf(t)&&!_(t))throw new Error("unsupported BodyInit type");this._bodyArrayBuffer=h(t)}else this._bodyText="";this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):m.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))
},m.blob&&(this.blob=function(){var t=i(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");
return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?i(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(a)}),this.text=function(){var t=i(this);
if(t)return t;if(this._bodyBlob)return u(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(f(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");
return Promise.resolve(this._bodyText)},m.formData&&(this.formData=function(){return this.text().then(l)}),this.json=function(){return this.text().then(JSON.parse)},this}function y(t){var e=t.toUpperCase();
return B.indexOf(e)>-1?e:t}function c(t,e){e=e||{};var r=e.body;if("string"==typeof t)this.url=t;else{if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new n(t.headers)),this.method=t.method,this.mode=t.mode,r||null==t._bodyInit||(r=t._bodyInit,t.bodyUsed=!0)
}if(this.credentials=e.credentials||this.credentials||"omit",(e.headers||!this.headers)&&(this.headers=new n(e.headers)),this.method=y(e.method||this.method||"GET"),this.mode=e.mode||this.mode||null,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&r)throw new TypeError("Body not allowed for GET or HEAD requests");
this._initBody(r)}function l(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var r=t.split("="),o=r.shift().replace(/\+/g," "),n=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(o),decodeURIComponent(n))
}}),e}function p(t){var e=new n;return t.split("\r\n").forEach(function(t){var r=t.split(":"),o=r.shift().trim();if(o){var n=r.join(":").trim();e.append(o,n)}}),e}function b(t,e){e||(e={}),this.type="default",this.status="status"in e?e.status:200,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in e?e.statusText:"OK",this.headers=new n(e.headers),this.url=e.url||"",this._initBody(t)
}if(!t.fetch){var m={searchParams:"URLSearchParams"in t,iterable:"Symbol"in t&&"iterator"in Symbol,blob:"FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),formData:"FormData"in t,arrayBuffer:"ArrayBuffer"in t};
if(m.arrayBuffer)var w=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],v=function(t){return t&&DataView.prototype.isPrototypeOf(t)
},_=ArrayBuffer.isView||function(t){return t&&w.indexOf(Object.prototype.toString.call(t))>-1};n.prototype.append=function(t,o){t=e(t),o=r(o);var n=this.map[t];n||(n=[],this.map[t]=n),n.push(o)},n.prototype["delete"]=function(t){delete this.map[e(t)]
},n.prototype.get=function(t){var r=this.map[e(t)];return r?r[0]:null},n.prototype.getAll=function(t){return this.map[e(t)]||[]},n.prototype.has=function(t){return this.map.hasOwnProperty(e(t))},n.prototype.set=function(t,o){this.map[e(t)]=[r(o)]
},n.prototype.forEach=function(t,e){Object.getOwnPropertyNames(this.map).forEach(function(r){this.map[r].forEach(function(o){t.call(e,o,r,this)},this)},this)},n.prototype.keys=function(){var t=[];return this.forEach(function(e,r){t.push(r)
}),o(t)},n.prototype.values=function(){var t=[];return this.forEach(function(e){t.push(e)}),o(t)},n.prototype.entries=function(){var t=[];return this.forEach(function(e,r){t.push([r,e])}),o(t)},m.iterable&&(n.prototype[Symbol.iterator]=n.prototype.entries);
var B=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];c.prototype.clone=function(){return new c(this,{body:this._bodyInit})},d.call(c.prototype),d.call(b.prototype),b.prototype.clone=function(){return new b(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new n(this.headers),url:this.url})
},b.error=function(){var t=new b(null,{status:0,statusText:""});return t.type="error",t};var A=[301,302,303,307,308];b.redirect=function(t,e){if(-1===A.indexOf(e))throw new RangeError("Invalid status code");
return new b(null,{status:e,headers:{location:t}})},t.Headers=n,t.Request=c,t.Response=b,t.fetch=function(t,e){return new Promise(function(r,o){var n=new c(t,e),i=new XMLHttpRequest;i.onload=function(){var t={status:i.status,statusText:i.statusText,headers:p(i.getAllResponseHeaders()||"")};
t.url="responseURL"in i?i.responseURL:t.headers.get("X-Request-URL");var e="response"in i?i.response:i.responseText;r(new b(e,t))},i.onerror=function(){o(new TypeError("Network request failed"))},i.ontimeout=function(){o(new TypeError("Network request failed"))
},i.open(n.method,n.url,!0),"include"===n.credentials&&(i.withCredentials=!0),"responseType"in i&&m.blob&&(i.responseType="blob"),n.headers.forEach(function(t,e){i.setRequestHeader(e,t)}),i.send("undefined"==typeof n._bodyInit?null:n._bodyInit)
})},t.fetch.polyfill=!0}}("undefined"!=typeof self?self:this)});
;define("hiloan:node_modules/store/store",function(e,t,n){var r="undefined"!=typeof r?r:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};!function(e,r){"function"==typeof define&&define.amd?define([],r):"object"==typeof t?n.exports=r():e.store=r()
}(this,function(){function e(){try{return a in i&&i[a]}catch(e){return!1}}var t,n={},i="undefined"!=typeof window?window:r,o=i.document,a="localStorage",u="script";if(n.disabled=!1,n.version="1.3.20",n.set=function(){},n.get=function(){},n.has=function(e){return void 0!==n.get(e)
},n.remove=function(){},n.clear=function(){},n.transact=function(e,t,r){null==r&&(r=t,t=null),null==t&&(t={});var i=n.get(e,t);r(i),n.set(e,i)},n.getAll=function(){},n.forEach=function(){},n.serialize=function(e){return JSON.stringify(e)
},n.deserialize=function(e){if("string"!=typeof e)return void 0;try{return JSON.parse(e)}catch(t){return e||void 0}},e())t=i[a],n.set=function(e,r){return void 0===r?n.remove(e):(t.setItem(e,n.serialize(r)),r)
},n.get=function(e,r){var i=n.deserialize(t.getItem(e));return void 0===i?r:i},n.remove=function(e){t.removeItem(e)},n.clear=function(){t.clear()},n.getAll=function(){var e={};return n.forEach(function(t,n){e[t]=n
}),e},n.forEach=function(e){for(var r=0;r<t.length;r++){var i=t.key(r);e(i,n.get(i))}};else if(o&&o.documentElement.addBehavior){var c,f;try{f=new ActiveXObject("htmlfile"),f.open(),f.write("<"+u+">document.w=window</"+u+'><iframe src="/favicon.ico"></iframe>'),f.close(),c=f.w.frames[0].document,t=c.createElement("div")
}catch(d){t=o.createElement("div"),c=o.body}var l=function(e){return function(){var r=Array.prototype.slice.call(arguments,0);r.unshift(t),c.appendChild(t),t.addBehavior("#default#userData"),t.load(a);
var i=e.apply(n,r);return c.removeChild(t),i}},s=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g"),v=function(e){return e.replace(/^d/,"___$&").replace(s,"___")};n.set=l(function(e,t,r){return t=v(t),void 0===r?n.remove(t):(e.setAttribute(t,n.serialize(r)),e.save(a),r)
}),n.get=l(function(e,t,r){t=v(t);var i=n.deserialize(e.getAttribute(t));return void 0===i?r:i}),n.remove=l(function(e,t){t=v(t),e.removeAttribute(t),e.save(a)}),n.clear=l(function(e){var t=e.XMLDocument.documentElement.attributes;
e.load(a);for(var n=t.length-1;n>=0;n--)e.removeAttribute(t[n].name);e.save(a)}),n.getAll=function(){var e={};return n.forEach(function(t,n){e[t]=n}),e},n.forEach=l(function(e,t){for(var r,i=e.XMLDocument.documentElement.attributes,o=0;r=i[o];++o)t(r.name,n.deserialize(e.getAttribute(r.name)))
})}try{var m="__storejs__";n.set(m,m),n.get(m)!=m&&(n.disabled=!0),n.remove(m)}catch(d){n.disabled=!0}return n.enabled=!n.disabled,n})});
;define("hiloan:node_modules/vue-validator/dist/vue-validator.common",function(t,i,e){"use strict";function n(t,i){window.console&&(console.warn("[vue-validator] "+t),i&&console.warn(i.stack))}function a(t){if(null===t||void 0===t)return!0;
if(Array.isArray(t)){if(t.length>0)return!1;if(0===t.length)return!0}else if(A.Vue.util.isPlainObject(t))for(var i in t)if(A.Vue.util.hasOwn(t,i))return!1;return!0}function o(t,i,e){if(Array.isArray(t))for(var n=0;n<t.length;n++)i.call(e||t[n],t[n],n);
else if(A.Vue.util.isPlainObject(t)){var a=A.Vue.util.hasOwn;for(var o in t)a(t,o)&&i.call(e||t[o],t[o],o)}}function r(t,i){var e=A.Vue.util.indexOf(t,i);return~e?t.splice(e,1):null}function s(t,i,e){var n=document.createEvent("HTMLEvents");
if(n.initEvent(i,!0,!1),e)for(var a in e)n[a]=e[a];try{t.dispatchEvent(n)}catch(n){}}function l(t){return t&&"function"==typeof t.then}function d(t,i,e){if(i=i.trim(),-1===i.indexOf(" "))return void e(t,i);
for(var n=i.split(/\s+/),a=0,o=n.length;o>a;a++)e(t,n[a])}function u(t){if(Array.isArray(t)){if(0!==t.length){for(var i=!0,e=0,n=t.length;n>e&&(i=u(t[e]),i);e++);return i}return!1}return"number"==typeof t||"function"==typeof t?!0:"boolean"==typeof t?t:"string"==typeof t?t.length>0:null!==t&&"object"===("undefined"==typeof t?"undefined":E.typeof(t))?Object.keys(t).length>0:null===t||void 0===t?!1:void 0
}function h(t,i){if("string"!=typeof i)return!1;var e=i.match(new RegExp("^/(.*?)/([gimy]*)$"));return e?new RegExp(e[1],e[2]).test(t):!1}function c(t,i){return"string"==typeof t?g(i,10)&&t.length>=parseInt(i,10):Array.isArray(t)?t.length>=parseInt(i,10):!1
}function f(t,i){return"string"==typeof t?g(i,10)&&t.length<=parseInt(i,10):Array.isArray(t)?t.length<=parseInt(i,10):!1}function p(t,i){return!isNaN(+t)&&!isNaN(+i)&&+t>=+i}function v(t,i){return!isNaN(+t)&&!isNaN(+i)&&+i>=+t
}function g(t){return/^(-?[1-9]\d*|0)$/.test(t)}function _(t){var i=t.util.extend,e=Object.create(null);i(e,F),t.options.validators=e;var n=t.config.optionMergeStrategies;n&&(n.validators=function(t,e){if(!e)return t;
if(!t)return e;var n=Object.create(null);i(n,t);for(var a in e)n[a]=e[a];return n}),t.validator=function(i,e){return e?void(t.options.validators[i]=e):t.options.validators[i]}}function m(t){var i=t.prototype._init;
t.prototype._init=function(t){this._validatorMaps||(this._validatorMaps=Object.create(null)),i.call(this,t)};var e=t.prototype._destroy;t.prototype._destroy=function(){e.apply(this,arguments),this._validatorMaps=null
}}function y(t){var i=t.directive("if"),e=t.FragmentFactory,n=t.util,a=n.toArray,o=n.replace,r=n.createAnchor;t.directive("validate-class",{terminal:!0,priority:i.priority+x,bind:function(){var t=this,i=String(M++);
this.setClassIds(this.el,i),this.vm.$on(S,this.cb=function(e,n,a){e.indexOf(i)>-1&&n.updateClasses(a,t.frag.node)}),this.setupFragment()},unbind:function(){this.vm.$off(S,this.cb),this.teardownFragment()
},setClassIds:function(t,i){for(var e=a(t.childNodes),n=0,o=e.length;o>n;n++){var r=e[n];if(1===r.nodeType)for(var s=r.hasAttributes(),l=s&&a(r.attributes),d=0,u=l.length;u>d;d++){var h=l[d];if(h.name.match(O)){var c=r.getAttribute(S),f=c?c+","+i:i;
r.setAttribute(S,f)}}r.hasChildNodes()&&this.setClassIds(r,i)}},setupFragment:function(){this.anchor=r("v-validate-class"),o(this.el,this.anchor),this.factory=new e(this.vm,this.el),this.frag=this.factory.create(this._host,this._scope,this._frag),this.frag.before(this.anchor)
},teardownFragment:function(){this.frag&&(this.frag.remove(),this.frag=null,this.factory=null),o(this.anchor,this.el),this.anchor=null}})}function V(t){function i(){if(r){var t=document.createElement("textarea");
return t.placeholder="t","t"===t.cloneNode(!0).value}return!1}var e=t.FragmentFactory,n=t.parsers.directive.parseDirective,a=t.util,r=a.inBrowser,s=a.bind,l=a.on,d=a.off,u=a.createAnchor,h=a.replace,c=a.camelize,f=a.isPlainObject,p=i();
t.directive("validate",{deep:!0,terminal:!0,priority:N,params:["group","field","detect-blur","detect-change","initial","classes"],paramWatchers:{detectBlur:function(t){this._invalid||(this.validation.detectBlur=this.isDetectBlur(t),this.validator.validate(this.field))
},detectChange:function(t){this._invalid||(this.validation.detectChange=this.isDetectChange(t),this.validator.validate(this.field))}},bind:function(){var t=this.el,i=this.vm.$options._validator,e=t.getAttribute("v-model"),n=this.parseModelRaw(e),a=n.model,o=n.filters;
this.model=a,this.setupFragment(),this.setupValidate(i,a,o),this.listen()},update:function(t,i){if(t&&!this._invalid){f(t)||i&&f(i)?this.handleObject(t,i,this.params.initial):(Array.isArray(t)||i&&Array.isArray(i))&&this.handleArray(t,i,this.params.initial);
var e={field:this.field};this.frag&&(e.el=this.frag.node),this.validator.validate(e)}},unbind:function(){this._invalid||(this.unlisten(),this.teardownValidate(),this.teardownFragment(),this.model=null)
},parseModelRaw:function(t){if(I.test(t)){var i=n(t);return{model:i.expression,filters:i.filters}}return{model:t}},setupValidate:function(t,i,e){var n=this.params,a=this.validator=this.vm._validatorMaps[t];
this.field=c(this.arg?this.arg:n.field),this.validation=a.manageValidation(this.field,i,this.vm,this.getElementFrom(this.frag),this._scope,e,n.initial,this.isDetectBlur(n.detectBlur),this.isDetectChange(n.detectChange)),f(n.classes)&&this.validation.setValidationClasses(n.classes),n.group&&a.addGroupValidation(n.group,this.field)
},listen:function(){var t=this.model,i=this.validation,e=this.getElementFrom(this.frag);this.onBlur=s(i.listener,i),l(e,"blur",this.onBlur),"radio"!==e.type&&"SELECT"!==e.tagName||t?"checkbox"===e.type?t?(this.onClick=s(i.listener,i),l(e,"click",this.onClick)):(this.onChange=s(i.listener,i),l(e,"change",this.onChange)):t||(this.onInput=s(i.listener,i),l(e,"input",this.onInput)):(this.onChange=s(i.listener,i),l(e,"change",this.onChange))
},unlisten:function(){var t=this.getElementFrom(this.frag);this.onInput&&(d(t,"input",this.onInput),this.onInput=null),this.onClick&&(d(t,"click",this.onClick),this.onClick=null),this.onChange&&(d(t,"change",this.onChange),this.onChange=null),this.onBlur&&(d(t,"blur",this.onBlur),this.onBlur=null)
},teardownValidate:function(){if(this.validator&&this.validation){var t=this.getElementFrom(this.frag);this.params.group&&this.validator.removeGroupValidation(this.params.group,this.field),this.validator.unmanageValidation(this.field,t),this.validator=null,this.validation=null,this.field=null
}},setupFragment:function(){this.anchor=u("v-validate"),h(this.el,this.anchor),this.factory=new e(this.vm,this.shimNode(this.el)),this.frag=this.factory.create(this._host,this._scope,this._frag),this.frag.before(this.anchor)
},teardownFragment:function(){this.frag&&(this.frag.remove(),this.frag=null,this.factory=null),h(this.anchor,this.el),this.anchor=null},handleArray:function(t,i,e){var n=this;i&&this.validation.resetValidation(),o(t,function(t){n.validation.setValidation(t,void 0,void 0,e)
})},handleObject:function(t,i,e){var n=this;i&&this.validation.resetValidation(),o(t,function(t,i){if(f(t)){if("rule"in t){var a="message"in t?t.message:null,o="initial"in t?t.initial:null;n.validation.setValidation(i,t.rule,a,o||e)
}}else n.validation.setValidation(i,t,void 0,e)})},isDetectBlur:function(t){return void 0===t||"on"===t||t===!0},isDetectChange:function(t){return void 0===t||"on"===t||t===!0},isInitialNoopValidation:function(t){return"off"===t||t===!1
},shimNode:function(t){var i=t;if(p&&"TEXTAREA"===t.tagName){i=t.cloneNode(!0),i.value=t.value;for(var e=i.childNodes.length;e--;)i.removeChild(i.childNodes[e])}return i},getElementFrom:function(t){return t.single?t.node:t.node.nextSibling
}})}function b(t){var i=t.FragmentFactory,e=t.directive("if"),n=t.util,a=n.isArray,o=n.isPlainObject,r=n.createAnchor,s=n.replace,l=n.extend,d=n.camelize;t.elementDirective("validator",{params:["name","groups","lazy","classes"],bind:function(){var t=this.params;
if(this.validatorName="$"+d(t.name),!this.vm._validatorMaps)throw new Error("Invalid validator management error");var i={};o(this.params.classes)&&(i=this.params.classes),this.setupValidator(i),this.setupFragment(t.lazy)
},unbind:function(){this.teardownFragment(),this.teardownValidator()},getGroups:function(){var t=this.params,i=[];return t.groups&&(a(t.groups)?i=t.groups:o(t.groups)||"string"!=typeof t.groups||i.push(t.groups)),i
},setupValidator:function(t){var i=this.validator=new T(this.validatorName,this,this.getGroups(),t);i.enableReactive(),i.setupScope(),i.registerEvents()},teardownValidator:function(){this.validator.unregisterEvents(),this.validator.disableReactive(),this.validatorName&&(this.validatorName=null,this.validator=null)
},setupFragment:function(t){var n=this,a=this.vm;this.validator.waitFor(function(){n.anchor=r("vue-validator"),s(n.el,n.anchor),l(a.$options,{_validator:n.validatorName}),n.factory=new i(a,n.el.innerHTML),e.insert.call(n)
}),!t&&a.$activateValidator()},teardownFragment:function(){e.unbind.call(this)}})}function w(){var t={name:"validator-error",props:{field:{type:String,required:!0},validator:{type:String},message:{type:String,required:!0},partial:{type:String,"default":"validator-error-default"}},template:'<div><partial :name="partial"></partial></div>',partials:{}};
return t.partials["validator-error-default"]="<p>{{field}}: {{message}}</p>",t}function C(t){var i=t.util,e=w(t),n={name:"validator-errors",props:{validation:{type:Object,required:!0},group:{type:String,"default":null},field:{type:String,"default":null},component:{type:String,"default":"validator-error"}},computed:{errors:function(){var t=this;
if(null!==this.group)return this.validation[this.group].errors;if(null!==this.field){var e=this.validation[this.field];if(!e.errors)return;return e.errors.map(function(e){var n={field:t.field};return i.isPlainObject(e)?(e.validator&&(n.validator=e.validator),n.message=e.message):"string"==typeof e&&(n.message=e),n
})}return this.validation.errors}},template:'<template v-for="error in errors"><component :is="component" :partial="partial" :field="error.field" :validator="error.validator" :message="error.message"></component></template>',components:{}};
return n.props.partial=e.props.partial,n.components[e.name]=e,t.component(n.name,n),n}function k(t){arguments.length<=1||void 0===arguments[1]?{}:arguments[1];return k.installed?void n("already installed."):(A.Vue=t,_(t),C(t),m(t),b(t),y(t),void V(t))
}var E={};E.typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},E.classCallCheck=function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")
},E.createClass=function(){function t(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(i,e,n){return e&&t(i.prototype,e),n&&t(i,n),i
}}(),E.inherits=function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function, not "+typeof i);t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(t,i):t.__proto__=i)
},E.possibleConstructorReturn=function(t,i){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!i||"object"!=typeof i&&"function"!=typeof i?t:i};var A={},F=Object.freeze({required:u,pattern:h,minlength:c,maxlength:f,min:p,max:v}),S="__vue-validator-validate-update__",N=4096,x=32,I=/[^|]\|[^|]/,O=/^v-validate(?:$|:(.*)$)/,$=/^v-on:|^@/,M=0,j=function(){function t(i,e,n,a,o,r,s,l,d){E.classCallCheck(this,t),this.field=i,this.touched=!1,this.dirty=!1,this.modified=!1,this._modified=!1,this._model=e,this._filters=s,this._validator=r,this._vm=n,this._el=a,this._forScope=o,this._init=this._getValue(a),this._validators={},this._detectBlur=l,this._detectChange=d,this._classes={}
}return t.prototype.manageElement=function(t,i){var e=this,n=this._getScope(),a=this._model;this._initial=i;var o=t.getAttribute(S);o&&(t.removeAttribute(S),this._classIds=o.split(",")),a&&(t.value=this._evalModel(a,this._filters),this._unwatch=n.$watch(a,function(i,n){if(i!==n){if(e.guardValidate(t,"input"))return;
e.handleValidate(t,{noopable:e._initial}),e._initial&&(e._initial=null)}},{deep:!0}))},t.prototype.unmanageElement=function(){this._unwatch&&this._unwatch()},t.prototype.resetValidation=function(){var t=this,i=Object.keys(this._validators);
o(i,function(i){t._validators[i]=null,delete t._validators[i]})},t.prototype.resetValidationNoopable=function(){o(this._validators,function(t){t.initial&&!t._isNoopable&&(t._isNoopable=!0)})},t.prototype.setValidation=function(t,i,e,n){var a=this._validators[t];
a||(a=this._validators[t]={},a.name=t),a.arg=i,e&&(a.msg=e),n&&(a.initial=n,a._isNoopable=!0)},t.prototype.setValidationClasses=function(t){var i=this;o(t,function(t,e){i._classes[e]=t})},t.prototype.willUpdateFlags=function(){var t=arguments.length<=0||void 0===arguments[0]?!1:arguments[0];
t&&this.willUpdateTouched(this._el,"blur"),this.willUpdateDirty(this._el),this.willUpdateModified(this._el)},t.prototype.willUpdateTouched=function(t,i){i&&"blur"===i&&(this.touched=!0,this._fireEvent(t,"touched"))
},t.prototype.willUpdateDirty=function(t){!this.dirty&&this._checkModified(t)&&(this.dirty=!0,this._fireEvent(t,"dirty"))},t.prototype.willUpdateModified=function(t){this.modified=this._checkModified(t),this._modified!==this.modified&&(this._fireEvent(t,"modified",{modified:this.modified}),this._modified=this.modified)
},t.prototype.listener=function(t){this.guardValidate(t.target,t.type)||this.handleValidate(t.target,{type:t.type})},t.prototype.handleValidate=function(t){var i=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],e=i.type,n=void 0===e?null:e,a=i.noopable,o=void 0===a?!1:a;
this.willUpdateTouched(t,n),this.willUpdateDirty(t),this.willUpdateModified(t),this._validator.validate({field:this.field,el:t,noopable:o})},t.prototype.validate=function(t){var i=this,e=arguments.length<=1||void 0===arguments[1]?!1:arguments[1],n=arguments.length<=2||void 0===arguments[2]?null:arguments[2],o=A.Vue.util,r={},s=[],l=!0;
this._runValidators(function(t,n,a){var d=i._resolveValidator(n),u=null,h=null;if(o.isPlainObject(d)?(d.check&&"function"==typeof d.check&&(u=d.check),d.message&&(h=d.message)):"function"==typeof d&&(u=d),t.msg&&(h=t.msg),e)return r[n]=!1,a();
if(t._isNoopable)return r[n]=!1,t._isNoopable=null,a();if(u){var c=i._getValue(i._el);i._invokeValidator(i._vm,u,c,t.arg,function(e,o){if(e)r[n]=!e;else if(l=!1,o)s.push({validator:n,message:o}),r[n]=o;
else if(h){var d={validator:n};d.message="function"==typeof h?h.call(i._vm,i.field,t.arg):h,s.push(d),r[n]=d.message}else r[n]=!e;a()})}else a()},function(){i._fireEvent(i._el,l?"valid":"invalid");var e={valid:l,invalid:!l,touched:i.touched,untouched:!i.touched,dirty:i.dirty,pristine:!i.dirty,modified:i.modified};
a(s)||(e.errors=s),o.extend(r,e),i.willUpdateClasses(r,n),t(r)})},t.prototype.resetFlags=function(){this.touched=!1,this.dirty=!1,this.modified=!1,this._modified=!1},t.prototype.reset=function(){this.resetValidationNoopable(),this.resetFlags(),this._init=this._getValue(this._el)
},t.prototype.willUpdateClasses=function(t){var i=this,e=arguments.length<=1||void 0===arguments[1]?null:arguments[1];this._checkClassIds(e)?!function(){var n=i._getClassIds(e);i.vm.$nextTick(function(){i.vm.$emit(S,n,i,t)
})}():this.updateClasses(t)},t.prototype.updateClasses=function(t){var i=arguments.length<=1||void 0===arguments[1]?null:arguments[1];this._updateClasses(i||this._el,t)},t.prototype.guardValidate=function(t,i){return i&&"blur"===i&&!this.detectBlur?!0:i&&"input"===i&&!this.detectChange?!0:i&&"change"===i&&!this.detectChange?!0:i&&"click"===i&&!this.detectChange?!0:!1
},t.prototype._getValue=function(t){return t.value},t.prototype._getScope=function(){return this._forScope||this._vm},t.prototype._getClassIds=function(){return this._classIds},t.prototype._checkModified=function(t){return this._init!==this._getValue(t)
},t.prototype._checkClassIds=function(t){return this._getClassIds(t)},t.prototype._fireEvent=function(t,i,e){s(t,i,e)},t.prototype._evalModel=function(t,i){var e=this._getScope(),n=null;return i?(n=e.$get(t),i?this._applyFilters(n,null,i):n):(n=e.$get(t),void 0===n||null===n?"":n)
},t.prototype._updateClasses=function(t,i){this._toggleValid(t,i.valid),this._toggleTouched(t,i.touched),this._togglePristine(t,i.pristine),this._toggleModfied(t,i.modified)},t.prototype._toggleValid=function(t,i){var e=A.Vue.util,n=e.addClass,a=e.removeClass,o=this._classes.valid||"valid",r=this._classes.invalid||"invalid";
i?(d(t,o,n),d(t,r,a)):(d(t,o,a),d(t,r,n))},t.prototype._toggleTouched=function(t,i){var e=A.Vue.util,n=e.addClass,a=e.removeClass,o=this._classes.touched||"touched",r=this._classes.untouched||"untouched";
i?(d(t,o,n),d(t,r,a)):(d(t,o,a),d(t,r,n))},t.prototype._togglePristine=function(t,i){var e=A.Vue.util,n=e.addClass,a=e.removeClass,o=this._classes.pristine||"pristine",r=this._classes.dirty||"dirty";i?(d(t,o,n),d(t,r,a)):(d(t,o,a),d(t,r,n))
},t.prototype._toggleModfied=function(t,i){var e=A.Vue.util,n=e.addClass,a=e.removeClass,o=this._classes.modified||"modified";i?d(t,o,n):d(t,o,a)},t.prototype._applyFilters=function(t,i,e,n){var a=A.Vue.util.resolveAsset,o=this._getScope(),r=void 0,s=void 0,l=void 0,d=void 0,u=void 0,h=void 0,c=void 0,f=void 0,p=void 0;
for(h=0,c=e.length;c>h;h++)if(r=e[h],s=a(this._vm.$options,"filters",r.name),s&&(s=n?s.write:s.read||s,"function"==typeof s)){if(l=n?[t,i]:[t],u=n?2:1,r.args)for(f=0,p=r.args.length;p>f;f++)d=r.args[f],l[f+u]=d.dynamic?o.$get(d.value):d.value;
t=s.apply(this._vm,l)}return t},t.prototype._runValidators=function(t,i){var e=this._validators,n=Object.keys(e).length,a=0;o(e,function(e,o){t(e,o,function(){++a,a>=n&&i()})})},t.prototype._invokeValidator=function(t,i,e,n,a){var o=i.call(this,e,n);
"function"==typeof o?o(function(){a(!0)},function(t){a(!1,t)}):l(o)?o.then(function(){a(!0)},function(t){a(!1,t)}).catch(function(t){a(!1,t.message)}):a(o)},t.prototype._resolveValidator=function(t){var i=A.Vue.util.resolveAsset;
return i(this._vm.$options,"validators",t)},E.createClass(t,[{key:"vm",get:function(){return this._vm}},{key:"el",get:function(){return this._el}},{key:"detectChange",get:function(){return this._detectChange
},set:function(t){this._detectChange=t}},{key:"detectBlur",get:function(){return this._detectBlur},set:function(t){this._detectBlur=t}}]),t}(),U=function(t){function i(e,n,a,o,r,s,l,d,u){E.classCallCheck(this,i);
var h=E.possibleConstructorReturn(this,t.call(this,e,n,a,o,r,s,l,d,u));return h._inits=[],h}return E.inherits(i,t),i.prototype.manageElement=function(t,i){var e=this,n=this._getScope(),a=this._addItem(t,i),o=a.model=this._model;
if(o){var r=this._evalModel(o,this._filters);Array.isArray(r)?(this._setChecked(r,a.el),a.unwatch=n.$watch(o,function(t,i){if(t!==i){if(e.guardValidate(a.el,"change"))return;e.handleValidate(a.el,{noopable:a.initial}),a.initial&&(a.initial=null)
}})):(t.checked=r||!1,this._init=t.checked,a.init=t.checked,a.value=t.value,a.unwatch=n.$watch(o,function(i,n){if(i!==n){if(e.guardValidate(t,"change"))return;e.handleValidate(t,{noopable:a.initial}),a.initial&&(a.initial=null)
}}))}else{var s={field:this.field,noopable:i};this._checkClassIds(t)&&(s.el=t),this._validator.validate(s)}},i.prototype.unmanageElement=function(t){var i=-1;o(this._inits,function(e,n){e.el===t&&(i=n,e.unwatch&&e.model&&(e.unwatch(),e.unwatch=null,e.model=null))
}),-1!==i&&(this._inits.splice(i,1),this._validator.validate({field:this.field}))},i.prototype.willUpdateFlags=function(){var t=this,i=arguments.length<=0||void 0===arguments[0]?!1:arguments[0];o(this._inits,function(e){i&&t.willUpdateTouched(e.el,"blur"),t.willUpdateDirty(e.el),t.willUpdateModified(e.el)
})},i.prototype.reset=function(){this.resetValidationNoopable(),this.resetFlags(),o(this._inits,function(t){t.init=t.el.checked,t.value=t.el.value})},i.prototype.updateClasses=function(t){var i=this,e=arguments.length<=1||void 0===arguments[1]?null:arguments[1];
e?this._updateClasses(e,t):o(this._inits,function(e){i._updateClasses(e.el,t)})},i.prototype._addItem=function(t,i){var e={el:t,init:t.checked,value:t.value,initial:i},n=t.getAttribute(S);return n&&(t.removeAttribute(S),e.classIds=n.split(",")),this._inits.push(e),e
},i.prototype._setChecked=function(t,i){for(var e=0,n=t.length;n>e;e++){var a=t[e];i.disabled||i.value!==a||i.checked||(i.checked=!0)}},i.prototype._getValue=function(t){var i=this;if(!this._inits||0===this._inits.length)return t.checked;
var e=function(){var t=[];return o(i._inits,function(i){i.el.checked&&t.push(i.el.value)}),{v:t}}();return"object"===("undefined"==typeof e?"undefined":E.typeof(e))?e.v:void 0},i.prototype._getClassIds=function(t){var i=void 0;
return o(this._inits,function(e){e.el===t&&(i=e.classIds)}),i},i.prototype._checkModified=function(t){var i=this;if(0===this._inits.length)return this._init!==t.checked;var e=function(){var t=!1;return o(i._inits,function(i){t||(t=i.init!==i.el.checked)
}),{v:t}}();return"object"===("undefined"==typeof e?"undefined":E.typeof(e))?e.v:void 0},i}(j),B=function(t){function i(e,n,a,o,r,s,l,d,u){E.classCallCheck(this,i);var h=E.possibleConstructorReturn(this,t.call(this,e,n,a,o,r,s,l,d,u));
return h._inits=[],h}return E.inherits(i,t),i.prototype.manageElement=function(t,i){var e=this,n=this._getScope(),a=this._addItem(t,i),o=a.model=this._model;if(o){var r=this._evalModel(o,this._filters);
this._setChecked(r,t,a),a.unwatch=n.$watch(o,function(i,n){if(i!==n){if(e.guardValidate(a.el,"change"))return;e.handleValidate(t,{noopable:a.initial}),a.initial&&(a.initial=null)}})}else{var s={field:this.field,noopable:i};
this._checkClassIds(t)&&(s.el=t),this._validator.validate(s)}},i.prototype.unmanageElement=function(t){var i=-1;o(this._inits,function(e,n){e.el===t&&(i=n)}),-1!==i&&(this._inits.splice(i,1),this._validator.validate({field:this.field}))
},i.prototype.willUpdateFlags=function(){var t=this,i=arguments.length<=0||void 0===arguments[0]?!1:arguments[0];o(this._inits,function(e){i&&t.willUpdateTouched(e.el,"blur"),t.willUpdateDirty(e.el),t.willUpdateModified(e.el)
})},i.prototype.reset=function(){this.resetValidationNoopable(),this.resetFlags(),o(this._inits,function(t){t.init=t.el.checked,t.value=t.el.value})},i.prototype.updateClasses=function(t){var i=this,e=arguments.length<=1||void 0===arguments[1]?null:arguments[1];
e?this._updateClasses(e,t):o(this._inits,function(e){i._updateClasses(e.el,t)})},i.prototype._addItem=function(t,i){var e={el:t,init:t.checked,value:t.value,initial:i},n=t.getAttribute(S);return n&&(t.removeAttribute(S),e.classIds=n.split(",")),this._inits.push(e),e
},i.prototype._setChecked=function(t,i,e){i.value===t&&(i.checked=!0,this._init=i.checked,e.init=i.checked,e.value=t)},i.prototype._getValue=function(t){var i=this;if(!this._inits||0===this._inits.length)return t.checked;
var e=function(){var t=[];return o(i._inits,function(i){i.el.checked&&t.push(i.el.value)}),{v:t}}();return"object"===("undefined"==typeof e?"undefined":E.typeof(e))?e.v:void 0},i.prototype._getClassIds=function(t){var i=void 0;
return o(this._inits,function(e){e.el===t&&(i=e.classIds)}),i},i.prototype._checkModified=function(t){var i=this;if(0===this._inits.length)return this._init!==t.checked;var e=function(){var t=!1;return o(i._inits,function(i){t||(t=i.init!==i.el.checked)
}),{v:t}}();return"object"===("undefined"==typeof e?"undefined":E.typeof(e))?e.v:void 0},i}(j),R=function(t){function i(e,n,a,o,r,s,l,d,u){E.classCallCheck(this,i);var h=E.possibleConstructorReturn(this,t.call(this,e,n,a,o,r,s,l,d,u));
return h._multiple=h._el.hasAttribute("multiple"),h}return E.inherits(i,t),i.prototype.manageElement=function(t,i){var e=this,n=this._getScope(),a=this._model;this._initial=i;var o=t.getAttribute(S);if(o&&(t.removeAttribute(S),this._classIds=o.split(",")),a){var r=this._evalModel(a,this._filters),s=Array.isArray(r)?r:[r];
this._setOption(s,t),this._unwatch=n.$watch(a,function(i,n){var a=Array.isArray(i)?i:[i],o=Array.isArray(n)?n:[n];if(a.slice().sort().toString()!==o.slice().sort().toString()){if(e.guardValidate(t,"change"))return;
e.handleValidate(t,{noopable:e._initial}),e._initial&&(e._initial=null)}})}},i.prototype.unmanageElement=function(){this._unwatch&&this._unwatch()},i.prototype._getValue=function(t){for(var i=[],e=0,n=t.options.length;n>e;e++){var a=t.options[e];
!a.disabled&&a.selected&&i.push(a.value)}return i},i.prototype._setOption=function(t,i){for(var e=0,n=t.length;n>e;e++)for(var a=t[e],o=0,r=i.options.length;r>o;o++){var s=i.options[o];s.disabled||s.value!==a||s.hasAttribute("selected")&&s.selected||(s.selected=!0)
}},i.prototype._checkModified=function(t){var i=this._getValue(t).slice().sort();if(this._init.length!==i.length)return!0;var e=this._init.slice().sort();return e.toString()!==i.toString()},i}(j),T=function(){function t(i,e,n,a){var r=this;
E.classCallCheck(this,t),this.name=i,this._scope={},this._dir=e,this._validations={},this._checkboxValidations={},this._radioValidations={},this._groups=n,this._groupValidations={},this._events={},this._modified=!1,this._classes=a,o(n,function(t){r._groupValidations[t]=[]
})}return t.prototype.enableReactive=function(){var t=this._dir.vm;A.Vue.util.defineReactive(t,this.name,this._scope),t._validatorMaps[this.name]=this,this._defineResetValidation(),this._defineValidate(),this._defineSetValidationErrors()
},t.prototype.disableReactive=function(){var t=this._dir.vm;t.$setValidationErrors=null,delete t.$setValidationErrors,t.$validate=null,delete t.$validate,t.$resetValidation=null,delete t.$resetValidation,t._validatorMaps[this.name]=null,delete t._validatorMaps[this.name],t[this.name]=null,delete t[this.name]
},t.prototype.registerEvents=function(){for(var t=A.Vue.parsers.expression.isSimplePath,i=this._dir.el.attributes,e=0,n=i.length;n>e;e++){var a=i[e].name;if($.test(a)){var o=i[e].value;t(o)&&(o+=".apply(this, $arguments)"),a=a.replace($,""),this._events[this._getEventName(a)]=this._dir.vm.$eval(o,!0)
}}},t.prototype.unregisterEvents=function(){var t=this;o(this._events,function(i,e){t._events[e]=null,delete t._events[e]})},t.prototype.manageValidation=function(t,i,e,n,a,o,r,s,l){var d=null;return d="SELECT"===n.tagName?this._manageSelectValidation(t,i,e,n,a,o,r,s,l):"checkbox"===n.type?this._manageCheckboxValidation(t,i,e,n,a,o,r,s,l):"radio"===n.type?this._manageRadioValidation(t,i,e,n,a,o,r,s,l):this._manageBaseValidation(t,i,e,n,a,o,r,s,l),d.setValidationClasses(this._classes),d
},t.prototype.unmanageValidation=function(t,i){"checkbox"===i.type?this._unmanageCheckboxValidation(t,i):"radio"===i.type?this._unmanageRadioValidation(t,i):"SELECT"===i.tagName?this._unmanageSelectValidation(t,i):this._unmanageBaseValidation(t,i)
},t.prototype.addGroupValidation=function(t,i){var e=A.Vue.util.indexOf,n=this._getValidationFrom(i),a=this._groupValidations[t];a&&!~e(a,n)&&a.push(n)},t.prototype.removeGroupValidation=function(t,i){var e=this._getValidationFrom(i),n=this._groupValidations[t];
n&&r(n,e)},t.prototype.validate=function(){var t=arguments.length<=0||void 0===arguments[0]?{}:arguments[0],i=t.el,e=void 0===i?null:i,n=t.field,a=void 0===n?null:n,r=t.touched,s=void 0===r?!1:r,l=t.noopable,d=void 0===l?!1:l,u=t.cb,h=void 0===u?null:u;
a?this._validate(a,s,d,e,h):(o(this.validations,function(t){t.willUpdateFlags(s)}),this._validates(h))},t.prototype.setupScope=function(){var t=this;this._defineProperties(function(){return t.validations
},function(){return t._scope}),o(this._groups,function(i){var e=t._groupValidations[i],n={};A.Vue.set(t._scope,i,n),t._defineProperties(function(){return e},function(){return n})})},t.prototype.waitFor=function(t){var i="$activateValidator",e=this._dir.vm;
e[i]=function(){t(),e[i]=null}},t.prototype._defineResetValidation=function(){var t=this;this._dir.vm.$resetValidation=function(i){t._resetValidation(i)}},t.prototype._defineValidate=function(){var t=this;
this._dir.vm.$validate=function(){for(var i=arguments.length,e=Array(i),n=0;i>n;n++)e[n]=arguments[n];var a=null,r=!1,s=null;o(e,function(t){"string"==typeof t?a=t:"boolean"==typeof t?r=t:"function"==typeof t&&(s=t)
}),t.validate({field:a,touched:r,cb:s})}},t.prototype._defineSetValidationErrors=function(){var t=this;this._dir.vm.$setValidationErrors=function(i){t._setValidationErrors(i)}},t.prototype._validate=function(t){var i=arguments.length<=1||void 0===arguments[1]?!1:arguments[1],e=arguments.length<=2||void 0===arguments[2]?!1:arguments[2],n=this,a=arguments.length<=3||void 0===arguments[3]?null:arguments[3],o=arguments.length<=4||void 0===arguments[4]?null:arguments[4],r=this._scope,s=this._getValidationFrom(t);
s&&(s.willUpdateFlags(i),s.validate(function(i){A.Vue.set(r,t,i),n._fireEvents(),o&&o()},e,a))},t.prototype._validates=function(t){var i=this,e=this._scope;this._runValidates(function(t,i,n){t.validate(function(t){A.Vue.set(e,i,t),n()
})},function(){i._fireEvents(),t&&t()})},t.prototype._getValidationFrom=function(t){return this._validations[t]||this._checkboxValidations[t]&&this._checkboxValidations[t].validation||this._radioValidations[t]&&this._radioValidations[t].validation
},t.prototype._resetValidation=function(t){o(this.validations,function(t){t.reset()}),this._validates(t)},t.prototype._setValidationErrors=function(t){var i=this,e=A.Vue.util.extend,n={};o(t,function(t){n[t.field]||(n[t.field]=[]),n[t.field].push(t)
}),o(n,function(t,n){var a=i._scope[n],r={};o(t,function(t){t.validator&&(a[t.validator]=t.message)}),a.valid=!1,a.invalid=!0,a.errors=t,e(r,a);var s=i._getValidationFrom(n);s.willUpdateClasses(r,s.el),A.Vue.set(i._scope,n,r)
})},t.prototype._manageBaseValidation=function(t,i,e,n,a,o,r,s,l){var d=this._validations[t]=new j(t,i,e,n,a,this,o,s,l);return d.manageElement(n,r),d},t.prototype._unmanageBaseValidation=function(t,i){var e=this._validations[t];
e&&(e.unmanageElement(i),A.Vue.delete(this._scope,t),this._validations[t]=null,delete this._validations[t])},t.prototype._manageCheckboxValidation=function(t,i,e,n,a,o,r,s,l){var d=this._checkboxValidations[t];
if(!d){var u=new U(t,i,e,n,a,this,o,s,l);d={validation:u,elements:0},this._checkboxValidations[t]=d}return d.elements++,d.validation.manageElement(n,r),d.validation},t.prototype._unmanageCheckboxValidation=function(t,i){var e=this._checkboxValidations[t];
e&&(e.elements--,e.validation.unmanageElement(i),0===e.elements&&(A.Vue.delete(this._scope,t),this._checkboxValidations[t]=null,delete this._checkboxValidations[t]))},t.prototype._manageRadioValidation=function(t,i,e,n,a,o,r,s,l){var d=this._radioValidations[t];
if(!d){var u=new B(t,i,e,n,a,this,o,s,l);d={validation:u,elements:0},this._radioValidations[t]=d}return d.elements++,d.validation.manageElement(n,r),d.validation},t.prototype._unmanageRadioValidation=function(t,i){var e=this._radioValidations[t];
e&&(e.elements--,e.validation.unmanageElement(i),0===e.elements&&(A.Vue.delete(this._scope,t),this._radioValidations[t]=null,delete this._radioValidations[t]))},t.prototype._manageSelectValidation=function(t,i,e,n,a,o,r,s,l){var d=this._validations[t]=new R(t,i,e,n,a,this,o,s,l);
return d.manageElement(n,r),d},t.prototype._unmanageSelectValidation=function(t,i){var e=this._validations[t];e&&(e.unmanageElement(i),A.Vue.delete(this._scope,t),this._validations[t]=null,delete this._validations[t])
},t.prototype._fireEvent=function(t){for(var i=arguments.length,e=Array(i>1?i-1:0),n=1;i>n;n++)e[n-1]=arguments[n];var a=this._events[this._getEventName(t)];a&&this._dir.vm.$nextTick(function(){a.apply(null,e)
})},t.prototype._fireEvents=function(){var t=this._scope;t.touched&&this._fireEvent("touched"),t.dirty&&this._fireEvent("dirty"),this._modified!==t.modified&&(this._fireEvent("modified",t.modified),this._modified=t.modified);
var i=t.valid;this._fireEvent(i?"valid":"invalid")},t.prototype._getEventName=function(t){return this.name+":"+t},t.prototype._defineProperties=function(t,i){var e=this,n=A.Vue.util.bind;o({valid:{fn:this._defineValid,arg:t},invalid:{fn:this._defineInvalid,arg:i},touched:{fn:this._defineTouched,arg:t},untouched:{fn:this._defineUntouched,arg:i},modified:{fn:this._defineModified,arg:t},dirty:{fn:this._defineDirty,arg:t},pristine:{fn:this._definePristine,arg:i},errors:{fn:this._defineErrors,arg:t}},function(t,a){Object.defineProperty(i(),a,{enumerable:!0,configurable:!0,get:function(){return n(t.fn,e)(t.arg)
}})})},t.prototype._runValidates=function(t,i){var e=Object.keys(this.validations).length,n=0;o(this.validations,function(a,o){t(a,o,function(){++n,n>=e&&i()})})},t.prototype._walkValidations=function(t,i,e){var n=this,a=A.Vue.util.hasOwn,r=e;
return o(t,function(t){if(r!==!e&&a(n._scope,t.field)){var o=n._scope[t.field];o&&o[i]===!e&&(r=!e)}}),r},t.prototype._defineValid=function(t){return this._walkValidations(t(),"valid",!0)},t.prototype._defineInvalid=function(t){return!t().valid
},t.prototype._defineTouched=function(t){return this._walkValidations(t(),"touched",!1)},t.prototype._defineUntouched=function(t){return!t().touched},t.prototype._defineModified=function(t){return this._walkValidations(t(),"modified",!1)
},t.prototype._defineDirty=function(t){return this._walkValidations(t(),"dirty",!1)},t.prototype._definePristine=function(t){return!t().dirty},t.prototype._defineErrors=function(t){var i=this,e=A.Vue.util.hasOwn,n=A.Vue.util.isPlainObject,r=[];
return o(t(),function(t){if(e(i._scope,t.field)){var s=i._scope[t.field];s&&!a(s.errors)&&o(s.errors,function(i){var e={field:t.field};n(i)?(i.validator&&(e.validator=i.validator),e.message=i.message):"string"==typeof i&&(e.message=i),r.push(e)
})}}),a(r)?void 0:r.sort(function(t,i){return t.field<i.field?-1:1})},E.createClass(t,[{key:"validations",get:function(){var t=A.Vue.util.extend,i={};return t(i,this._validations),o(this._checkboxValidations,function(t,e){i[e]=t.validation
}),o(this._radioValidations,function(t,e){i[e]=t.validation}),i}}]),t}();k.version="2.1.7","undefined"!=typeof window&&window.Vue&&window.Vue.use(k),e.exports=k});
;define("hiloan:node_modules/iscroll/build/iscroll",function(t,i,s){!function(t,i,e){function o(s,e){this.wrapper="string"==typeof s?i.querySelector(s):s,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={resizeScrollbars:!0,mouseWheelSpeed:20,snapThreshold:.334,disablePointer:!a.hasPointer,disableTouch:a.hasPointer||!a.hasTouch,disableMouse:a.hasPointer||a.hasTouch,startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0,bindToWrapper:"undefined"==typeof t.onmousedown};
for(var o in e)this.options[o]=e[o];this.translateZ=this.options.HWCompositing&&a.hasPerspective?" translateZ(0)":"",this.options.useTransition=a.hasTransition&&this.options.useTransition,this.options.useTransform=a.hasTransform&&this.options.useTransform,this.options.eventPassthrough=this.options.eventPassthrough===!0?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY="vertical"==this.options.eventPassthrough?!1:this.options.scrollY,this.options.scrollX="horizontal"==this.options.eventPassthrough?!1:this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing="string"==typeof this.options.bounceEasing?a.ease[this.options.bounceEasing]||a.ease.circular:this.options.bounceEasing,this.options.resizePolling=void 0===this.options.resizePolling?60:this.options.resizePolling,this.options.tap===!0&&(this.options.tap="tap"),"scale"==this.options.shrinkScrollbars&&(this.options.useTransition=!1),this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1,this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()
}function n(t,s,e){var o=i.createElement("div"),n=i.createElement("div");return e===!0&&(o.style.cssText="position:absolute;z-index:9999",n.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);border-radius:3px"),n.className="iScrollIndicator","h"==t?(e===!0&&(o.style.cssText+=";height:7px;left:2px;right:2px;bottom:0",n.style.height="100%"),o.className="iScrollHorizontalScrollbar"):(e===!0&&(o.style.cssText+=";width:7px;bottom:2px;top:2px;right:1px",n.style.width="100%"),o.className="iScrollVerticalScrollbar"),o.style.cssText+=";overflow:hidden",s||(o.style.pointerEvents="none"),o.appendChild(n),o
}function r(s,e){this.wrapper="string"==typeof e.el?i.querySelector(e.el):e.el,this.wrapperStyle=this.wrapper.style,this.indicator=this.wrapper.children[0],this.indicatorStyle=this.indicator.style,this.scroller=s,this.options={listenX:!0,listenY:!0,interactive:!1,resize:!0,defaultScrollbars:!1,shrink:!1,fade:!1,speedRatioX:0,speedRatioY:0};
for(var o in e)this.options[o]=e[o];if(this.sizeRatioX=1,this.sizeRatioY=1,this.maxPosX=0,this.maxPosY=0,this.options.interactive&&(this.options.disableTouch||(a.addEvent(this.indicator,"touchstart",this),a.addEvent(t,"touchend",this)),this.options.disablePointer||(a.addEvent(this.indicator,a.prefixPointerEvent("pointerdown"),this),a.addEvent(t,a.prefixPointerEvent("pointerup"),this)),this.options.disableMouse||(a.addEvent(this.indicator,"mousedown",this),a.addEvent(t,"mouseup",this))),this.options.fade){this.wrapperStyle[a.style.transform]=this.scroller.translateZ;
var n=a.style.transitionDuration;this.wrapperStyle[n]=a.isBadAndroid?"0.0001ms":"0ms";var r=this;a.isBadAndroid&&h(function(){"0.0001ms"===r.wrapperStyle[n]&&(r.wrapperStyle[n]="0s")}),this.wrapperStyle.opacity="0"
}}var h=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(i){t.setTimeout(i,1e3/60)},a=function(){function s(t){return r===!1?!1:""===r?t:r+t.charAt(0).toUpperCase()+t.substr(1)
}var o={},n=i.createElement("div").style,r=function(){for(var t,i=["t","webkitT","MozT","msT","OT"],s=0,e=i.length;e>s;s++)if(t=i[s]+"ransform",t in n)return i[s].substr(0,i[s].length-1);return!1}();o.getTime=Date.now||function(){return(new Date).getTime()
},o.extend=function(t,i){for(var s in i)t[s]=i[s]},o.addEvent=function(t,i,s,e){t.addEventListener(i,s,!!e)},o.removeEvent=function(t,i,s,e){t.removeEventListener(i,s,!!e)},o.prefixPointerEvent=function(i){return t.MSPointerEvent?"MSPointer"+i.charAt(7).toUpperCase()+i.substr(8):i
},o.momentum=function(t,i,s,o,n,r){var h,a,l=t-i,c=e.abs(l)/s;return r=void 0===r?6e-4:r,h=t+c*c/(2*r)*(0>l?-1:1),a=c/r,o>h?(h=n?o-n/2.5*(c/8):o,l=e.abs(h-t),a=l/c):h>0&&(h=n?n/2.5*(c/8):0,l=e.abs(t)+h,a=l/c),{destination:e.round(h),duration:a}
};var h=s("transform");return o.extend(o,{hasTransform:h!==!1,hasPerspective:s("perspective")in n,hasTouch:"ontouchstart"in t,hasPointer:!(!t.PointerEvent&&!t.MSPointerEvent),hasTransition:s("transition")in n}),o.isBadAndroid=function(){var i=t.navigator.appVersion;
if(/Android/.test(i)&&!/Chrome\/\d/.test(i)){var s=i.match(/Safari\/(\d+.\d)/);return s&&"object"==typeof s&&s.length>=2?parseFloat(s[1])<535.19:!0}return!1}(),o.extend(o.style={},{transform:h,transitionTimingFunction:s("transitionTimingFunction"),transitionDuration:s("transitionDuration"),transitionDelay:s("transitionDelay"),transformOrigin:s("transformOrigin")}),o.hasClass=function(t,i){var s=new RegExp("(^|\\s)"+i+"(\\s|$)");
return s.test(t.className)},o.addClass=function(t,i){if(!o.hasClass(t,i)){var s=t.className.split(" ");s.push(i),t.className=s.join(" ")}},o.removeClass=function(t,i){if(o.hasClass(t,i)){var s=new RegExp("(^|\\s)"+i+"(\\s|$)","g");
t.className=t.className.replace(s," ")}},o.offset=function(t){for(var i=-t.offsetLeft,s=-t.offsetTop;t=t.offsetParent;)i-=t.offsetLeft,s-=t.offsetTop;return{left:i,top:s}},o.preventDefaultException=function(t,i){for(var s in i)if(i[s].test(t[s]))return!0;
return!1},o.extend(o.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),o.extend(o.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)
}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(t){return e.sqrt(1- --t*t)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(t){var i=4;return(t-=1)*t*((i+1)*t+i)+1}},bounce:{style:"",fn:function(t){return(t/=1)<1/2.75?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375
}},elastic:{style:"",fn:function(t){var i=.22,s=.4;return 0===t?0:1==t?1:s*e.pow(2,-10*t)*e.sin(2*(t-i/4)*e.PI/i)+1}}}),o.tap=function(t,s){var e=i.createEvent("Event");e.initEvent(s,!0,!0),e.pageX=t.pageX,e.pageY=t.pageY,t.target.dispatchEvent(e)
},o.click=function(t){var s,e=t.target;/(SELECT|INPUT|TEXTAREA)/i.test(e.tagName)||(s=i.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!0,t.view,1,e.screenX,e.screenY,e.clientX,e.clientY,t.ctrlKey,t.altKey,t.shiftKey,t.metaKey,0,null),s._constructed=!0,e.dispatchEvent(s))
},o}();o.prototype={version:"5.2.0",_init:function(){this._initEvents(),(this.options.scrollbars||this.options.indicators)&&this._initIndicators(),this.options.mouseWheel&&this._initWheel(),this.options.snap&&this._initSnap(),this.options.keyBindings&&this._initKeys()
},destroy:function(){this._initEvents(!0),clearTimeout(this.resizeTimeout),this.resizeTimeout=null,this._execEvent("destroy")},_transitionEnd:function(t){t.target==this.scroller&&this.isInTransition&&(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))
},_start:function(t){if(1!=a.eventType[t.type]){var i;if(i=t.which?t.button:t.button<2?0:4==t.button?1:2,0!==i)return}if(this.enabled&&(!this.initiated||a.eventType[t.type]===this.initiated)){!this.options.preventDefault||a.isBadAndroid||a.preventDefaultException(t.target,this.options.preventDefaultException)||t.preventDefault();
var s,o=t.touches?t.touches[0]:t;this.initiated=a.eventType[t.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this.startTime=a.getTime(),this.options.useTransition&&this.isInTransition?(this._transitionTime(),this.isInTransition=!1,s=this.getComputedPosition(),this._translate(e.round(s.x),e.round(s.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=o.pageX,this.pointY=o.pageY,this._execEvent("beforeScrollStart")
}},_move:function(t){if(this.enabled&&a.eventType[t.type]===this.initiated){this.options.preventDefault&&t.preventDefault();var i,s,o,n,r=t.touches?t.touches[0]:t,h=r.pageX-this.pointX,l=r.pageY-this.pointY,c=a.getTime();
if(this.pointX=r.pageX,this.pointY=r.pageY,this.distX+=h,this.distY+=l,o=e.abs(this.distX),n=e.abs(this.distY),!(c-this.endTime>300&&10>o&&10>n)){if(this.directionLocked||this.options.freeScroll||(this.directionLocked=o>n+this.options.directionLockThreshold?"h":n>=o+this.options.directionLockThreshold?"v":"n"),"h"==this.directionLocked){if("vertical"==this.options.eventPassthrough)t.preventDefault();
else if("horizontal"==this.options.eventPassthrough)return void(this.initiated=!1);l=0}else if("v"==this.directionLocked){if("horizontal"==this.options.eventPassthrough)t.preventDefault();else if("vertical"==this.options.eventPassthrough)return void(this.initiated=!1);
h=0}h=this.hasHorizontalScroll?h:0,l=this.hasVerticalScroll?l:0,i=this.x+h,s=this.y+l,(i>0||i<this.maxScrollX)&&(i=this.options.bounce?this.x+h/3:i>0?0:this.maxScrollX),(s>0||s<this.maxScrollY)&&(s=this.options.bounce?this.y+l/3:s>0?0:this.maxScrollY),this.directionX=h>0?-1:0>h?1:0,this.directionY=l>0?-1:0>l?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(i,s),c-this.startTime>300&&(this.startTime=c,this.startX=this.x,this.startY=this.y)
}}},_end:function(t){if(this.enabled&&a.eventType[t.type]===this.initiated){this.options.preventDefault&&!a.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();var i,s,o=(t.changedTouches?t.changedTouches[0]:t,a.getTime()-this.startTime),n=e.round(this.x),r=e.round(this.y),h=e.abs(n-this.startX),l=e.abs(r-this.startY),c=0,p="";
if(this.isInTransition=0,this.initiated=0,this.endTime=a.getTime(),!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(n,r),!this.moved)return this.options.tap&&a.tap(t,this.options.tap),this.options.click&&a.click(t),void this._execEvent("scrollCancel");
if(this._events.flick&&200>o&&100>h&&100>l)return void this._execEvent("flick");if(this.options.momentum&&300>o&&(i=this.hasHorizontalScroll?a.momentum(this.x,this.startX,o,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:n,duration:0},s=this.hasVerticalScroll?a.momentum(this.y,this.startY,o,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:r,duration:0},n=i.destination,r=s.destination,c=e.max(i.duration,s.duration),this.isInTransition=1),this.options.snap){var d=this._nearestSnap(n,r);
this.currentPage=d,c=this.options.snapSpeed||e.max(e.max(e.min(e.abs(n-d.x),1e3),e.min(e.abs(r-d.y),1e3)),300),n=d.x,r=d.y,this.directionX=0,this.directionY=0,p=this.options.bounceEasing}return n!=this.x||r!=this.y?((n>0||n<this.maxScrollX||r>0||r<this.maxScrollY)&&(p=a.ease.quadratic),void this.scrollTo(n,r,c,p)):void this._execEvent("scrollEnd")
}}},_resize:function(){var t=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t.refresh()},this.options.resizePolling)},resetPosition:function(t){var i=this.x,s=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?i=0:this.x<this.maxScrollX&&(i=this.maxScrollX),!this.hasVerticalScroll||this.y>0?s=0:this.y<this.maxScrollY&&(s=this.maxScrollY),i==this.x&&s==this.y?!1:(this.scrollTo(i,s,t,this.options.bounceEasing),!0)
},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){this.wrapper.offsetHeight;this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight,this.scrollerWidth=this.scroller.offsetWidth,this.scrollerHeight=this.scroller.offsetHeight,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,this.wrapperOffset=a.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()
},on:function(t,i){this._events[t]||(this._events[t]=[]),this._events[t].push(i)},off:function(t,i){if(this._events[t]){var s=this._events[t].indexOf(i);s>-1&&this._events[t].splice(s,1)}},_execEvent:function(t){if(this._events[t]){var i=0,s=this._events[t].length;
if(s)for(;s>i;i++)this._events[t][i].apply(this,[].slice.call(arguments,1))}},scrollBy:function(t,i,s,e){t=this.x+t,i=this.y+i,s=s||0,this.scrollTo(t,i,s,e)},scrollTo:function(t,i,s,e){e=e||a.ease.circular,this.isInTransition=this.options.useTransition&&s>0;
var o=this.options.useTransition&&e.style;!s||o?(o&&(this._transitionTimingFunction(e.style),this._transitionTime(s)),this._translate(t,i)):this._animate(t,i,s,e.fn)},scrollToElement:function(t,i,s,o,n){if(t=t.nodeType?t:this.scroller.querySelector(t)){var r=a.offset(t);
r.left-=this.wrapperOffset.left,r.top-=this.wrapperOffset.top,s===!0&&(s=e.round(t.offsetWidth/2-this.wrapper.offsetWidth/2)),o===!0&&(o=e.round(t.offsetHeight/2-this.wrapper.offsetHeight/2)),r.left-=s||0,r.top-=o||0,r.left=r.left>0?0:r.left<this.maxScrollX?this.maxScrollX:r.left,r.top=r.top>0?0:r.top<this.maxScrollY?this.maxScrollY:r.top,i=void 0===i||null===i||"auto"===i?e.max(e.abs(this.x-r.left),e.abs(this.y-r.top)):i,this.scrollTo(r.left,r.top,i,n)
}},_transitionTime:function(t){t=t||0;var i=a.style.transitionDuration;if(this.scrollerStyle[i]=t+"ms",!t&&a.isBadAndroid){this.scrollerStyle[i]="0.0001ms";var s=this;h(function(){"0.0001ms"===s.scrollerStyle[i]&&(s.scrollerStyle[i]="0s")
})}if(this.indicators)for(var e=this.indicators.length;e--;)this.indicators[e].transitionTime(t)},_transitionTimingFunction:function(t){if(this.scrollerStyle[a.style.transitionTimingFunction]=t,this.indicators)for(var i=this.indicators.length;i--;)this.indicators[i].transitionTimingFunction(t)
},_translate:function(t,i){if(this.options.useTransform?this.scrollerStyle[a.style.transform]="translate("+t+"px,"+i+"px)"+this.translateZ:(t=e.round(t),i=e.round(i),this.scrollerStyle.left=t+"px",this.scrollerStyle.top=i+"px"),this.x=t,this.y=i,this.indicators)for(var s=this.indicators.length;s--;)this.indicators[s].updatePosition()
},_initEvents:function(i){var s=i?a.removeEvent:a.addEvent,e=this.options.bindToWrapper?this.wrapper:t;s(t,"orientationchange",this),s(t,"resize",this),this.options.click&&s(this.wrapper,"click",this,!0),this.options.disableMouse||(s(this.wrapper,"mousedown",this),s(e,"mousemove",this),s(e,"mousecancel",this),s(e,"mouseup",this)),a.hasPointer&&!this.options.disablePointer&&(s(this.wrapper,a.prefixPointerEvent("pointerdown"),this),s(e,a.prefixPointerEvent("pointermove"),this),s(e,a.prefixPointerEvent("pointercancel"),this),s(e,a.prefixPointerEvent("pointerup"),this)),a.hasTouch&&!this.options.disableTouch&&(s(this.wrapper,"touchstart",this),s(e,"touchmove",this),s(e,"touchcancel",this),s(e,"touchend",this)),s(this.scroller,"transitionend",this),s(this.scroller,"webkitTransitionEnd",this),s(this.scroller,"oTransitionEnd",this),s(this.scroller,"MSTransitionEnd",this)
},getComputedPosition:function(){var i,s,e=t.getComputedStyle(this.scroller,null);return this.options.useTransform?(e=e[a.style.transform].split(")")[0].split(", "),i=+(e[12]||e[4]),s=+(e[13]||e[5])):(i=+e.left.replace(/[^-\d.]/g,""),s=+e.top.replace(/[^-\d.]/g,"")),{x:i,y:s}
},_initIndicators:function(){function t(t){if(h.indicators)for(var i=h.indicators.length;i--;)t.call(h.indicators[i])}var i,s=this.options.interactiveScrollbars,e="string"!=typeof this.options.scrollbars,o=[],h=this;
this.indicators=[],this.options.scrollbars&&(this.options.scrollY&&(i={el:n("v",s,this.options.scrollbars),interactive:s,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenX:!1},this.wrapper.appendChild(i.el),o.push(i)),this.options.scrollX&&(i={el:n("h",s,this.options.scrollbars),interactive:s,defaultScrollbars:!0,customStyle:e,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenY:!1},this.wrapper.appendChild(i.el),o.push(i))),this.options.indicators&&(o=o.concat(this.options.indicators));
for(var a=o.length;a--;)this.indicators.push(new r(this,o[a]));this.options.fadeScrollbars&&(this.on("scrollEnd",function(){t(function(){this.fade()})}),this.on("scrollCancel",function(){t(function(){this.fade()
})}),this.on("scrollStart",function(){t(function(){this.fade(1)})}),this.on("beforeScrollStart",function(){t(function(){this.fade(1,!0)})})),this.on("refresh",function(){t(function(){this.refresh()})}),this.on("destroy",function(){t(function(){this.destroy()
}),delete this.indicators})},_initWheel:function(){a.addEvent(this.wrapper,"wheel",this),a.addEvent(this.wrapper,"mousewheel",this),a.addEvent(this.wrapper,"DOMMouseScroll",this),this.on("destroy",function(){clearTimeout(this.wheelTimeout),this.wheelTimeout=null,a.removeEvent(this.wrapper,"wheel",this),a.removeEvent(this.wrapper,"mousewheel",this),a.removeEvent(this.wrapper,"DOMMouseScroll",this)
})},_wheel:function(t){if(this.enabled){t.preventDefault();var i,s,o,n,r=this;if(void 0===this.wheelTimeout&&r._execEvent("scrollStart"),clearTimeout(this.wheelTimeout),this.wheelTimeout=setTimeout(function(){r.options.snap||r._execEvent("scrollEnd"),r.wheelTimeout=void 0
},400),"deltaX"in t)1===t.deltaMode?(i=-t.deltaX*this.options.mouseWheelSpeed,s=-t.deltaY*this.options.mouseWheelSpeed):(i=-t.deltaX,s=-t.deltaY);else if("wheelDeltaX"in t)i=t.wheelDeltaX/120*this.options.mouseWheelSpeed,s=t.wheelDeltaY/120*this.options.mouseWheelSpeed;
else if("wheelDelta"in t)i=s=t.wheelDelta/120*this.options.mouseWheelSpeed;else{if(!("detail"in t))return;i=s=-t.detail/3*this.options.mouseWheelSpeed}if(i*=this.options.invertWheelDirection,s*=this.options.invertWheelDirection,this.hasVerticalScroll||(i=s,s=0),this.options.snap)return o=this.currentPage.pageX,n=this.currentPage.pageY,i>0?o--:0>i&&o++,s>0?n--:0>s&&n++,void this.goToPage(o,n);
o=this.x+e.round(this.hasHorizontalScroll?i:0),n=this.y+e.round(this.hasVerticalScroll?s:0),this.directionX=i>0?-1:0>i?1:0,this.directionY=s>0?-1:0>s?1:0,o>0?o=0:o<this.maxScrollX&&(o=this.maxScrollX),n>0?n=0:n<this.maxScrollY&&(n=this.maxScrollY),this.scrollTo(o,n,0)
}},_initSnap:function(){this.currentPage={},"string"==typeof this.options.snap&&(this.options.snap=this.scroller.querySelectorAll(this.options.snap)),this.on("refresh",function(){var t,i,s,o,n,r,h=0,a=0,l=0,c=this.options.snapStepX||this.wrapperWidth,p=this.options.snapStepY||this.wrapperHeight;
if(this.pages=[],this.wrapperWidth&&this.wrapperHeight&&this.scrollerWidth&&this.scrollerHeight){if(this.options.snap===!0)for(s=e.round(c/2),o=e.round(p/2);l>-this.scrollerWidth;){for(this.pages[h]=[],t=0,n=0;n>-this.scrollerHeight;)this.pages[h][t]={x:e.max(l,this.maxScrollX),y:e.max(n,this.maxScrollY),width:c,height:p,cx:l-s,cy:n-o},n-=p,t++;
l-=c,h++}else for(r=this.options.snap,t=r.length,i=-1;t>h;h++)(0===h||r[h].offsetLeft<=r[h-1].offsetLeft)&&(a=0,i++),this.pages[a]||(this.pages[a]=[]),l=e.max(-r[h].offsetLeft,this.maxScrollX),n=e.max(-r[h].offsetTop,this.maxScrollY),s=l-e.round(r[h].offsetWidth/2),o=n-e.round(r[h].offsetHeight/2),this.pages[a][i]={x:l,y:n,width:r[h].offsetWidth,height:r[h].offsetHeight,cx:s,cy:o},l>this.maxScrollX&&a++;
this.goToPage(this.currentPage.pageX||0,this.currentPage.pageY||0,0),this.options.snapThreshold%1===0?(this.snapThresholdX=this.options.snapThreshold,this.snapThresholdY=this.options.snapThreshold):(this.snapThresholdX=e.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].width*this.options.snapThreshold),this.snapThresholdY=e.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].height*this.options.snapThreshold))
}}),this.on("flick",function(){var t=this.options.snapSpeed||e.max(e.max(e.min(e.abs(this.x-this.startX),1e3),e.min(e.abs(this.y-this.startY),1e3)),300);this.goToPage(this.currentPage.pageX+this.directionX,this.currentPage.pageY+this.directionY,t)
})},_nearestSnap:function(t,i){if(!this.pages.length)return{x:0,y:0,pageX:0,pageY:0};var s=0,o=this.pages.length,n=0;if(e.abs(t-this.absStartX)<this.snapThresholdX&&e.abs(i-this.absStartY)<this.snapThresholdY)return this.currentPage;
for(t>0?t=0:t<this.maxScrollX&&(t=this.maxScrollX),i>0?i=0:i<this.maxScrollY&&(i=this.maxScrollY);o>s;s++)if(t>=this.pages[s][0].cx){t=this.pages[s][0].x;break}for(o=this.pages[s].length;o>n;n++)if(i>=this.pages[0][n].cy){i=this.pages[0][n].y;
break}return s==this.currentPage.pageX&&(s+=this.directionX,0>s?s=0:s>=this.pages.length&&(s=this.pages.length-1),t=this.pages[s][0].x),n==this.currentPage.pageY&&(n+=this.directionY,0>n?n=0:n>=this.pages[0].length&&(n=this.pages[0].length-1),i=this.pages[0][n].y),{x:t,y:i,pageX:s,pageY:n}
},goToPage:function(t,i,s,o){o=o||this.options.bounceEasing,t>=this.pages.length?t=this.pages.length-1:0>t&&(t=0),i>=this.pages[t].length?i=this.pages[t].length-1:0>i&&(i=0);var n=this.pages[t][i].x,r=this.pages[t][i].y;
s=void 0===s?this.options.snapSpeed||e.max(e.max(e.min(e.abs(n-this.x),1e3),e.min(e.abs(r-this.y),1e3)),300):s,this.currentPage={x:n,y:r,pageX:t,pageY:i},this.scrollTo(n,r,s,o)},next:function(t,i){var s=this.currentPage.pageX,e=this.currentPage.pageY;
s++,s>=this.pages.length&&this.hasVerticalScroll&&(s=0,e++),this.goToPage(s,e,t,i)},prev:function(t,i){var s=this.currentPage.pageX,e=this.currentPage.pageY;s--,0>s&&this.hasVerticalScroll&&(s=0,e--),this.goToPage(s,e,t,i)
},_initKeys:function(){var i,s={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40};if("object"==typeof this.options.keyBindings)for(i in this.options.keyBindings)"string"==typeof this.options.keyBindings[i]&&(this.options.keyBindings[i]=this.options.keyBindings[i].toUpperCase().charCodeAt(0));
else this.options.keyBindings={};for(i in s)this.options.keyBindings[i]=this.options.keyBindings[i]||s[i];a.addEvent(t,"keydown",this),this.on("destroy",function(){a.removeEvent(t,"keydown",this)})},_key:function(t){if(this.enabled){var i,s=this.options.snap,o=s?this.currentPage.pageX:this.x,n=s?this.currentPage.pageY:this.y,r=a.getTime(),h=this.keyTime||0,l=.25;
switch(this.options.useTransition&&this.isInTransition&&(i=this.getComputedPosition(),this._translate(e.round(i.x),e.round(i.y)),this.isInTransition=!1),this.keyAcceleration=200>r-h?e.min(this.keyAcceleration+l,50):0,t.keyCode){case this.options.keyBindings.pageUp:this.hasHorizontalScroll&&!this.hasVerticalScroll?o+=s?1:this.wrapperWidth:n+=s?1:this.wrapperHeight;
break;case this.options.keyBindings.pageDown:this.hasHorizontalScroll&&!this.hasVerticalScroll?o-=s?1:this.wrapperWidth:n-=s?1:this.wrapperHeight;break;case this.options.keyBindings.end:o=s?this.pages.length-1:this.maxScrollX,n=s?this.pages[0].length-1:this.maxScrollY;
break;case this.options.keyBindings.home:o=0,n=0;break;case this.options.keyBindings.left:o+=s?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.up:n+=s?1:5+this.keyAcceleration>>0;break;
case this.options.keyBindings.right:o-=s?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.down:n-=s?1:5+this.keyAcceleration>>0;break;default:return}if(s)return void this.goToPage(o,n);
o>0?(o=0,this.keyAcceleration=0):o<this.maxScrollX&&(o=this.maxScrollX,this.keyAcceleration=0),n>0?(n=0,this.keyAcceleration=0):n<this.maxScrollY&&(n=this.maxScrollY,this.keyAcceleration=0),this.scrollTo(o,n,0),this.keyTime=r
}},_animate:function(t,i,s,e){function o(){var d,u,f,m=a.getTime();return m>=p?(n.isAnimating=!1,n._translate(t,i),void(n.resetPosition(n.options.bounceTime)||n._execEvent("scrollEnd"))):(m=(m-c)/s,f=e(m),d=(t-r)*f+r,u=(i-l)*f+l,n._translate(d,u),void(n.isAnimating&&h(o)))
}var n=this,r=this.x,l=this.y,c=a.getTime(),p=c+s;this.isAnimating=!0,o()},handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;
case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);
break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(t);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(t);
break;case"keydown":this._key(t);break;case"click":this.enabled&&!t._constructed&&(t.preventDefault(),t.stopPropagation())}}},r.prototype={handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);
break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t)
}},destroy:function(){this.options.fadeScrollbars&&(clearTimeout(this.fadeTimeout),this.fadeTimeout=null),this.options.interactive&&(a.removeEvent(this.indicator,"touchstart",this),a.removeEvent(this.indicator,a.prefixPointerEvent("pointerdown"),this),a.removeEvent(this.indicator,"mousedown",this),a.removeEvent(t,"touchmove",this),a.removeEvent(t,a.prefixPointerEvent("pointermove"),this),a.removeEvent(t,"mousemove",this),a.removeEvent(t,"touchend",this),a.removeEvent(t,a.prefixPointerEvent("pointerup"),this),a.removeEvent(t,"mouseup",this)),this.options.defaultScrollbars&&this.wrapper.parentNode.removeChild(this.wrapper)
},_start:function(i){var s=i.touches?i.touches[0]:i;i.preventDefault(),i.stopPropagation(),this.transitionTime(),this.initiated=!0,this.moved=!1,this.lastPointX=s.pageX,this.lastPointY=s.pageY,this.startTime=a.getTime(),this.options.disableTouch||a.addEvent(t,"touchmove",this),this.options.disablePointer||a.addEvent(t,a.prefixPointerEvent("pointermove"),this),this.options.disableMouse||a.addEvent(t,"mousemove",this),this.scroller._execEvent("beforeScrollStart")
},_move:function(t){{var i,s,e,o,n=t.touches?t.touches[0]:t;a.getTime()}this.moved||this.scroller._execEvent("scrollStart"),this.moved=!0,i=n.pageX-this.lastPointX,this.lastPointX=n.pageX,s=n.pageY-this.lastPointY,this.lastPointY=n.pageY,e=this.x+i,o=this.y+s,this._pos(e,o),t.preventDefault(),t.stopPropagation()
},_end:function(i){if(this.initiated){if(this.initiated=!1,i.preventDefault(),i.stopPropagation(),a.removeEvent(t,"touchmove",this),a.removeEvent(t,a.prefixPointerEvent("pointermove"),this),a.removeEvent(t,"mousemove",this),this.scroller.options.snap){var s=this.scroller._nearestSnap(this.scroller.x,this.scroller.y),o=this.options.snapSpeed||e.max(e.max(e.min(e.abs(this.scroller.x-s.x),1e3),e.min(e.abs(this.scroller.y-s.y),1e3)),300);
(this.scroller.x!=s.x||this.scroller.y!=s.y)&&(this.scroller.directionX=0,this.scroller.directionY=0,this.scroller.currentPage=s,this.scroller.scrollTo(s.x,s.y,o,this.scroller.options.bounceEasing))}this.moved&&this.scroller._execEvent("scrollEnd")
}},transitionTime:function(t){t=t||0;var i=a.style.transitionDuration;if(this.indicatorStyle[i]=t+"ms",!t&&a.isBadAndroid){this.indicatorStyle[i]="0.0001ms";var s=this;h(function(){"0.0001ms"===s.indicatorStyle[i]&&(s.indicatorStyle[i]="0s")
})}},transitionTimingFunction:function(t){this.indicatorStyle[a.style.transitionTimingFunction]=t},refresh:function(){this.transitionTime(),this.indicatorStyle.display=this.options.listenX&&!this.options.listenY?this.scroller.hasHorizontalScroll?"block":"none":this.options.listenY&&!this.options.listenX?this.scroller.hasVerticalScroll?"block":"none":this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none",this.scroller.hasHorizontalScroll&&this.scroller.hasVerticalScroll?(a.addClass(this.wrapper,"iScrollBothScrollbars"),a.removeClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="8px":this.wrapper.style.bottom="8px")):(a.removeClass(this.wrapper,"iScrollBothScrollbars"),a.addClass(this.wrapper,"iScrollLoneScrollbar"),this.options.defaultScrollbars&&this.options.customStyle&&(this.options.listenX?this.wrapper.style.right="2px":this.wrapper.style.bottom="2px"));
this.wrapper.offsetHeight;this.options.listenX&&(this.wrapperWidth=this.wrapper.clientWidth,this.options.resize?(this.indicatorWidth=e.max(e.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8),this.indicatorStyle.width=this.indicatorWidth+"px"):this.indicatorWidth=this.indicator.clientWidth,this.maxPosX=this.wrapperWidth-this.indicatorWidth,"clip"==this.options.shrink?(this.minBoundaryX=-this.indicatorWidth+8,this.maxBoundaryX=this.wrapperWidth-8):(this.minBoundaryX=0,this.maxBoundaryX=this.maxPosX),this.sizeRatioX=this.options.speedRatioX||this.scroller.maxScrollX&&this.maxPosX/this.scroller.maxScrollX),this.options.listenY&&(this.wrapperHeight=this.wrapper.clientHeight,this.options.resize?(this.indicatorHeight=e.max(e.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8),this.indicatorStyle.height=this.indicatorHeight+"px"):this.indicatorHeight=this.indicator.clientHeight,this.maxPosY=this.wrapperHeight-this.indicatorHeight,"clip"==this.options.shrink?(this.minBoundaryY=-this.indicatorHeight+8,this.maxBoundaryY=this.wrapperHeight-8):(this.minBoundaryY=0,this.maxBoundaryY=this.maxPosY),this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.sizeRatioY=this.options.speedRatioY||this.scroller.maxScrollY&&this.maxPosY/this.scroller.maxScrollY),this.updatePosition()
},updatePosition:function(){var t=this.options.listenX&&e.round(this.sizeRatioX*this.scroller.x)||0,i=this.options.listenY&&e.round(this.sizeRatioY*this.scroller.y)||0;this.options.ignoreBoundaries||(t<this.minBoundaryX?("scale"==this.options.shrink&&(this.width=e.max(this.indicatorWidth+t,8),this.indicatorStyle.width=this.width+"px"),t=this.minBoundaryX):t>this.maxBoundaryX?"scale"==this.options.shrink?(this.width=e.max(this.indicatorWidth-(t-this.maxPosX),8),this.indicatorStyle.width=this.width+"px",t=this.maxPosX+this.indicatorWidth-this.width):t=this.maxBoundaryX:"scale"==this.options.shrink&&this.width!=this.indicatorWidth&&(this.width=this.indicatorWidth,this.indicatorStyle.width=this.width+"px"),i<this.minBoundaryY?("scale"==this.options.shrink&&(this.height=e.max(this.indicatorHeight+3*i,8),this.indicatorStyle.height=this.height+"px"),i=this.minBoundaryY):i>this.maxBoundaryY?"scale"==this.options.shrink?(this.height=e.max(this.indicatorHeight-3*(i-this.maxPosY),8),this.indicatorStyle.height=this.height+"px",i=this.maxPosY+this.indicatorHeight-this.height):i=this.maxBoundaryY:"scale"==this.options.shrink&&this.height!=this.indicatorHeight&&(this.height=this.indicatorHeight,this.indicatorStyle.height=this.height+"px")),this.x=t,this.y=i,this.scroller.options.useTransform?this.indicatorStyle[a.style.transform]="translate("+t+"px,"+i+"px)"+this.scroller.translateZ:(this.indicatorStyle.left=t+"px",this.indicatorStyle.top=i+"px")
},_pos:function(t,i){0>t?t=0:t>this.maxPosX&&(t=this.maxPosX),0>i?i=0:i>this.maxPosY&&(i=this.maxPosY),t=this.options.listenX?e.round(t/this.sizeRatioX):this.scroller.x,i=this.options.listenY?e.round(i/this.sizeRatioY):this.scroller.y,this.scroller.scrollTo(t,i)
},fade:function(t,i){if(!i||this.visible){clearTimeout(this.fadeTimeout),this.fadeTimeout=null;var s=t?250:500,e=t?0:300;t=t?"1":"0",this.wrapperStyle[a.style.transitionDuration]=s+"ms",this.fadeTimeout=setTimeout(function(t){this.wrapperStyle.opacity=t,this.visible=+t
}.bind(this,t),e)}}},o.utils=a,"undefined"!=typeof s&&s.exports?s.exports=o:"function"==typeof define&&define.amd?define(function(){return o}):t.IScroll=o}(window,document,Math)});
;define("hiloan:node_modules/vue-validator-manage/dist/vue-validator-manage.common",function(e,i){"use strict";function t(e){return e&&e.__esModule?e:{"default":e}}function a(e,i,t){return i in e?Object.defineProperty(e,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[i]=t,e
}Object.defineProperty(i,"__esModule",{value:!0});var n=e("hiloan:node_modules/vue/dist/vue.common"),o=t(n),d=e("hiloan:node_modules/underscore/underscore"),r=t(d),l=function(e){return btoa(unescape(encodeURIComponent(e)))
},s="form-valid",u="form-invalid",c="form-touched",f="form-untouched",v="form-dirty",m="form-pristine",h="form-modified",p=Object.assign?Object.assign:r.default.extend,y={};y.install=function(e){e.directive("fieldname",{params:["v-model","v-text","base64","format"],update:function(e,i){var t=e,n=i,o=this.vm,d=o.$root;
r.default.isUndefined(e)&&(t=this.expression),r.default.isUndefined(i)&&(n=this.expression),n&&n===t&&delete d._fieldsData[n],d.getFieldsData=d.getFieldsData||function(){},d._fieldsData=d._fieldsData||{},d.fieldsData=d.fieldsData||{};
var s=this.params.format||function(e){return e},u=this.params.base64?!0:!1,c=this.params,f=c.vModel,v=c.vText;if(f=f?f:v){var m=s(o[f]);if(u){var h=l(m);m=encodeURIComponent(h)}t&&(d._fieldsData[t]=m,d.fieldsData=p({},d.fieldsData,a({},t,m))),o.$watch(f,function(e){e=s(e),n&&n===t?(delete d._fieldsData[n],d.fieldsData[n]=""):"fieldname"!==t&&(d._fieldsData[t]=u?encodeURIComponent(l(m)):e,d.fieldsData[t]=d._fieldsData[t])
})}d.getFieldsData=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:!1,i=d._fieldsData,t={};return Object.keys(i).forEach(function(a){var n=i[a];i[a]&&"string"==typeof i[a]&&(n=i[a].replace(/[\u0000-\u001F|\u007F|\u0080-\u009F]/g,"").trim()),a&&("string"==typeof n&&n||e)&&(t[a]=n)
}),JSON.parse(JSON.stringify(t))}}}),e.directive("fieldset",{params:["vfor"],update:function(i){var t=i;if(void 0===i&&(t=this.expression),""===t)return!1;var n=this.vm,o=n.$root,d=this.params.vfor?!0:!1;
o.validation||o.$set("validation",{valid:!1,invalid:!0,touched:!1,untouched:!0,modified:!1,dirty:!1,pristine:!0,asyncDetail:{},asyncResult:"true"}),o.collection={valid:{},touched:{},dirty:{},modified:{}};
var r=function(e,i){return Object.keys(e).some(function(t){return e[t]===i})},l=function(e){var i="",t=e._directives;if(t.some(function(e){return e.validatorName?(i=e.validatorName,!0):void 0}),!i){var a=Object.keys(e._validatorMaps);
return a.length?a[0]:""}return i},y=function(e,i,t){e.$watch(i,function(e){e?this.$emit(t[0]):t[1]&&this.$emit(t[1])})},D=p({},n),_=function(){var e=n._directives,a=void 0;return e.forEach(function(e){if("fieldset"===e.name){var n=!1;
if(i||e.expression!==t){if(i){if(d)D=i;else{var o=e.expression.split(".");o.forEach(function(e){D=D[e]})}D===t&&(n=!0)}}else n=!0;if(n)return a=e.el.__vue__,!1}}),a||!1},g=_(),b=function(e,i,t){var n=t.valid,o=t.invalid,d=t.touched,l=t.untouched,s=t.modified,u=t.dirty,c=t.pristine,f=t.errors,v={valid:n,invalid:o,touched:d,untouched:l,modified:s,dirty:u,pristine:c,errors:f};
e.validation=p({},e.validation,a({},i,v)),e.collection.valid[i]=t.valid,e.collection.touched[i]=t.touched,e.collection.dirty[i]=t.dirty,e.collection.modified[i]=t.modified;var m=e.validation;m.valid=t.valid?!r(e.collection.valid,!1):!1,m.invalid=!m.valid,m.touched=t.touched?!0:r(e.collection.touched,!0),m.untouched=!m.touched,m.dirty=t.dirty?!0:r(e.collection.dirty,!0),m.pristine=!m.dirty,m.modified=t.modified?!0:r(e.collection.modified,!0)
},O=function(e,i){var t="true";if("false"===i)t="false";else{var a=Object.keys(e),n=a.some(function(i){return"false"===e[i]});if(n)t="false";else{var o=a.every(function(i){return"true"===e[i]});if(o)t="true";
else{var d=a.every(function(i){return"loading"===e[i]});t=d?"loading":"init"}}}return t},$=function(e,i,t){e.validation.asyncDetail[i]=t,e.validation.asyncResult=O(e.validation.asyncDetail,t)};e.nextTick(function(){var e=l(g);
if(b(o,t,g[e]),g){var i=function(e){o.validation.asyncDetail=p({},o.validation.asyncDetail,a({},t,"init")),o.validation.asyncResult="init",e.$watch("asyncState",function(e){$(o,t,e)})};if(g.$watch(e,function(e){b(o,t,e)
},{deep:!0}),"ui-select-input"===g.is&&g.hasAsync)return i(g);g.$parent.hasAsync&&i(g.$parent)}}),n.dispatchValidationOnce||(y(o,"validation.valid",[s,u]),y(o,"validation.touched",[c,f]),y(o,"validation.dirty",[v,m]),y(o,"validation.modified",[h]),n.dispatchValidationOnce=!0)
}})},o.default.use(y),i.default=y});