<div class="layui-table-tool">
<div class="filter">

    <form action="{:U(GROUP_NAME.'/User/qianbao')}" method="post">

        <input name="keyword" type="text" class="inpMain" placeholder="用户名"  value="{$keyword}" size="20" />

        <input name="sday" id="sday" type="text" placeholder="开始时间" class="inpMain  day1" value="{$sday}" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="16" />



        <input name="eday" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="eday" type="text" placeholder="结束时间" class="inpMain  day1" value="{$eday}" size="16" />

        <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
        <span><font color="red"><b>用户总计：{$count}人</b></font></span>

    </form>
</div>
</div>
<div id="list">

    <table width="100%" border="0" cellpadding="10" cellspacing="0" class="tableBasic">

        <tr>

            <th width="80" align="center"><b>ID</b></th>

            <th width="150" align="center"><b>用户名</b></th>

            <th width="80" align="center"><b>钱包余额</b></th>

            <th width="80" align="center"><b>处理中金额</b></th>

            <th width="80" align="center"><b>提现密码</b></th>



            <th align="center"><font color="red"><b>操作</b></th>

        </tr>

        <volist name="list" id="vo">

            <tr>

                <td align="center">{$vo.id}</td>

                <td align="center">{$vo.phone}</td>

                <td align="center">{$vo.zhanghuyue}</td>

                <td align="center">{$vo.daihuan_money}</td>

                <td align="center">{$vo.tixianmima}</td>

                <td align="center">




                    <div class="layui-btn layui-btn-sm layui-btn-normal"><a href="javascript:chongzhi('{$vo.id}');">帐号充值</a>

                    </div>

                    <div class="layui-btn layui-btn-sm layui-btn-warm"><a href="javascript:koukuan('{$vo.id}');">手动扣款</a>

                    </div>



                </td>

            </tr>

        </volist>

    </table>

</div>

<div class="clear"></div>

<div class="pager">

    {$page}

</div>


<script>






    function chongzhi(uid){

        layer.prompt({title: '输入充值金额，并确认', formType: 0}, function(money, index){

            if(money == '' || money == null){

                layer.msg('充值金额不能为空!');

            }else{

                $.post(

                    "{:U(GROUP_NAME.'/User/chongzhi')}",

                    {id:uid,money:money},

                    function (data){

                        if(data.status != 1){

                            layer.msg(data.msg);

                        }else if(data.status == 1){

                            layer.msg(data.msg);

                            layer.close(index);

                            location.reload();

                        }else{

                            layer.msg(data.msg);

                        }

                    }

                );

            }

        });

    }





    function koukuan(uid){

        layer.prompt({title: '输入扣款金额，并确认', formType: 0}, function(money, index){

            if(money == '' || money == null){

                layer.msg('扣款金额不能为空!');

            }else{

                $.post(

                    "{:U(GROUP_NAME.'/User/koukuan')}",

                    {id:uid,money:money},

                    function (data){

                        if(data.status != 1){

                            layer.msg(data.msg);

                        }else if(data.status == 1){

                            layer.msg("扣款成功!");

                            layer.close(index);

                            location.reload();

                        }else{

                            layer.msg(data.msg);

                        }

                    }

                );

            }

        });

    }







</script>