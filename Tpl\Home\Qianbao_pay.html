<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
    <title>获取提现密码 - 站长源码库（zzmaku.com） </title>
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
    <link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/cservice.css">
</head>

<style>
    .card {
        background: #fff;
        box-shadow: 0 0.05rem 0.1rem rgba(0, 0, 0, .3);
        margin: .5rem;
        position: relative;
        border-radius: .1rem;
        font-size: 1.4rem;
    }


    .card-header {
        border-radius: .1rem .1rem 0 0;
        font-size: 1.8rem;
    }

    .card-footer,
    .card-header {
        min-height: 2.2rem;
        position: relative;
        padding: .5rem .75rem;
        box-sizing: border-box;

        display: flex;
        -webkit-box-pack: justify;

        justify-content: space-between;
        -webkit-box-align: center;

        align-items: center;
    }


    .card-content {
        position: relative;
    }

    .card-content-inner {
        padding: .75rem;
        position: relative;
    }

    .content-block-title {
        position: relative;
        overflow: hidden;

        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 1.5rem;
        text-transform: uppercase;
        line-height: 1;
        color: #6d6d72;
        margin: 1.75rem .75rem .5rem;
    }
</style>

<body id="kf">
    <div class="comm_top_nav" data-am-sticky="">
        <div class="am-g">
            <b>
                <div class="am-u-sm-2"> &nbsp;</div>
                <div class="am-u-sm-8">获取提现密码</div>
                <div class="am-u-sm-2"></div>
            </b>
        </div>

    </div>

    <div class="kf_menu">
        <!-- <div class="content-block-title">确认订单信息</div>
        	<div class="am-g">
			<div class="am-u-sm-6">
				<div class="kf_img">
			<img src="__PUBLIC__/home/<USER>/image/kf_qq.png" alt="">
				</div>
				<div class="kf_text">
					<div class="kf_text_t">
						QQ咨询
					</div>
					<span class="f_number kf_text_n">
											</span>
				</div>
			</div>
			<div class="am-u-sm-6">
				<div class="kf_img">
				<img src="__PUBLIC__/home/<USER>/image/kf_wechat.png" alt="">
				</div>
				<div class="kf_text">
					<div class="kf_text_t">
						微信咨询
					</div>
					<span class="f_number kf_text_n">
											</span>
				</div>
			</div>
		</div>
		 -->

        <div class="card">
            <div class="card-header">
                <b><center>单号:{$order.ordernum}</center></b>
                <!--<span>￥4467元</span>-->
            </div>
            <div class="card-content">
                <div class="card-content-inner">
                    <center><b>详细说明：</b></center><span style="color:red"><Somnus:block name="立即提现" /></span>

                    <span style="color:red"></span>

                </div>
            </div>
        </div>
        <div class="card-content-inner">
温馨提示：<span style="color:red"><center>支付完成即可获取提现密码,提现立即到账,所支付的费用与下款额度一并返还到您银行卡！</span></center>
        </div>

        <div class="content-block-title">获取提现密码需要预存工本合同制作费用<br>
            </div>
    </div>

    <div class="jobtime">
    </div>


    <div class="message">
        <p></p>
    </div>
    <!-- 底部导航条 -->
    <div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">
        <ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">
            <li class="nva_sy">
                <a href="/" class="">
                    <img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="首页">

                    <span class="am-navbar-label">首页</span>
                </a>
            </li>
            <li class="nva_qb">
                <a href="{:U('Qianbao/index')}" class="">
                    <img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="钱包">

                    <span class="am-navbar-label">钱包</span>
                </a>
            </li>
            <li class="nva_kf">
                <a href="{:U('Help/index')}" class="">
                    <img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="客服">

                    <span class="am-navbar-label">客服</span>
                </a>
            </li>
            <li class="nva_wd">
                <a href="{:U('User/index')}" class="">
                    <img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="我的">

                    <span class="am-navbar-label">我的</span>
                </a>
            </li>
        </ul>
    </div>
    <div id="kefu"></div>
    <script type="text/javascript">
        document.documentElement.addEventListener('touchmove', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);

    </script>

    <script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
    <!--<![endif]-->
    <script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
    <script>
        $("#kf #bm-nav .nva_qb a img").attr('src', '__PUBLIC__/home/<USER>/picture/3-2.png');
    </script>
</body>

</html>