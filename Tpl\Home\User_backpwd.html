<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="/Public/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="/Public/home/<USER>/css/app.css">
	<link rel="stylesheet" href="/Public/home/<USER>/css/all.css">
	<title>找回密码 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="/Public/home/<USER>/css/login--1.css">
</head>

<body>
	<!--[if lte IE 9]>
<p class="browsehappy">你正在使用<strong>过时</strong>的浏览器，Amaze UI 暂不支持。 请 <a
  href="http://browsehappy.com/" target="_blank">升级浏览器</a>
  以获得更好的体验！</p>
<![endif]-->

	<div class="head-bg forget">
		<div class="back" onclick="javascript:window.location.href='{:U('User/login')}'">
			<i class="fas fa-chevron-circle-left"></i>
			返回
		</div>
		<div class="websitename_box">
			<div class="websitename">
				<div>找回密码</div>
			</div>
		</div>

	</div>

	<div class="login">
		<div class="am-g">

			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal">
					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="/Public/home/<USER>/picture/phone.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="number" name="account" id="account" minlength="11" placeholder="输入你的手机号">
						</div>
					</div>

					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="/Public/home/<USER>/picture/yzm.png" alt="">
						</label>
						<div class="am-u-sm-8 f_number" style="padding: 0;">
							<input type="number" name="code" id="code" placeholder="输入验证码">
						</div>
						<span style="
							font-size: 1.8rem;
							font-weight: 600;
							text-align: center;
							padding: 0.2em 0;
							height: 100%;
							background: rgb(255, 209, 27);
							border-radius: 1000px;
							letter-spacing: 1px;" class="am-u-sm-3 am-form-label gain-button" data-type="1">获取</span>
					</div>

					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="/Public/home/<USER>/picture/pwd.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="password" name="password" id="password" minlength="6" maxlength="15"
								placeholder="设置你的密码">
						</div>
					</div>

					<div class="am-form-group input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="/Public/home/<USER>/picture/pwd2.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="password" name="dpassword" id="dpassword" minlength="6" maxlength="15"
								placeholder="再次输入密码">
						</div>
					</div>


					<div style="height: 15px;"></div>

					<div class="am-form-group">
						<div class="">
							<button type="button" class="am-btn" id="forget-button">提交修改</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div class="message">
		<p></p>
	</div>


	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>


	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="/Public/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="/Public/home/<USER>/js/amazeui.min.js"></script>
	<!--<div id="kefu"></div>-->
	<script type="text/javascript" src="/Public/home/<USER>/js/login.js"></script>
</body>

</html>