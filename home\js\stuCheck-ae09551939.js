function checkinfo(e){var l=document.getElementById("usr"),t=document.getElementById("percard"),n=document.getElementById("qq"),u=(document.getElementById("showCityPicker1"),document.getElementById("add"),document.getElementById("sch")),d=document.getElementById("showCityPicker2"),s=document.getElementById("cadd"),c=(document.getElementById("sel"),document.getElementById("messageBox")),o=/^([\*\`\·\.\u4e00-\u9fa5])+$/,a=/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,y=/^\d{4,13}$/;if(null==l.value||""==l.value)return l.focus(),c.style.display="block",c.innerHTML="请输入姓名",!1;if(!o.test(l.value))return c.style.display="block",c.innerHTML="请输入中文姓名",!1;if(2==e){if(null==t.value||""==t.value)return t.focus(),c.style.display="block",c.innerHTML="请输入身份证号",!1;if(!a.test(t.value))return t.focus(),c.style.display="block",c.innerHTML="请输入真实身份证号",!1}return null==n.value||""==n.value?(n.focus(),c.style.display="block",c.innerHTML="请输入QQ号码",!1):y.test(n.value)?null==u.value||""==u.value?(u.focus(),c.style.display="block",c.innerHTML="请输入学校",!1):null==d.value||""==d.value?(d.focus(),c.style.display="block",c.innerHTML="请选择宿舍地址省市区",!1):null!=s.value&&""!=s.value||(s.focus(),c.style.display="block",c.innerHTML="请输入宿舍地址",!1):(n.focus(),c.style.display="block",c.innerHTML="请填写4-13位数字",!1)}