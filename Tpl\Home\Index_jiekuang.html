<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>首页 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/iindex.css">
</head>

<body id="sy">
	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('Index/index')}'"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">借款</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>

	<div class="am-slider am-slider-default" data-am-flexslider="{controlNav:false}" id="demo-slider-0"
		style="max-height: 200px;overflow: hidden;margin: 0;border:0;border-radius: 0;box-shadow: none;">
		<ul class="am-slides">
			<li><img src="__PUBLIC__/home/<USER>/image/bing-1.jpg"></li>
			<li><img src="__PUBLIC__/home/<USER>/image/bing-2.jpg"></li>
		</ul>
	</div>

	<div id="jk_lb_box" class="am-slider am-slider-default f_number"
		data-am-flexslider="{controlNav:false,direction:'vertical',directionNav: false,slideshowSpeed: 2000,animationSpeed: 1000}"
		id="demo-slider-1"
		style="max-height: 170px;overflow: hidden;text-align: center;font-size: 15px;margin: 0;border:0;padding:5px 0;border-radius: 0;box-shadow: none;background: none">
		<ul class="am-slides" id="jk_lb">
			<foreach name="redaydata" item="vo">
				<li>
					<div>
						<php>echo date("Y-m-d");</php><span>{$vo.phone}</span>成功借款<span>
							<php>echo rand(200,300);</php>
						</span>00元!
					</div>
				</li>
			</foreach>
		</ul>
	</div>

	<div class="jk-box">
		<div class="am-g">
			<div style="text-align: center;">
				<span id="jkje" class="f_number">{:C('cfg_definamoney')}</span>
				<sapn id="edit"><i class="far fa-edit"></i></sapn>
			</div>

			<div style="text-align: center;">
				<small>
					<i class="am-icon-credit-card"></i>
					<span>借款金额(元)日/月息(<span id="rixiss"></span>%/<span id="yuess"></span>%)</span>
				</small>
			</div>

			<ul class="am-avg-sm-3 boxes">
				<li class="box box-1">
					<h3 id="dzje" class="f_number"></h3>
					<small>借款金额(元)</small>
				</li>
				<li class="box box-2">
					<input hidden="" type="number" id="input_mqhk">
					<h3 id="mqhk" class="f_number">1800</h3>
					<small>每期还款(元)</small>
				</li>
				<li class="box box-3">
					<h3 id="jksj" class="f_number">30</h3>
					<small>借款时间(个月)</small>
				</li>
			</ul>

			<hr data-am-widget="divider" style="border-top: 1px solid #f0f0f0 !important; width: 90%;"
				class="am-divider am-divider-default">

			<div style="height:10px;"></div>
			<div class="title_item">
				<i class="am-icon-ioxhost"></i>
				<span>借款金额(元)</span>
			</div>

			<div class="am-u-sm-11 am-u-sm-centered">
				<input id="amount" name="amount" class="ne-input" placeholder="只允许输入数字" pattern="\d*" type="hidden">
				<input type="range" id="slider" name="slider" class="ne-range" value=" " style="margin-top: 4vw;">
				<div class="sjkd">
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="sjkd_line"></div>
					<div class="clear"></div>
				</div>
				<div style="display: none;" id="feilv">{:C('cfg_fuwufei')}</div>
				<div class="sjkd_v f_number">
					<div class="sjkd_value"><span class="sjkd_value_l" id="min">{:C('cfg_minmoney')}</span></div>
					<div class="sjkd_value"><span class="sjkd_value_l"></span></div>
					<div class="sjkd_value"><span class="sjkd_value_l"></span><span class="sjkd_value_r"
							id="max">{:C('cfg_maxmoney')}</span></div>

				</div>
			</div>
			<div style="height:20px;"></div>
			<div class="title_item">
				<i class="am-icon-history"></i>
				<span>借款时间</span>
			</div>

			<div id="jksj_group" class="am-btn-group am-btn-group-justify am-u-sm-11 am-u-sm-centered"
				data-am-button="">
				<!--借款时间-->
				<volist name="montharr" id="v">
				<a class="am-btn am-btn-default am-btn-xs jk_time  " data-time="{$v}" role="button"><span
						class="f_number">{$v}</span>个月</a>
				</volist>
				
				<!--<a class="am-btn am-btn-default am-btn-xs jk_time  jk_time_add" data-time="{$v}" role="button"><span-->
				<!--		class="f_number">6</span>个月</a>-->
				<!--<a class="am-btn am-btn-default am-btn-xs jk_time" data-time="12" role="button"><span-->
				<!--		class="f_number">12</span>个月</a>-->
				<!--<a class="am-btn am-btn-default am-btn-xs jk_time" data-time="18" role="button"><span-->
				<!--		class="f_number">18</span>个月</a>-->
				<!--<a class="am-btn am-btn-default am-btn-xs jk_time" data-time="24" role="button"><span-->
				<!--		class="f_number">24</span>个月</a>-->
				<!--<a class="am-btn am-btn-default am-btn-xs jk_time" data-time="36" role="button"><span-->
				<!--		class="f_number">36</span>个月</a>-->

			</div>
			
			
			<div style="height:30px;"></div>
			<div class="am-u-sm-11 am-u-sm-centered">
				<button type="button" class="am-btn" id="q-button"
					data-am-modal="{target: '#my-popup-sq'}">立即申请</button>
			</div>
		</div>
	</div>


	<div class="am-modal am-modal-prompt" tabindex="-1" id="my-prompt">
		<div class="am-modal-dialog">
			<div class="am-modal-hd">
				<Somnus:sitecfg name="sitetitle" />
			</div>
			<div class="am-modal-bd">
				请输入借款金额
				<input type="number" class="am-modal-prompt-input input_jkje">
			</div>
			<div class="am-modal-footer">
				<span class="am-modal-btn" data-am-modal-cancel="">取消</span>
				<span class="am-modal-btn" data-am-modal-confirm="">提交</span>
			</div>
		</div>
	</div>

	<div class="am-popup" id="my-popup-sq">
		<div class="am-popup-inner">
			<div class="am-popup-hd">
				<h4 class="am-popup-title">借款详情</h4>
				<span data-am-modal-close="" class="am-close">&times;</span>
			</div>
			<div class="am-popup-bd" style="background: #ffffff;">
				<span style="font-size: 23px;font-weight: bold;">借款详情</span>
				<div>
					<div class="topline">

					</div>
					<div class="sq_box">
						<div class="am-g">
							<div class="am-u-sm-12" style="text-align: center; padding: 10px 0;">
								<b class="f_number rll_number p_jkje">100</b>
								<span class="rll_symbol">元</span>
								<br>
								<span class="loan_title">借款金额</span>
								<div>
									<small>
										<i class="fas fa-ticket-alt"></i>
										<sapn class="cop_text"></sapn>
									</small>
								</div>
							</div>
						</div>
						<div class="am-g" style="text-align: center;">
							<div class="am-u-sm-6" style="border-right:solid 1px #ececec;">
								<b class="f_number jksj_number p_mqhk"></b>
								<span class="jksj_symbol">元</span>


								<br>
								<span class="loan_title">还款金额<b><small style="color: #bf2222"
											class="f_number p_del_cop"></small></b></span>
							</div>
							<div class="am-u-sm-6">
								<b class="f_number jksj_number p_jksj"></b>
								<span class="jksj_symbol">月</span>
								<br>
								<span class="loan_title">借款时间</span>
							</div>
						</div>
						<div class="am-g p_u_info" style="padding-top: 30px;">
							<div class="am-u-sm-4">借款人</div>
							<div class="am-u-sm-8 p_u_fullname">{$userinfo.name}</div>
						</div>
						<div class="am-g p_u_info">
							<div class="am-u-sm-4">入账银行</div>
							<div class="am-u-sm-8 p_u_showbank">{$userinfo.bankname}</div>
						</div>
						<div class="am-g p_u_info">
							<div class="am-u-sm-4">入账卡号</div>
							<!--<div class="am-u-sm-8 f_number">-->
							<!--	****&nbsp;****&nbsp;****&nbsp;-->
							<!--	<span class="p_u_banknumber">{$userinfo.bankcard}</span>-->
							<!--</div>-->
							<div class="am-u-sm-8 f_number">
								<span class="p_u_banknumber">{$userinfo.bankcard}</span>
							</div>
						</div>
					</div>

					<div class="bottomline"></div>

					<div style="height: 50px;"></div>
					<div class="am-g">
						<div class="am-u-sm-11 am-u-sm-centered">

							<button type="button" class="am-btn" id="index-button">立即借款</button>

						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
		
		var Discountmonth = {$Discountmonth};
		

	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script>$("#jksj_group").children(":first").addClass('jk_time_add')</script>
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/iindex.js"></script>

	<script>
		// 获取选中的值
		
		$(function () {
			var $radios = $('[name="options"]');
			$radios.on('change', function () {
				console.log('单选框当前选中的是：', $radios.filter(':checked').val());
			});
		});
	</script>
	
	  <div style="display: none;">
    <Somnus:sitecfg name="sitecode" />
  </div>
	
	

</body>

</html>