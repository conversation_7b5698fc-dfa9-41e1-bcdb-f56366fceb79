void function(a,b){var c=a.alogObjectName||"alog",d=a[c]=a[c]||function(){a[c].l=a[c].l||+new Date,(a[c].q=a[c].q||[]).push(arguments)},e="monkey";d("define",e,["element"],function(c){var f,g=d.tracker(e),h=a.screen,i=a.orientation,j={90:1,180:2,"-90":3},k=b.referrer;return g.set("ver",5),g.set("pid",241),h&&g.set("px",h.width+"*"+h.height),g.set("ori",j[i]),g.set("ref",k),d(e+".on","create",function(){f=d.timestamp,g.set("protocolParameter",{reports:null})}),d(e+".on","send",function(a){"pageview"==a.t&&(a.cmd="open",a.ps=c.ps()),a.now&&(a.ts=f(a.now).toString(36),a.now="")}),g})}(window,document);