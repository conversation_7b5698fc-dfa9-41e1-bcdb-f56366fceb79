#evaluation
{
    background: #ffffff;
}

.fix_bottom
{
    position: fixed;
    width: 100%;
    background: #0a628f;
    color: #ffffff;
    bottom: 0;
}

button
{
    outline: none !important;
    background: #fddf65;
    color: #333333;
    width: 100%;
    letter-spacing: 0.2rem;
    
    line-height: 30px !important;
    font-size: 20px !important;
}

.evaluation_box
{
    padding: 20px;
}

.check_list
{
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: solid 1px #f0f0f0;
}

.e_color
{
    color: #969696;
}

.am-ucheck-icons
{
    top: auto;
}

.evaluation_list
{
    position: absolute;
    top: 70px;
    width: 90%;
    left: 5%;
    display: none;
}

.question>div
{
    border-bottom: solid 1px #efefef;
    padding: 10px 0;
}

.question_12
{
    margin: 0 0 50px 0;
}
.question_12>div
{
    border-bottom: 0;
}
.question span
{
    font-weight: 600;
}

.question small
{
    color: #777777;
}

.angle
{
    text-align: right;
    line-height: 300%;
    color: #777777;
}

.double
{
    border: solid 1px #a26c00;
    border-radius: 3px;
    padding: 1px 5px;
    color: #a26c00;
}