/*******************************************************************************
* KindEditor - WYSIWYG HTML Editor for Internet
* Copyright (C) 2006-2011 kindsoft.net
*
* <AUTHOR> <<EMAIL>>
* @site http://www.kindsoft.net/
* @licence http://www.kindsoft.net/license.php
*******************************************************************************/

KindEditor.lang({
	source : '原始碼',
	preview : '預覽',
	undo : '復原(Ctrl+Z)',
	redo : '重複(Ctrl+Y)',
	cut : '剪下(Ctrl+X)',
	copy : '複製(Ctrl+C)',
	paste : '貼上(Ctrl+V)',
	plainpaste : '貼為純文字格式',
	wordpaste : '自Word貼上',
	selectall : '全選(Ctrl+A)',
	justifyleft : '靠左對齊',
	justifycenter : '置中',
	justifyright : '靠右對齊',
	justifyfull : '左右對齊',
	insertorderedlist : '編號清單',
	insertunorderedlist : '項目清單',
	indent : '增加縮排',
	outdent : '減少縮排',
	subscript : '下標',
	superscript : '上標',
	formatblock : '標題',
	fontname : '字體',
	fontsize : '文字大小',
	forecolor : '文字顏色',
	hilitecolor : '背景顏色',
	bold : '粗體(Ctrl+B)',
	italic : '斜體(Ctrl+I)',
	underline : '底線(Ctrl+U)',
	strikethrough : '刪除線',
	removeformat : '清除格式',
	image : '影像',
	multiimage : '批量影像上傳',
	flash : 'Flash',
	media : '多媒體',
	table : '表格',
	hr : '插入水平線',
	emoticons : '插入表情',
	link : '超連結',
	unlink : '移除超連結',
	fullscreen : '最大化',
	about : '關於',
	print : '列印(Ctrl+P)',
	fileManager : '瀏覽伺服器',
	code : '插入程式代碼',
	map : 'Google地圖',
	baidumap : 'Baidu地圖',
	lineheight : '行距',
	clearhtml : '清理HTML代碼',
	pagebreak : '插入分頁符號',
	quickformat : '快速排版',
	insertfile : '插入文件',
	template : '插入樣板',
	anchor : '錨點',
	yes : '確定',
	no : '取消',
	close : '關閉',
	editImage : '影像屬性',
	deleteImage : '刪除影像',
	editFlash : 'Flash屬性',
	deleteFlash : '删除Flash',
	editMedia : '多媒體屬性',
	deleteMedia : '删除多媒體',
	editLink : '超連結屬性',
	deleteLink : '移除超連結',
	tableprop : '表格屬性',
	tablecellprop : '儲存格屬性',
	tableinsert : '插入表格',
	tabledelete : '刪除表格',
	tablecolinsertleft : '向左插入列',
	tablecolinsertright : '向右插入列',
	tablerowinsertabove : '向上插入欄',
	tablerowinsertbelow : '下方插入欄',
	tablerowmerge : '向下合併單元格',
	tablecolmerge : '向右合併單元格',
	tablerowsplit : '分割欄',
	tablecolsplit : '分割列',
	tablecoldelete : '删除列',
	tablerowdelete : '删除欄',
	noColor : '自動',
	pleaseSelectFile : '請選擇文件。',
	invalidImg : "請輸入有效的URL。\n只允許jpg,gif,bmp,png格式。",
	invalidMedia : "請輸入有效的URL。\n只允許swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb格式。",
	invalidWidth : "寬度必須是數字。",
	invalidHeight : "高度必須是數字。",
	invalidBorder : "邊框必須是數字。",
	invalidUrl : "請輸入有效的URL。",
	invalidRows : '欄數是必須輸入項目，只允許輸入大於0的數字。',
	invalidCols : '列數是必須輸入項目，只允許輸入大於0的數字。',
	invalidPadding : '內距必須是數字。',
	invalidSpacing : '間距必須是數字。',
	invalidBorder : '边框必须为数字。',
	pleaseInput : "請輸入內容。",
	invalidJson : '伺服器發生故障。',
	uploadSuccess : '上傳成功。',
	cutError : '您的瀏覽器安全設置不允許使用剪下操作，請使用快捷鍵(Ctrl+X)完成。',
	copyError : '您的瀏覽器安全設置不允許使用剪下操作，請使用快捷鍵(Ctrl+C)完成。',
	pasteError : '您的瀏覽器安全設置不允許使用剪下操作，請使用快捷鍵(Ctrl+V)完成。',
	ajaxLoading : '加載中，請稍候 ...',
	uploadLoading : '上傳中，請稍候 ...',
	uploadError : '上傳錯誤',
	'plainpaste.comment' : '請使用快捷鍵(Ctrl+V)把內容貼到下方區域裡。',
	'wordpaste.comment' : '請使用快捷鍵(Ctrl+V)把內容貼到下方區域裡。',
	'code.pleaseInput' : 'Please input code.',
	'link.url' : 'URL',
	'link.linkType' : '打開類型',
	'link.newWindow' : '新窗口',
	'link.selfWindow' : '本頁窗口',
	'flash.url' : 'URL',
	'flash.width' : '寬度',
	'flash.height' : '高度',
	'flash.upload' : '上傳',
	'flash.viewServer' : '瀏覽',
	'media.url' : 'URL',
	'media.width' : '寬度',
	'media.height' : '高度',
	'media.autostart' : '自動播放',
	'media.upload' : '上傳',
	'media.viewServer' : '瀏覽',
	'image.remoteImage' : '網絡影像',
	'image.localImage' : '上傳影像',
	'image.remoteUrl' : '影像URL',
	'image.localUrl' : '影像URL',
	'image.size' : '影像大小',
	'image.width' : '寬度',
	'image.height' : '高度',
	'image.resetSize' : '原始大小',
	'image.align' : '對齊方式',
	'image.defaultAlign' : '未設定',
	'image.leftAlign' : '向左對齊',
	'image.rightAlign' : '向右對齊',
	'image.imgTitle' : '影像說明',
	'image.upload' : '瀏覽...',
	'image.viewServer' : '瀏覽...',
	'multiimage.uploadDesc' : 'Allows users to upload <%=uploadLimit%> images, single image size not exceeding <%=sizeLimit%>',
	'multiimage.startUpload' : 'Start upload',
	'multiimage.clearAll' : 'Clear all',
	'multiimage.insertAll' : 'Insert all',
	'multiimage.queueLimitExceeded' : 'Queue limit exceeded.',
	'multiimage.fileExceedsSizeLimit' : 'File exceeds size limit.',
	'multiimage.zeroByteFile' : 'Zero byte file.',
	'multiimage.invalidFiletype' : 'Invalid file type.',
	'multiimage.unknownError' : 'Unknown upload error.',
	'multiimage.pending' : 'Pending ...',
	'multiimage.uploadError' : 'Upload error',
	'filemanager.emptyFolder' : '空文件夾',
	'filemanager.moveup' : '至上一級文件夾',
	'filemanager.viewType' : '顯示方式：',
	'filemanager.viewImage' : '縮略圖',
	'filemanager.listImage' : '詳細信息',
	'filemanager.orderType' : '排序方式：',
	'filemanager.fileName' : '名稱',
	'filemanager.fileSize' : '大小',
	'filemanager.fileType' : '類型',
	'insertfile.url' : 'URL',
	'insertfile.title' : '文件說明',
	'insertfile.upload' : '上傳',
	'insertfile.viewServer' : '瀏覽',
	'table.cells' : '儲存格數',
	'table.rows' : '欄數',
	'table.cols' : '列數',
	'table.size' : '表格大小',
	'table.width' : '寬度',
	'table.height' : '高度',
	'table.percent' : '%',
	'table.px' : 'px',
	'table.space' : '內距間距',
	'table.padding' : '內距',
	'table.spacing' : '間距',
	'table.align' : '對齊方式',
	'table.textAlign' : '水平對齊',
	'table.verticalAlign' : '垂直對齊',
	'table.alignDefault' : '未設定',
	'table.alignLeft' : '向左對齊',
	'table.alignCenter' : '置中',
	'table.alignRight' : '向右對齊',
	'table.alignTop' : '靠上',
	'table.alignMiddle' : '置中',
	'table.alignBottom' : '靠下',
	'table.alignBaseline' : '基線',
	'table.border' : '表格邊框',
	'table.borderWidth' : '邊框',
	'table.borderColor' : '顏色',
	'table.backgroundColor' : '背景顏色',
	'map.address' : '住所: ',
	'map.search' : '尋找',
	'baidumap.address' : '住所: ',
	'baidumap.search' : '尋找',
	'baidumap.insertDynamicMap' : '插入動態地圖',
	'anchor.name' : '錨點名稱',
	'formatblock.formatBlock' : {
		h1 : '標題 1',
		h2 : '標題 2',
		h3 : '標題 3',
		h4 : '標題 4',
		p : '一般'
	},
	'fontname.fontName' : {
		'MingLiU' : '細明體',
		'PMingLiU' : '新細明體',
		'DFKai-SB' : '標楷體',
		'SimSun' : '宋體',
		'NSimSun' : '新宋體',
		'FangSong' : '仿宋體',
		'Arial' : 'Arial',
		'Arial Black' : 'Arial Black',
		'Times New Roman' : 'Times New Roman',
		'Courier New' : 'Courier New',
		'Tahoma' : 'Tahoma',
		'Verdana' : 'Verdana'
	},
	'lineheight.lineHeight' : [
		{'1' : '单倍行距'},
		{'1.5' : '1.5倍行距'},
		{'2' : '2倍行距'},
		{'2.5' : '2.5倍行距'},
		{'3' : '3倍行距'}
	],
	'template.selectTemplate' : '可選樣板',
	'template.replaceContent' : '取代當前內容',
	'template.fileList' : {
		'1.html' : '影像和文字',
		'2.html' : '表格',
		'3.html' : '项目清單'
	}
}, 'zh_TW');
