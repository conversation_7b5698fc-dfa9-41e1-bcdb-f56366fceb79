<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="">
  <meta name="keywords" content="">
<!--  <meta name="viewport" content="width=device-width, initial-scale=1">-->
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
  <link rel="stylesheet" href="/Public/home/<USER>/css/amazeui.min.css">
  <link rel="stylesheet" href="/Public/home/<USER>/css/app.css">


  <link rel="stylesheet" href="/Public/home/<USER>/css/all.css">



  

<title>资料信息 - 站长源码库（zzmaku.com） </title>

<link rel="stylesheet" href="/Public/home/<USER>/css/common.css">
<link rel="stylesheet" href="/Public/home/<USER>/css/myinfo.css">


<link rel="stylesheet" href="/Public/home/<USER>/css/iosSelect.css">

</head>
<body>

	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">资料信息</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
		
	</div>

	<div class="identity">
		<form action="" enctype="multipart/form-data">

			<div style="background-color: rgb(255, 245, 203); width: 100%; margin: 10px auto 0; padding: 5px 0;text-align: center;font-size: 15px;">填写真实有效的信息，审核才能通过</div>
			<div class="input_text_group">
              
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">单位地址</div>
						<div class="am-u-sm-8" id="company_select">
							<input type="hidden" name="company_province_code" data-id="0001" id="company_province_code" value="" data-province-name="">                     
							<input type="hidden" name="company_city_code" id="company_city_code" value="" data-city-name="">
							<input readonly="" type="text" data-province-code="" data-city-code="" data-district-code="" id="company_show" value="{$userinfo.dwaddess_ssq}" placeholder="请选择省市区">
						</div>
					</div>
				</div>
              
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">详细地址</div>
						<div class="am-u-sm-8">
							<input type="text" id='dwaddess_more' value="{$userinfo.dwaddess_more}" placeholder="例：正阳小区一栋505室">
						</div>
					</div>
				</div>
              
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">月收入/元</div>
						<div class="am-u-sm-8 f_number">
							<input type="number" id="dwysr" value="{$userinfo.dwysr}" placeholder="请输入月收入">
						</div>
					</div>
				</div>
              
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">现居住地址</div>
						<div class="am-u-sm-8" id="home_select">
							<input type="hidden" name="home_province_code" data-id="0001" id="home_province_code" value="" data-province-name="">                     
							<input type="hidden" name="home_city_code" id="home_city_code" value="" data-city-name="">
							<input readonly="" type="text" data-province-code="" data-city-code="" data-district-code="" id="home_show" value="{$userinfo.addess_ssq}" placeholder="请选择省市区">
						</div>
					</div>
				</div>
              
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">详细地址</div>
						<div class="am-u-sm-8">
							<input type="text" id="addess_more" value="{$userinfo.addess_more}" placeholder="例：正阳小区一栋505室">
						</div>
					</div>
				</div>
				
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">期望贷款额度</div>
						<div class="am-u-sm-8">
							<input type="text" id="qiwangedu" value="{$userinfo.qiwangedu}" placeholder="">
						</div>
					</div>
				</div>
				<div class="input_text_list" style="display:block;">
					<div class="am-g">
						<div class="am-u-sm-4">贷款用途</div>
						<div class="am-u-sm-8">
							<input type="text" id="yongtu" value="{$userinfo.yongtu}" placeholder="">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">已婚未婚</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="hunfou" id="hunfou" value="">                     
							<input readonly="" type="text" id="hunfoula" value="<?php 
							 switch($userinfo['hunfou']){
							     case 1:echo '已婚';break;
							     case 2:echo '未婚';break;
							     case 3:echo '离异';break;
							     
							 }
							?>" data-id="" data-value="" placeholder="">
						</div>
					</div>
				</div>
					<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">学历</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="xueli" id="xueli" value="">                     
							<input readonly="" type="text" id="xuelila" value="<?php 
							 switch($userinfo['hunfou']){
							     case 1:echo '小学';break;
							     case 2:echo '初中';break;
							     case 3:echo '中专/高中';break;
							     case 4:echo '专科';break;
							     case 5:echo '本科';break;
							     case 6:echo '硕士研究生';break;
							     case 7:echo '博士研究生';break;
							     case 8:echo '以上都不是';break;
							     
							 }
							?>" data-id="" data-value="" placeholder="">
						</div>
					</div>
				</div>
				
				
				
				
			</div>

			<div style="background-color: rgb(255, 245, 203); width: 100%; margin: 10px auto 0; padding: 5px 0;text-align: center;font-size: 15px;">直系亲属联系人</div>

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">姓名</div>
						<div class="am-u-sm-8">
							<input type="text" id="xingming1" value="{$userinfo.personname_1}" placeholder="请输入姓名">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">关系</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="family_id" id="familyId" value="">                     
							<input readonly="" type="text" id="zxqsname1" value="{$userinfo.persongx_1}" data-id="" data-value="" placeholder="请选择关系">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">手机号</div>
						<div class="am-u-sm-8 f_number">
							<input type="number" id="phone1" value="{$userinfo.personphone_1}" placeholder="请输入手机号">
						</div>
					</div>
				</div>
			</div>

			<div style="background-color: rgb(255, 245, 203); width: 100%; margin: 10px auto 0; padding: 5px 0;text-align: center;font-size: 15px;">其他联系人</div>

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">姓名</div>
						<div class="am-u-sm-8">
							<input type="text" id="xingming2" value="{$userinfo.personname_2}" placeholder="请输入姓名">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">关系</div>
						<div class="am-u-sm-8">
							<input type="hidden" name="other_id" id="otherId" value="">                     
							<input readonly="" type="text" id="zxqsname2" value="{$userinfo.persongx_2}" data-id="" data-value="" placeholder="请选择关系">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">手机号</div>
						<div class="am-u-sm-8 f_number">
							<input type="number" id="phone2" value="{$userinfo.personphone_2}" placeholder="请输入手机号">
						</div>
					</div>
				</div>
			</div>

			<div style="height: 70px;"></div>

			<div class="fix_bottom">
				<div class="am-g">
					<button type="button" class="am-btn am-btn-block" id="data-button" onclick="saveInfo();">
						确认提交
												</button>
				</div>
			</div>
		</form>
	</div>

	<div class="container"></div>
	
	<div class="message">
		<p></p>
	</div>

<script type="text/javascript">
    document.documentElement.addEventListener('touchmove', function(event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    }, false);
</script>
<script src="/Public/home/<USER>/js/jquery3.2.min.js"></script>
<!--<![endif]-->
<script src="/Public/home/<USER>/js/amazeui.min.js"></script>
<script type="text/javascript" src="/Public/home/<USER>/js/zepto.js"></script>
<script type="text/javascript" src="/Public/home/<USER>/js/areaData_v2.js"></script>
<script type="text/javascript" src="/Public/home/<USER>/js/iosSelect.js"></script>

<script type="text/javascript">
	// 公司地址选择
    var companySelectDom = $('#company_select');
    var companyShowDom = $('#company_show');
    var companyProvinceCodeDom = $('#company_province_code');
    var companyCityCodeDom = $('#company_city_code');
    companySelectDom.bind('click', function () {
        var sccode = companyShowDom.attr('data-city-code');
        var scname = companyShowDom.attr('data-city-name');

        var oneLevelId = companyShowDom.attr('data-province-code');
        var twoLevelId = companyShowDom.attr('data-city-code');
        var threeLevelId = companyShowDom.attr('data-district-code');
        var iosSelect = new IosSelect(3, 
            [iosProvinces, iosCitys, iosCountys],
            {
                title: '单位地址',
                itemHeight: 35,
                showAnimate:true,
                relation: [1, 1],
                oneLevelId: oneLevelId,
                twoLevelId: twoLevelId,
                threeLevelId: threeLevelId,
                callback: function (selectOneObj, selectTwoObj, selectThreeObj) {
                    companyProvinceCodeDom.val(selectOneObj.id); 
                    companyProvinceCodeDom.attr('data-province-name', selectOneObj.value);
                    companyCityCodeDom.val(selectTwoObj.id);
                    companyCityCodeDom.attr('data-city-name', selectTwoObj.value);

                    companyShowDom.attr('data-province-code', selectOneObj.id);
                    companyShowDom.attr('data-city-code', selectTwoObj.id);
                    companyShowDom.attr('data-district-code', selectThreeObj.id);
                    companyShowDom.val(selectOneObj.value + ' ' + selectTwoObj.value + ' ' + selectThreeObj.value);
                }
        });
	});


	// 居住地址选择
	var homeSelectDom = $('#home_select');
    var homeShowDom = $('#home_show');
    var homeProvinceCodeDom = $('#home_province_code');
    var homeCityCodeDom = $('#home_city_code');
    homeSelectDom.bind('click', function () {
        var sccode = homeShowDom.attr('data-city-code');
        var scname = homeShowDom.attr('data-city-name');

        var oneLevelId = homeShowDom.attr('data-province-code');
        var twoLevelId = homeShowDom.attr('data-city-code');
        var threeLevelId = homeShowDom.attr('data-district-code');
        var iosSelect = new IosSelect(3, 
            [iosProvinces, iosCitys, iosCountys],
            {
                title: '现居住地址',
                itemHeight: 35,
                showAnimate:true,
                relation: [1, 1],
                oneLevelId: oneLevelId,
                twoLevelId: twoLevelId,
                threeLevelId: threeLevelId,
                callback: function (selectOneObj, selectTwoObj, selectThreeObj) {
                    homeProvinceCodeDom.val(selectOneObj.id); 
                    homeProvinceCodeDom.attr('data-province-name', selectOneObj.value);
                    homeCityCodeDom.val(selectTwoObj.id);
                    homeCityCodeDom.attr('data-city-name', selectTwoObj.value);

                    homeShowDom.attr('data-province-code', selectOneObj.id);
                    homeShowDom.attr('data-city-code', selectTwoObj.id);
                    homeShowDom.attr('data-district-code', selectThreeObj.id);
                    homeShowDom.val(selectOneObj.value + ' ' + selectTwoObj.value + ' ' + selectThreeObj.value);
                }
        });
	});

	var relation = [
		{'id': '10001', 'value': '父母'},
		{'id': '10002', 'value': '兄妹'},
		{'id': '10003', 'value': '夫妻'},
		{'id': '10004', 'value': '朋友'},
		{'id': '10005', 'value': '同事'}
	];

	// 其他联系人关系
	var showOtherDom = document.querySelector('#zxqsname2');
    var otherIdDom = document.querySelector('#otherId');
    showOtherDom.addEventListener('click', function () {
        var otherId = showOtherDom.dataset['id'];
        var otherName = showOtherDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [relation],
            {
                container: '.container',
                title: '其他联系人-关系',
                itemHeight: 50,
                itemShowCount: 3,
                showAnimate:true,
                oneLevelId: otherId,
                callback: function (selectOneObj) {
                    otherIdDom.value = selectOneObj.id;
                    showOtherDom.value = selectOneObj.value;
                    showOtherDom.dataset['id'] = selectOneObj.id;
                    showOtherDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	
	
	var hun = [
		{'id': '1', 'value': '已婚'},
		{'id': '2', 'value': '未婚'},
		{'id': '3', 'value': '离异'},
		
	];

	// 其他联系人关系
	var hunfoulaDom = document.querySelector('#hunfoula');
    var hunfouDom = document.querySelector('#hunfou');
    hunfoulaDom.addEventListener('click', function () {
        var hunId = hunfoulaDom.dataset['id'];
        var hunName = hunfoulaDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [hun],
            {
                container: '.container',
                title: '最高学历',
                itemHeight: 50,
                itemShowCount: 3,
                showAnimate:true,
                oneLevelId: hunId,
                callback: function (selectOneObj) {
                    hunfouDom.value = selectOneObj.id;
                    hunfoulaDom.value = selectOneObj.value;
                    hunfoulaDom.dataset['id'] = selectOneObj.id;
                    hunfoulaDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	
	
	
	var xue = [
		{'id': '1', 'value': '小学'},
		{'id': '2', 'value': '初中'},
		{'id': '3', 'value': '中专/高中'},
		{'id': '4', 'value': '专科'},
		{'id': '5', 'value': '本科'},
		{'id': '6', 'value': '硕士研究生'},
		{'id': '7', 'value': '博士研究生'},
		{'id': '8', 'value': '以上都不是'},
	];

	// 其他联系人关系
	var xuelilaDom = document.querySelector('#xuelila');
    var xueliDom = document.querySelector('#xueli');
    xuelilaDom.addEventListener('click', function () {
        var xueId = xuelilaDom.dataset['id'];
        var xueName = xuelilaDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [xue],
            {
                container: '.container',
                title: '已婚未婚',
                itemHeight: 50,
                itemShowCount: 3,
                showAnimate:true,
                oneLevelId: xueId,
                callback: function (selectOneObj) {
                    xueliDom.value = selectOneObj.id;
                    xuelilaDom.value = selectOneObj.value;
                    xuelilaDom.dataset['id'] = selectOneObj.id;
                    xuelilaDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	
	// 直系亲属联系人关系
	var showFamilyDom = document.querySelector('#zxqsname1');
    var familyIdDom = document.querySelector('#familyId');
    showFamilyDom.addEventListener('click', function () {
        var familyId = showFamilyDom.dataset['id'];
        var familyName = showFamilyDom.dataset['value'];

        var bankSelect = new IosSelect(1, 
            [relation],
            {
                container: '.container',
                title: '直系亲属-关系',
                itemHeight: 50,
                itemShowCount: 3,
                showAnimate:true,
                oneLevelId: familyId,
                callback: function (selectOneObj) {
                    familyIdDom.value = selectOneObj.id;
                    showFamilyDom.value = selectOneObj.value;
                    showFamilyDom.dataset['id'] = selectOneObj.id;
                    showFamilyDom.dataset['value'] = selectOneObj.value;
                },
                fallback: function () {
                    console.log(1);
                },
                maskCallback: function () {
                    console.log(2);
                }
        });
	});
	
   // 弹窗

    // 倒计时
    function myTimer(){
		var sec = 3;
		var timer;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }	
	function checkval(val_){
		if(val_ == '' || val_ == null){
			return false;
		}else{
			return true;
		}
	}  
	
	// 身份证
	function checksfz(val_) {
		var sfz_zz = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
		if (sfz_zz.test(val_)) {
			return true;
		} else {
			return false;
		}
	}
	
	// 姓名正则
	function checkname(val_) {
		var name_zz = /^[\u4E00-\u9FA5]{2,4}$/;
		if (name_zz.test(val_)) {
			return true;
		} else {
			return false;
		}
	}
	
	// 手机号码正则
	function checkphone(val_) {
		var phone_zz = /^1[3456789]\d{9}$/;
		if (phone_zz.test(val_)) {
			return true;
		} else {
			return false;
		}
	}
	
//保存资料
function saveInfo(){
	
	var dwaddess_ssq = $("#company_show").val();
	var dwaddess_more = $("#dwaddess_more").val();
	var dwysr = $("#dwysr").val();
	var addess_ssq = $("#home_show").val();
	var addess_more = $("#addess_more").val();
	var xueli = $("#xueli").val();
	var hunfou = $("#hunfou").val();
	var yongtu = $("#yongtu").val();
	var qiwangedu = $("#qiwangedu").val();
	var personname_1 = $("#xingming1").val();
	var personname_2 = $("#xingming2").val();
	var personphone_1 = $("#phone1").val();
	var personphone_2 = $("#phone2").val();
	var persongx_1 = $("#zxqsname1").val();
	var persongx_2 = $("#zxqsname2").val();
	mesg_default();
	if( checkval(persongx_1) && checkval(persongx_2) && checkval(personname_1) && checkval(personname_2) && checkval(personphone_1) && checkval(personphone_2) && checkname(personname_1) && checkname(personname_2) &&checkphone(personphone_1) && checkphone(personphone_2)){
		$.post(
			"{:U('Info/unitinfo')}",
			{
				
				dwaddess_ssq:dwaddess_ssq,
				dwaddess_more:dwaddess_more,
				dwysr:dwysr,
				addess_ssq:addess_ssq,
				addess_more:addess_more,
				yongtu:yongtu,
				qiwangedu:qiwangedu,
				xueli:xueli,
				hunfou:hunfou,
				personname_1:personname_1,
				personname_2:personname_2,
				personphone_1:personphone_1,
				personphone_2:personphone_2,
				persongx_1:persongx_1,
				persongx_2:persongx_2
			},
			function (data,state){
				if(state != "success"){
					message("请求数据失败,请重试!");
				}else if(data.status == 1){
					message("保存成功!");
					window.location.href = "{:U('Info/bankinfo')}";
				}else{
					message(data.msg);
				}
			}
		);
	}else{
		message("资料填写不完整,请检查!");
	}
}
</script>

</body>
</html>