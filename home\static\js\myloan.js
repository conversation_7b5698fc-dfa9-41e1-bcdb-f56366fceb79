$(function () {
	var timer,
		msg,
		ordernumber,
        rll,
        addtime
		
	;

	
	// 弹窗

    // 倒计时
    function myTimer(){
        var sec = 3;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }

    $(".tx-show").unbind('click').on('click',function () {
        ordernumber = $(this).data('ordernumber');
        $.ajax({
            type:'post',
            url:'show_loan',
            data:{
                ordernumber : ordernumber
            },
            success: function(data){

                if (data.orderstatus == 2){
                    $(".confirmnumber").html(Math.round(data.confirmnumber));
                    $('.pp_ordernumber').html(data.ordernumber);
                    $(".confirmshm").html(data.confirmshm);
                    var $modal = $('#doc-modal-1');
                  /*  $modal.modal({
                        closeViaDimmer: 1,
                        dimmer:1
                    });
                  */
                    console.log(data.orderstatus);
                }
            }
        });
    });
	
	$(".loan-show").unbind('click').on('click',function(){
               orderstatus=$(this).data('status');
				$(".status_2").css("color","#c2c2c2");
                $(".status_11").css("color","#c2c2c2");
                $(".status_2 .point").css("border","solid 2px #c2c2c2");
                $(".status_2 .line").css("border","solid 1px #c2c2c2");
                $(".status_11 .point").css("border","solid 2px #c2c2c2");
                $(".line_right").css("border","solid 1px #c2c2c2");
				$(".status_"+orderstatus).css("color","#0c7cb5");
				$(".status_"+orderstatus+" .point").css("border","solid 2px #0c7cb5");
                $(".status_"+orderstatus+" .line").css("border","solid 1px #0c7cb5");

                if (orderstatus == 4){
                    $(".status_2").css("color","#f01f1f");
                    $(".status_2 .point").css("border","solid 2px #f01f1f");
                    $(".status_2 .line").css("border","solid 1px #f01f1f");
                }
                if (orderstatus == 11){
                    $(".line_right").css("border","solid 1px #0c7cb5");
                    $(".confirmnumber").html(Math.round(data.confirmnumber));
                    $('.pp_ordernumber').html(data.ordernumber);
                    $(".confirmshm").html(data.confirmshm);
                    var $modal = $('#doc-modal-1');
                    $modal.modal({
                        closeViaDimmer: 0,
                        dimmer:0
                    });
                    console.log(orderstatus);
                }   
		});

    // $(".contract-show").unbind('click').on('click',function () {
    //     ordernumber = $(this).data('ordernumber');
    //     $.ajax({
    //         type:'post',
    //         url:'contract_show',
    //         data:{
    //             ordernumber : ordernumber
    //         },
    //         success: function(data){
    //             $(".contract_show").html(data);
    //         }
    //     });

    // });
});