<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title><Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="description" content="  记录你读过的、想读和正在读的书，顺便打分，添加标签及个人附注，写评论。根据你的口味，推荐适合的书给你。  ">
<meta name="Keywords" content="  豆瓣读书,新书速递,畅销书,书评,书单  ">
<link rel="stylesheet" type="text/css" href="/Public/home/<USER>/mui.min.css">
<link rel="stylesheet" type="text/css" href="/Public/home/<USER>/feiqi-ee5401a8e6.css">
<link rel="stylesheet" type="text/css" href="/Public/home/<USER>/newpay-bb7fcb5546.css">
<link rel="stylesheet" type="text/css" href="/Public/home/<USER>/newindex-09d04b32f3.css">
</head>
<body>
    <!-- header -->
    <header class="mui-bar mui-bar-nav hnav">
		<a href="/index.php?m=Index&a=index" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
		<h1 class="mui-title">开通VIP</h1>
	</header>
	<!-- header end-->
<div class="mui-content">
  	<div>
      	<img src="/Public/home/<USER>/vip_banner.png" style="width:100%;" />
     </div>
	<article class="mt10 jiekuan" style="margin-top:0;">
		<div class="oktit">温馨提醒</div>
      	<div style="color: #aaa; font-size: 15px;padding: 10px 10px; text-indent: 20px;">选择支付项，在打开的图片直接截图保存，使用微信扫码支付即可。</div>
		<div class="cf okdan" style="border-bottom:none;">
			<div class="oktable" style=" padding-bottom:5px; margin: 0 10px;">
              <span class="fc9 listit" style="width: 38%;"><label><input style="-webkit-appearance:radio; width:15px; height:15px; border:solid #aaa 1px;" checked="checked" type="radio" rel="{:C('cfg_shenhefei')}" name="viptype" value="1" /> ￥ {:C('cfg_shenhefei')}</label></span>
				<span>中级VIP</span>
			</div>
			<div class="oktable" style="border-bottom:solid #ccc 1px; padding-bottom:15px; margin: 0 10px">
				<span class="fc9 listit" style="width: 38%;"><label><input style="-webkit-appearance:radio; width:15px; height:15px; border:solid #aaa 1px;" type="radio" name="viptype" rel="{:C('cfg_gaoji')}" value="2" /> ￥ {:C('cfg_gaoji')}</label></span>
				<span>高级VIP </span>
			</div>
		</div>
      <div class="oktit" style="font-size:15px;">支付方式</div>
      		<div class="oktable" style="border-bottom:solid #ccc 1px; padding-bottom:15px; margin: 0 10px 15px;">
				<span class="fc9 listit"><label><input style="-webkit-appearance:radio; width:15px; height:15px; border:solid #aaa 1px;" checked="checked" type="radio" name="paytype" value="1" /> 微信</label></span>
			</div>
			<!--<div class="oktable" style="border-bottom:solid #ccc 1px; padding-bottom:15px; margin: 0 10px 15px;">
				<span class="fc9 listit"><label><input style="-webkit-appearance:radio; width:15px; height:15px; border:solid #aaa 1px;" type="radio" checked="checked" name="paytype" value="2" /> 支付宝</label></span>
			</div>-->
      	</div>
	</article>

			
			<div class="protit sevagreee " style="background-color:#f5f5f9;">
	            <a class="logBtn" href="javascript:subForms();">立即购买</a>
	        </div>
  			<div class="cf mt20" style="margin-top:10px;">
                <label class="fl rev">
                    <input type="checkbox" id="xieyi" checked="checked">
                    <em></em>
                </label>
                <span class="fl arge" onclick="xieyiChange();">
                	同意 《用户购买协议》                </span>
            </div>
</div>

<div class="deowin2" style="display:none;" id="deowin31">
    <div class="deocon2">
        <div class="divpad2" style="text-align:center;height:110px">
            <p class='tex' style="color: #4c4c4c;line-height: 30px;font-size:16px;"></p>
        </div>
        <div class="wobtn">
            <!-- 一个按钮用这个结构 -->
                <a id="winbtn3" href="javascript:;">确定</a>
        </div>
    </div>
</div>
<div class="emask" id="mask3" style="display: none;"></div>
<script src="/Public/home/<USER>/jquery-1-fe84a54bc0.11.1.min.js"></script>
   <script>
if(('standalone' in window.navigator)&&window.navigator.standalone){
        var noddy,remotes=false;
        document.addEventListener('click',function(event){
                noddy=event.target;
                while(noddy.nodeName!=='A'&&noddy.nodeName!=='HTML') noddy=noddy.parentNode;
                if('href' in noddy&&noddy.href.indexOf('http')!==-1&&(noddy.href.indexOf(document.location.host)!==-1||remotes)){
                        event.preventDefault();
                        document.location.href=noddy.href;
                }
        },false);
}
</script>
<script>
var tongyi = false;
var isload = false;//避免重复提交
$(function(){
	$("#winbtn3").click(function(){
		$("#deowin31").hide();
		$('#mask3').hide();
	});
	$("#xieyi").click(function(){
		if(tongyi){tongyi = false;}else{tongyi = true;}
	});
});
function xieyiChange(){
	$("#xieyi").click();
}
function subForms(){
	var viptype = $('input:radio[name=viptype]:checked').val();
  	var paytype = $('input:radio[name=paytype]:checked').val();
  	var vipprice = $('input:radio[name=viptype]:checked').attr("rel");
  	var ordernum = "I310886073252708";
  	if(ordernum==0){
    	alert("无效的订单号!")
    }else{
      
      if(paytype==1){
        $(".saotit span").text("微信");
      }
      if(paytype==2){
        $(".saotit span").text("支付宝");
      }
      
      $(".saotit b").text(vipprice);
      
      changebanks(paytype);
      //window.location.href="/newpay/epayapi.php?ordernum="+ordernum+"&vipprice="+vipprice+"&paytype="+paytype+"&viptype="+viptype;
    }
  	
  	
}
function subForm(){
    if(!tongyi){
        $(".tex").html('请您同意并勾选协议');
        $("#deowin31").show();
        $('#mask3').show();
        return false;
    }
    if(isload){
        $(".tex").html('请勿重复提交!');
        $("#deowin31").show();
        $('#mask3').show();
    }else{
    	isload = true;
	    //提交获取支付订单号
	    $.post(
	    	"/index.php?m=Order&a=daikuan&trueorder=1",
	    	{
	    		money: "",
	    		month:""
	    	},
	    	function (data,state){
	    		if(state != "success"){
			        $(".tex").html('请求数据失败,请重试!');
			        $("#deowin31").show();
			        $('#mask3').show();
	    		}else if(data.status != 1){
			        $(".tex").html(data.msg);
			        $("#deowin31").show();
			        $('#mask3').show();
	    		}else{
	    			window.location.href = data.payurl;
	    		}
	    	}
	    );
	    isload = false;
    }
}
</script>
<div style="display: none;">
	</div>
  
  <style type="text/css">
  .NoteBox{ display:none;}
  .TanBox{ display:none;}
  .TanZhe{ position:fixed; left:0; top:0; width:100%; height:100%; background:#000; z-index:999; filter:alpha(opacity=50); /*支持 IE 浏览器*/-moz-opacity:0.50; /*支持 FireFox 浏览器*/opacity:0.50;}
  .TanMain{ position:fixed; left:50%; top:50%; width:810px; height:415px; margin-left:-405px; margin-top:-200px; background:#FFF; z-index:9999; border-radius:7px;}
  .TanMain h2{ height:40px; line-height:40px; font-size:13px; padding-left:20px;}
  .CloseBtn{ color:#000; font-size:18px; display:block; width:18px; height:18px; position:absolute; right:5px; top:5px;cursor:pointer;}
  .TanBox2 .TanMain{width:310px; height:325px; margin-left:-155px; margin-top:-160px;}
  .btnarea input{ background: #7457ea; height:46px; line-height:46px; color:#fff; display:block; margin:0 auto; border-radius: 6px;}
  .erweiimg{ width:200px; height:200px; margin:0 auto;}
  .saotit{ text-align:center; color:#f00;}
  .saotit b{ color:blue;}
    .erweiimg{ padding:10px;}
</style>
<div class="TanBox TanBox2">
  <div class="TanZhe"></div>
<div class="TanMain" id="changestatus_div">
  <h2>请扫码支付</h2><span class="CloseBtn CloseBtn2">&times;</span>
  <div class="erweima">
    <div class="saotit">请用手机<span>微信</span>支付金额 <b></b> 元</div>
    <div class="erweiimg"><img src="" id="erweimaimg" style="width:100%;" /></div>
    <!--div class="btnarea"><input type="button" onclick="savebank();" class="btn" value="已经支付" style="height:30px;line-height: 10px; font-size: 15px;" /></div-->
  </div>
  <input type="hidden" id="url2" value="/Upload/wxfk.jpg">
  <!--<input type="hidden" id="url2" value="/Upload/zfbfk.jpg">-->
</div>
</div>
  <script>
    function changebanks(paytype){
	var url1=$("#url1").val();
	var url2=$("#url2").val();
      	if(paytype==2){
        	$("#erweimaimg").attr("src",url1);
        }
      	if(paytype==1){
        	$("#erweimaimg").attr("src",url2);
        }
        $(".TanBox2").fadeIn();
    }
    $('.CloseBtn2').click(function(){
      		$(".TanBox2").fadeOut();
      });
  function savebank(){
    	window.location.href ="/index.php?m=User&a=index";    	
    }
  </script>
  
</body>
</html>