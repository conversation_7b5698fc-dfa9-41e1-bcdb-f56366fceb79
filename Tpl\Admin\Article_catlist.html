
<div class="layui-table-tool">
    <h3  class="layui-table-tool-self">

    <a href="{:U(GROUP_NAME.'/Article/addcat')}" class="actionBtn add  layui-btn">

        添加分类

    </a>

</h3>
</div>
<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

    <tr>

        <th width="30">ID</th>

        <th width="120" align="left">分类名称</th>

        <th width="120" align="left">上级分类</th>

        <th width="140" align="left">添加时间</th>

        <th width="60" align="center">排序</th>

        <th align="center">操作</th>

    </tr>

    <foreach name="data" item="vo">

        <tr>

            <td align="center">{$vo.id}</td>

            <td align="left">{$vo.name}</td>

            <td align="left">{$vo.lastname}</td>

            <td>{$vo.addtime|date="Y/m/d",###}</td>

            <td align="center">{$vo.sort}</td>

            <td align="center">

                <button class="layui-btn layui-btn-sm"> <a
                        href="{:U(GROUP_NAME.'/Article/addcat',array('pid'=>$vo['id']))}"><i class="layui-icon"></i>子分类</a> </button>

                <button class="layui-btn layui-btn-sm"><a href="{:U(GROUP_NAME.'/Article/index',array('cid'=>$vo['id']))}">查看内容列表</a>
                </button>

                <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="{:U(GROUP_NAME.'/Article/editcat',array('cid'=>$vo['id']))}"><i class="layui-icon"></i>编辑</a>
                </button>

                <button class="layui-btn layui-btn-sm layui-btn-danger"><a
                        href="javascript:delCat('{$vo.name}','{:U(GROUP_NAME.'/Article/delcat',array('cid'=>$vo['id']))}');"><i class="layui-icon"></i>删除</a></button>

            </td>

        </tr>

    </foreach>



</table>

<script>

    function delCat(catname, jumpurl) {

        layer.confirm(

            '确定要删除分类:[' + catname + ']吗?',

            function () {

                window.location.href = jumpurl;

            }

        );

    }

</script>