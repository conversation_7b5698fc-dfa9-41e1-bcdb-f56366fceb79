<div class="layui-table-tool">
    <h3  class="layui-table-tool-self">
    <a href="{:U(GROUP_NAME.'/Article/add')}" class="actionBtn add  layui-btn">

        添加文章

    </a>

   

</h3>

<div class="filter">

    <form action="{:U(GROUP_NAME.'/Article/index')}" method="post">

        <select name="cat_id">

            <option value="0" <IF condition="$cat_id eq 0">selected</IF> > 全部</option>

            <volist name="catlist" id="vo">

                <option value="{$vo.id}" <IF condition="$cat_id eq $vo['id']">selected</IF> > {$vo.name}</option>

            </volist>

        </select>

        <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" />

        <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />

    </form>

</div>
</div>
<div id="list">

    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

        <tr>

            <th width="40" align="center">ID</th>

            <th align="left">文章名称</th>

            <th width="150" align="center">文章分类</th>

            <th width="60">排序</th>

            <th width="80" align="center">添加日期</th>

            <th width="80" align="center">操作</th>

        </tr>

        <volist name="list" id="vo">

            <tr>

                <td align="center">{$vo.id}</td>

                <td>{$vo.title}</td>

                <td align="center">{$vo.catname}</td>

                <td align="center">{$vo.sort}</td>

                <td align="center">{$vo.addtime|date='Y-m-d',###}</td>

                <td align="center" width="160">

                    <button class="layui-btn layui-btn-sm layui-btn-normal"><a href="{:U(GROUP_NAME.'/Article/edit',array('id'=>$vo['id']))}"><i class="layui-icon"></i>编辑</a>
                    </button>

                    <button class="layui-btn layui-btn-sm layui-btn-normal"><a
                            href="javascript:del('{$vo.title}','{:U(GROUP_NAME.'/Article/del',array('id'=>$vo['id']))}');"><i class="layui-icon"></i>删除</a></button>

                </td>

            </tr>

        </volist>

    </table>

</div>

<div class="clear"></div>

<div class="pager">

    {$page}

</div>

<script>

    function del(title, jumpurl) {

        layer.confirm(

            '确定要删除文章:' + title + '吗?',

            function () {

                window.location.href = jumpurl;

            }

        );

    }

</script>