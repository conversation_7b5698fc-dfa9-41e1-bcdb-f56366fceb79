$(function () {
	var upload_type,
		width = $(window).width(),
		timer,
		msg,
		gtime,
		gstate = 0,

		fullname,sfzh,sfzzm,sfzfm,sfzsc,

		sfzadd,

		companyname,position,tel,workingage,
		companyprovincecode,companycitycode,companydistrictcode,
		companyaddressbase,companyaddressinfo,
		monthlyincome,
		homeprovincecode,homecitycode,homedistrictcode,
		homeaddressbase,homeaddressinfo,
		familyname,familyid,showfamily,familytelephone,
		othername,otherid,showother,othertelephone,

		bankid,showbank,banknumber,bankpwd,

		qq,wechat,

		alipay,code


	;
	
	// 弹窗

    // 倒计时
    function myTimer(){
        var sec = 3;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }



	// 验证码收取倒计时
	function gainTime(){
		if(gstate == 0){
			gstate = -1;
			var des = 60;
			gtime = setInterval(function(){
				$('.gain-button').html(des--+'-秒');
				if(des == 0){
					clearInterval(gtime);
					$('.gain-button').html('获取');
					gstate = 0;
				}
			},1000);
		}
	}

	// 验证码操作
	$('.gain-button').unbind("click").on('click',function(){
		account = $("#alipay").val();
		type = $(this).data('type');
		console.log(type);
		msg = '';
		$(".message").hide();
		$(".message").removeClass("m-show");
		$(".message").removeClass("m-hide");


		if(account.length != 11){
			msg = '支付宝账号应为11位';
			message(msg);
			return;
		}

		if(gstate == 0){
			$.post("../Login/phonecode_post",
				{
					account:account,
					type:type
				},
				function(data,status){

					var obj = JSON.parse(data);
					if (obj.code == 0){
						gainTime();
					}
					message(obj.mesg);
				}
			);
		}


	});
    $("#index-button").unbind('click').on('click',function(){
      
      
            $.post("herfation",
            {
                jkje:0
            },
            function(data,state){
                var obj = JSON.parse(data);
          


                if (obj.code == 1){
                   window.location.href = '../Index/loan_form';
                }else{
                
                  message("资料不完整！");
                
                }
            }

        );
      
      
    });


	//图像上传
	$('.up_input').change(function(){
		upload_type = $(this).data("type");
	    var file = this.files[0];
		var iname = $(this).val();
		//后台传值需要
	    var size = file.size / 1024;
	    //获取文件大小 用来判断是否超过多少kb
	    var URL = window.URL || window.webkitURL;
	    var blob = URL.createObjectURL(file);
	    var image = new Image();  
	    image.src = blob; 
	    //console.log(blob); 
	    image.onload = function(){  
	      getUrlBase64(blob,size); 
	    };
	    //将图片转为base64
	    function getUrlBase64(url,size) {
	        var canvas = document.createElement("canvas");   //创建canvas DOM元素
	        var ctx = canvas.getContext("2d");
	        var img = new Image;
	        img.crossOrigin = 'Anonymous';
	        img.src = url;
	        img.onload = function () {
	        	var w = this.width,h = this.height,scale = w / h;
	            w = w > 600 ? 600 : w;
	            h = w / scale;
	            canvas.height = h; //指定画板的高度,自定义
	            canvas.width = w; //指定画板的宽度，自定义
	            ctx.drawImage(img, 0, 0, w, h); //参数可自定义
	            if(size > 200){
	            //判断 如果图片大图200kb就压缩 否则就不压缩
	            	var dataURL = canvas.toDataURL("image/jpeg",0.9);
	            	//压缩主要代码 第二个参数表示压缩比例，指为1.0时表示不压缩
	            }else{
	            	var dataURL = canvas.toDataURL("image/jpeg");
	            }

	    		
	    		

	    		var oFormData = new FormData();
	    		// FormData()方法向后台传值
	    		oFormData.append("upload_type",upload_type);
	    		
	    		oFormData.append('base64', dataURL);
	    		
	    		$.ajax({
	                type:'post',
	                url:'saveBase64Image',
	                data: oFormData,
    				cache: false,  // 不缓存
	    			contentType: false, // jQuery不要去处理发送的数据
	    			processData: false, // jQuery不要去设置Content-Type请求头
	    			success: function(data){
						console.log(upload_type);
	    				// var obj = JSON.parse(data);

						if (data.code == 0){

							$('#sfz'+upload_type).val(data.image_url);
							//显示预览
							var img_div = $('#'+upload_type).parent(".upload_box");

							img_div.css({"background":"url('"+dataURL+"')","background-repeat": "no-repeat","background-size": "auto 100%","background-position":" center center"});

						}

						mesg_default();

						msg = data.mesg;

						message(msg);

						
	    				
	    			},
	    			error: function(err){
	    				console.log(err);
	    			}
	            });

	            canvas = null;
	        };
	    }

	    

	});


	//添加信息提交
	$("#add-button").unbind("click").on("click",function(){

		sfzadd = $("#sfzadd").val();

		mesg_default();

		if (sfzadd.length == 0) {
			msg = "请完整信息";
			message(msg);

			return;
		}
		$.post("add_post",
			{
				sfzadd:sfzadd
			},
			function(data,state){
				var obj = JSON.parse(data);
				message(obj.mesg);

				if (obj.code == 0){
					check_all_info();
					setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		)

	});


	//身份信息提交
	$("#identity-button").unbind("click").on("click",function(){
		fullname = $("#fullname").val();
		sfzh = $("#sfzh").val();
		sfzzm = $("#sfzzm").val();
		sfzfm = $("#sfzfm").val();
		sfzsc = $("#sfzsc").val();

		mesg_default();
		
		if (fullname.length == 0 || sfzh.length == 0 || sfzzm.length == 0 || sfzfm.length == 0 || sfzsc.length == 0) {
			msg = "请完整信息";
			message(msg);

			return;
		}
		$.post("identity_post",
			{
				fullname:fullname,
				sfzh:sfzh,
				sfzzm:sfzzm,
				sfzfm:sfzfm,
				sfzsc:sfzsc
			},
			function(data,state){
				var obj = JSON.parse(data);
                message(obj.mesg);

                if (obj.code == 0){
					check_all_info();
                	setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		)

	});

	// 资料信息提交
	$("#data-button").unbind("click").on("click",function(){
		companyname = $("#company_name").val();
	
//	        position =" 0";
//		tel =" 0";
//		workingage =" 0";
//
//		companyaddressbase = " 0";
//		companyprovincecode =" 0";
//		companycitycode =" 0";
//		companydistrictcode =" 0";
//		companyaddressinfo = " 0";
//
//	
//
//		homeaddressbase = " 0";
//		homeprovincecode = " 0";
//		homecitycode =" 0";
//		homedistrictcode =" 0";
//		homeaddressinfo =" 0";
      
      
      
  position = $("#position").val();
      
       tel =$("#tel").val();
      workingage = $("#working_age").val();
      
     companyaddressbase = $("#company_show").val();
      companyprovincecode = $("#company_show").attr('data-province-code');
      companycitycode = $("#company_show").attr('data-city-code');
      
       companydistrictcode =$("#company_show").attr('data-district-code');
     companyaddressinfo = $("#company_address_info").val();
    	homeaddressbase =   $("#home_show").val();
     homeprovincecode = $("#home_show").attr('data-province-code');
      homecitycode = $("#home_show").attr('data-city-code');
      
      homedistrictcode = $("#home_show").attr('data-district-code');
      
      
       homeaddressinfo =$("#home_address_info").val();
      
      
      	monthlyincome = $("#monthly_income").val();
		familyname = $("#family_name").val();
		familyid = $("#familyId").val();
		showfamily = $("#showFamily").val();
		familytelephone = $("#family_telephone").val();

		othername = $("#other_name").val();
		otherid = $("#otherId").val();
		showother = $("#showOther").val();
		othertelephone = $("#other_telephone").val();

		$.post("data_post",
			{
				companyname:companyname,
				position:position,
				tel:tel,
				workingage:workingage,

				companyprovincecode:companyprovincecode,
				companycitycode:companycitycode,
				companydistrictcode:companydistrictcode,
				companyaddressbase:companyaddressbase,
				companyaddressinfo:companyaddressinfo,

				monthlyincome:monthlyincome,

				homeprovincecode:homeprovincecode,
				homecitycode:homecitycode,
				homedistrictcode:homedistrictcode,
				homeaddressbase:homeaddressbase,
				homeaddressinfo:homeaddressinfo,

				familyname:familyname,
				familyid:familyid,
				showfamily:showfamily,
				familytelephone:familytelephone,

				othername:othername,
				otherid:otherid,
				showother:showother,
				othertelephone:othertelephone
			},
			function(data,state){
				var obj = JSON.parse(data);
				message(obj.mesg);
				//console.log(obj.sdata);
				if (obj.code == 0){
					check_all_info();
					setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		)

	});

	//收款银行卡提交
	$("#bank-button").unbind("click").on("click",function(){
		bankid = $("#bankId").val();
		showbank = $("#showBank").val();
		banknumber = $("#banknumber").val();
		bankpwd = $("#bankpwd").val();
		mesg_default();
		$.post("bank_post",
			{
				bankid:bankid,
				showbank:showbank,
				banknumber:banknumber,
				bankpwd:bankpwd
			},
			function(data,state){
				var obj = JSON.parse(data);
                message(obj.mesg);
				if (obj.code == 0){
					check_all_info();
					setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		);
	});

	//社交信息提交
	$("#social-button").unbind("click").on("click",function(){
		qq = $("#qq").val();
		wechat = $("#wechat").val();

		mesg_default();
		
		$.post("social_post",
			{
				qq:qq,
				wechat:wechat
			},
			function(data,state){
				var obj = JSON.parse(data);
                message(obj.mesg);
				if (obj.code == 0){
					check_all_info();
					setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		)

	});

	//支付宝信息提交
	$("#alipay-button").unbind("click").on("click",function(){
		alipay = $("#alipay").val();
		code = $("#code").val();

		mesg_default();
		
		$.post("alipay_post",
			{
				alipay:alipay,
				code:code
			},
			function(data,state){
				var obj = JSON.parse(data);
				message(obj.mesg);

				if (obj.code == 0){
					check_all_info();
					setTimeout(function () {
						window.location.href = 'myinfo';
					},1500);
				}
			}
		)

	});

	// 身份信息显示
	$.ajax({
		type:'get',
		url:'show_identity',
		success: function(data){
			// $("#fullname").val(data['fullname']);
			// $("#sfzh").val(data['sfzh']);
		}
	});



	function check_all_info() {
		$.post('check_all_info',
			{

			},
			function (data,status) {
				var obj = JSON.parse(data);
				if (obj.code == 0) {
					window.location.href = 'index';
				}
			}
		)
	}

	$(".j_bt").unbind('click').on('click',function () {
		var style = $(this).data('style');
		$.post('check_info',
			{
				style:style
			},function (data,status) {

				var obj = JSON.parse(data);
				if (obj.code == -1){
					message(obj.mesg);
				}

				if (obj.code == 0){

					window.location.href = obj.url;

				}
			});
	});

});