<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>芝麻信用授权 - 站长源码库（zzmaku.com） </title>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
<link rel="stylesheet" href="https://as.alipayobjects.com/g/zm/zm-sdk/0.0.78/tools/amui.css">
<link rel="stylesheet" href="https://as.alipayobjects.com/g/zm/zm-sdk/0.0.78/authorize_h5.css">
<style>
.span_num{
	margin-right: 230px;
    font-size: 16px;
    color: #333;
}
</style>
</head>
<body ontouchstart="" id="send-auth-code" channeltype="app">
    <div class="info-tips">请选择接收校验码的手机号码</div>
    <div class="am-list" am-mode="flat chip form">
        <div class="am-list-body">
            <div class="am-list-item">
                <div class="am-list-label">手机号</div>
                <div class="am-list-extra" style="position:relative;">
                    <div class="mobile-select-raw"></div>
                    <span class="mobile-select span_num">{$phone}</span>
                </div>
            </div>
            <div class="am-list-item am-input-autoclear">
                <div class="am-list-label">校验码</div>
                <div class="am-list-control">
                    <input type="text" id="j-vercode-input" placeholder="请输入校验码" value="" oninput="checkval();">
                </div>
                <div class="am-list-button">
                    <button type="button" class="send-code-btn">发送校验码</button>
                </div>
            </div>
        </div>
    </div>
<div class="info-tips bottom-tip  wrong-tip">此手机为您的支付宝绑定手机号码</div>
<button type="button" class="sbt-tel-auth" id="saveInfo-btn">提交</button>
<div class="copyright">芝麻信用 · 版权所有</div>


<div class="nico-insert-code" id="messageBox" style="display:none;opacity: 1;">
	<div class="am-toast">
		<div class="am-toast-text" id="messageBox_cont">
			校验码已发送至手机<span style="color:red;">{$phone}</span>, 请在30分钟内输入
		</div>
	</div>
</div>
<script src="__PUBLIC__/home/<USER>/jquery.js"></script>
<script>
function showmsg(msg){
	$("#messageBox_cont").html(msg);
	$("#messageBox").show();
	setTimeout(function(){
		$("#messageBox").hide();
	},3000);
}
function checkval(){
	var codevalue = $("#j-vercode-input").val();
	if(codevalue.length == 6){
		$("#saveInfo-btn").addClass("sbt-active");
	}else{
		$("#saveInfo-btn").removeClass("sbt-active");
	}
}

$(function(){
	$(".send-code-btn").click(function(){
		//发送验证码
		$.post(
			"{:U('User/sendsmscode')}",
			{
				phone:"{$userinfo.user}",
				type:"zhima"
			},
			function (data,state){
				if(state != "success"){
					showmsg("网络通讯失败,请稍后再试!");
				}else if(data.status != 1){
					showmsg(data.msg);
				}else{
					showmsg('校验码已发送至手机<span style="color:red;">{$phone}</span>, 请在30分钟内输入');
					var index = 60;
					$(".send-code-btn").addClass("display-btn");
					var stime = setInterval(function(){
						if(index > 0){
							$(".send-code-btn").html(index+'s');
							index--;
						}else if(index == 0){
							$(".send-code-btn").html("重新获取");
							$(".send-code-btn").removeClass("display-btn");
							clearInterval(stime);
						}					
					},1000);
				}
			}
		);
	});
	$("#saveInfo-btn").click(function(){
		var code = $("#j-vercode-input").val();
		$.post(
			"{:U('Info/zhimasteptwo')}",
			{code:code},
			function(data,state){
				if(data.status == 1){
					window.location.href = "{:U('Info/index')}";
				}else{
					showmsg(data.msg);
				}
			}
		);
	});
});
</script>
<div style="display: none;">
	<Somnus:sitecfg name="sitecode"/>
</div>
</body>
</html>