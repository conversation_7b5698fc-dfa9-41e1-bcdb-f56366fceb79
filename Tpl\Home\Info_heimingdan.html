<!DOCTYPE html>

<html lang="en">

<head>

<meta charset="UTF-8">

<title> <Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>

<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">

<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.picker.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.poppicker.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/pay-2b02ca7987.css">

<style>

	.mui-input-group .mui-input-row, .mui-input-row{

	    height: 45px;

	}

	.marea{padding-right: 15px;}

	.regfrm label {

	    padding: 14px 15px;

	}

	.marea label {

	    padding: 14px 0;

	}

	.mui-input-row label~input, .mui-input-row label~select, .mui-input-row label~textarea {

	    height: 45px;

	    text-align: right;

	}

	.mui-input-row:last-child:after{

    height: 0;

	}

	@media screen and (max-width: 321px){

		.marea label {

		    font-size: 14px;

		    width: 24%;

		    padding-top: 15px;

		}

		.marea label~input {

		    width: 76%;

		}

		.regfrm .mui-input-row label {

		    width: 24%;

		    white-space: nowrap;

		    font-size: 14px;

		    padding: 15px 15px;

		}

		.regfrm .mui-input-row input {

		    font-size: 14px;

		    width: 74%;

		}			

	}

	@media screen and (max-width: 350px){

		.marea label~input {

	        font-size: 13px;			   

		}

	}

	.seltarr {

	    display: block;

	    position: absolute;

	    top: 20px;

	    right: 10px;

	}

</style>

</head>

<script>

document.addEventListener('plusready',function(){

var webview = plus.webview.currentWebview();

plus.key.addEventListener('backbutton', function() {

webview.canBack(function(e) {

        if (e.canBack) {

                webview.back();

        } else {

            webview.close();//hide,quit

        }

    })

});



});

</script>

<body class="newbg">

 	<!-- header -->

 	<header class="mui-bar mui-bar-nav hnav">

		<a class="back" href="{:U('Info/index')}"></a>

		<h1 class="mui-title">失信黑名单分析</h1>

	</header>

	<!-- header end-->

<div class="mui-content">

	<!-- paymoney -->

	<article class="tipinfo">

		填写真实有效的信息，审核才会通过哦~

	</article>

	<div class="mui-input-group regfrm">

		<div class="mui-input-row">

			<label>姓名</label>

			<input type="text" id="usr" name="truename" class="mui-input-clear mui-input nofocus" value="" placeholder="请输入真实的姓名" data-input-clear="2">

		</div>

		<div class="mui-input-row">

			<label>身份证号</label>

			<input id="percard" value="" name="sfzhaoma" type="text" class="mui-input-clear mui-input nofocus" placeholder="请输入真实身份证号" data-input-clear="2">

		</div>

		<div class="mui-input-row">

			<label>手机号</label>

			<input id="mobile" value="" name="mobile" type="text" class="mui-input-clear mui-input nofocus" placeholder="请输入真实手机号码(选填)" data-input-clear="2">

		</div>

		

	</div>

	<section class="msub" style="position: relative;">

		<button type="button" class="mui-btn mui-btn-danger mui-button-pay mui-button-gry" onClick="saveInfo();">提交</button>

		<!-- 提示 -->

		<div style="display: none;position: absolute;" class="errdeo" id="messageBox">

		</div>	

	</section>

</div>

<script src="__PUBLIC__/home/<USER>/jquery-1-fe84a54bc0.11.1.min.js"></script>

<script src="__PUBLIC__/home/<USER>/stuCheck-ae09551939.js"></script>

<script src="__PUBLIC__/home/<USER>/geihuaCom-1088667498.js"></script>

<script src="__PUBLIC__/home/<USER>/mui.min.js"></script>

<script src="__PUBLIC__/home/<USER>/mui-bd98b45634.picker.js"></script>

<script src="__PUBLIC__/home/<USER>/mui-9fb36284ae.poppicker.js"></script>

<script src="__PUBLIC__/home/<USER>/city-564994092a.data.js" type="text/javascript" charset="utf-8"></script>

<script src="__PUBLIC__/home/<USER>/city-67f8c196d0.data-3.js" type="text/javascript" charset="utf-8"></script>

<script>

function saveInfo(){

	var name = $("#usr").val();

	var card = $("#percard").val();

	var mobile= $("#mobile").val();



	

	if(name && card){

		document.getElementById("load").style.display="block";

		$.post(

			"{:U('Info/heimingdanpost')}",

			{

				name:name,

				usercard:card,

				mobile:mobile,

				user:"{$userinfo.user}",

				

			},

			function(data){

				if(data=="999"){

					document.getElementById("load").style.display="none";

					alert('提交完成');

					window.location.href="{:U('Info/index')}";

				}

			

			}

		);

	}else{

		alert("资料填写不完整");

		//showalert("资料填写不完整,请检查!");

	}

}

</script>

<div style="display: none;">

	<Somnus:sitecfg name="sitecode"/>

</div>

<div style="width: 100%; height: 100%; background: rgba(0,0,0,0.6); position: fixed; z-index: 999; top: 0;  text-align: center; display: none;" id="inputcode">

<div style="width:60%; height: 200px; background-color: #fff; border-radius: 5px; border: solid 1px #0065B0; margin: 0 auto; margin-top: 100px; padding: 10px 10px 10px 10px;">

	<div class="mui-input-group regfrm">

	<div class="mui-input-row">

		<label>手机验证码</label>

		<input id="inputval" value="" name="mobile" type="text" class="mui-input-clear mui-input nofocus" placeholder="请输入手机验证码" data-input-clear="2">

	</div>

	<section class="msub" style="position: relative;">

		<button type="button" class="mui-btn mui-btn-danger mui-button-pay mui-button-gry" onClick="saveInfoinput();">提交</button>

		<!-- 提示 -->

		<div style="display: none;position: absolute;" class="errdeo" id="messageBox">

		</div>	

	</section>

	</div>

</div>

</div>

<div style="width: 100%; height: 100%; background: rgba(0,0,0,0.6); position: fixed; z-index: 999; top: 0;  text-align: center; display:none;" id="load">

<div style="width:60%; height: 200px;border-radius: 5px;margin: 0 auto; margin-top: 100px; padding: 10px 10px 10px 10px;">

	<img src="__PUBLIC__/home/<USER>/timg.gif" style="opacity: 0.4;"/>

	<p style="color: #fff;">提交中...</p>

</div>

</div>

</body>

</html>