<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>还款详情 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myrepayment.css">
	<style>
		.coupon_zero {
			width: 100%;
			height: 100%;
			text-align: center;
			margin: 70px 0 0;
			color: #a8a6a7;
		}
	</style>
</head>

<body>
	<div class="comm_top_nav">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('Order/bills')}'"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">还款详情-共{$data.0.months}期</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
	<empty name="data">
		<div class="coupon_zero">
			暂无还款
			<img src="__PUBLIC__/home/<USER>/image/norepay.png" alt="" width="100%">
		</div>
		<else />
		<foreach name="data" item="vo">
		<div class="repayment">
			<div class="am-g">
				<div class="am-u-sm-8">
					<span class="f_number">￥<span class="hk_money">{$vo.monthmoney}</span></span>
				</div>
				<div class="am-u-sm-4 stages_number">
					<span>第<span class="f_number">{$vo.ofnumber}</span>期</span>
				</div>
			</div>
			<div class="am-g state_info">
				<div class="am-u-sm-12">
					还款截止日期：<span class="f_number">{$vo.huantime}</span>
					<br>
					状态：<span class="repayment_state_yq"> <if condition="$vo['zfimg'] eq ''">
						<if condition="$time gt $vo['huantime']">已逾期-立即还款</if>
						<if condition="$time lt $vo['huantime']">提前还款</if>
						<if condition="$time eq $vo['huantime']">立即还款</if>
					 </if>
					 <if condition="$vo['zfimg'] neq ''">
						 <if condition="$vo.status eq 0">待确认还款状态</if>
						 <if condition="$vo.status eq 1">已还款</if>

					 </if>
				</span>
				</div>
			</div>
			<if condition="$vo.status neq 1">
			<div class="am-g">
				<div class="am-u-sm-12">
					<span class="state_button"><a href="{:U('Order/repay',array('type'=>2,'money'=>$vo['monthmoney'],'ordernum'=>$vo['ordernum'],'id'=>$vo['id']))}" style="color: #fff;">提交还款</a></span>
				</div>
			</div>
			</if>
		</div>
</foreach>
</empty>
		<div style="height: 20px;"></div>
		<div data-am-widget="gotop" class="am-gotop am-gotop-fixed">
			<a href="#top" title="">
				<i class="am-gotop-icon am-icon-arrow-up"></i>
			</a>
		</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
</body>

</html>