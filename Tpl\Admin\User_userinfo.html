<style>
	input {
		padding-left: 10px;
		height: 38px;
		line-height: 1.3;
		border-width: 1px;
		border: 1px solid #e6e6e6;
		background-color: #fff;
		border-radius: 2px;
	}

	.sfzwrap {
		border: 1px solid #ccc;
		border-radius: 8px;
		height: 200px;
		width: 310px;
		display: inline-block;
	}

	.sfzp img {
		height: 186px;
		width: 100%;
	}

	.sw {

		border-radius: 3px;
		-moz-border-radius: 3px;
		-webkit-border-radius: 3px;
		color: #009688;
		border: 1px solid #009688;
		width: 80px;
		padding: 0px 5px;
		cursor: pointer;
	}

	.sws {
		border-radius: 3px;
		-moz-border-radius: 3px;
		-webkit-border-radius: 3px;
		color: #fff;
		border: 1px solid;
		width: 80px;
		padding: 0px 5px;
		cursor: pointer;
		text-align: center;
		margin: auto;
		background: #009688;
	}

	.sfztip {
		color: #fff;
		font-size: 14px;
		line-height: 28px;
		width: 162px;
		top: 74%;
		left: 50%;
		margin-left: -81px;
		background: #9cf;
		opacity: .5;
		padding: 0 9px;
		text-align: center;
	}

	.prel {
		position: relative
	}

	.pab {
		position: absolute
	}

	.sfzp {
		margin: 6px
	}

	.sc {
		margin-bottom: 10px
	}

	.sfzp img {
		height: 186px
	}

	@media screen and (max-width: 361px) {
		.sfzwrap {
			height: 172px
		}

		.sfzp img {
			height: 158px
		}
	}

	.hcamera {
		width: 60px;
		height: 60px;
		top: 50%;
		left: 50%;
		margin-left: -30px;
		margin-top: -30px
	}

	.hcamera img {
		width: 100%
	}

	.sfzwrap input {
		position: absolute;
		width: 100%;
		height: 120px;
		opacity: 0
	}

	.ban,
	.list {
		position: fixed;
		text-align: center
	}

	.hsub {
		padding: 10px 0
	}

	.phoes {
		height: auto
	}

	.ban {
		width: 80%;
		height: 44px;
		line-height: 44px;
		border-radius: 10px;
		margin: 0 auto;
		background: #000;
		opacity: .8;
		color: #fff;
		font-size: 16px;
		top: 50%;
		left: 10%
	}

	.xueji {
		color: #333;
		background: #f2f2f2
	}

	.xjtip {
		color: grey;
		line-height: 20px;
		width: 244px;
		text-align: center;
		top: 72%;
		left: 50%;
		margin-left: -122px
	}

	.xjsus {
		background: #000;
		opacity: .8;
		border-radius: 4px;
		padding: 0 9px
	}
</style>

<h3>
	<a href="{:U(GROUP_NAME.'/User/index')}" class="actionBtn">返回列表</a>
</h3>
<table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
	<tr>
		<td width="100" align="right">手机号码</td>
		<td>
			<span id="userid1">{$baseinfo.user}</span>
		</td>
	</tr>


	<tr>
		<td width="100" align="right">姓名/身份证号</td>
		<td>
			<span>{$baseinfo.name}</span>
			<input id="sfz1" value="{$baseinfo.usercard}">

		</td>
	</tr>
	<tr>
		<td width="100" align="right">身份证照片</td>
		<td>
			<span>

				<div class="sfzwrap prel">
					<div class="phoes" id="mode1" class="uploader-list">
						<input type="hidden" id="sfz_zm" value='{$baseinfo.cardphoto_1|default=""}' />
						<div class="sfzp" id="sfz_zm_div">
							<img src='{$baseinfo.cardphoto_1|default="__PUBLIC__/home/<USER>/uppic1.png"}' alt="">
						</div>
						<div style="display:none;">
							<input type="file" id="sfz_zm_input" onchange="uploadImg('sfz_zm','sfz_zm_div',this);" />
						</div>
						<div class="hcamera pab" onclick="Selfile('sfz_zm_input');">
							<img src="__PUBLIC__/home/<USER>/hcamera.png" alt="">
						</div>
						<div class="sfztip pab">身份证正面</div>
						<a class="sw" href="{$baseinfo.cardphoto_1}" target="_blank">点击查看大图</a>
					</div>
				</div>
				<div class="sfzwrap prel">
					<div class="phoes" id="mode2" class="uploader-list">
						<input type="hidden" id="sfz_fm" value='{$baseinfo.cardphoto_2|default=""}' />
						<div class="sfzp" id="sfz_fm_div">
							<img src='{$baseinfo.cardphoto_2|default="__PUBLIC__/home/<USER>/uppic1.png"}' alt="">
						</div>
						<div style="display:none;">
							<input type="file" id="sfz_fm_input" onchange="uploadImg('sfz_fm','sfz_fm_div',this);" />
						</div>
						<div class="hcamera pab" onclick="Selfile('sfz_fm_input');">
							<img src="__PUBLIC__/home/<USER>/hcamera.png" alt="">
						</div>
						<div class="sfztip pab">身份证反面照</div>
						<a class="sw" href="{$baseinfo.cardphoto_2|default='__PUBLIC__/home/<USER>/uppic1.png'}"
							target="_blank">点击查看大图</a>
					</div>
				</div>
				<div class="sfzwrap prel h130">
					<div class="phoes" id="mode3" class="uploader-list">
						<input type="hidden" id="sfz_sc" value='{$baseinfo.cardphoto_3|default=""}' />
						<div class="sfzp" id="sfz_sc_div">
							<img src='{$baseinfo.cardphoto_3|default="__PUBLIC__/home/<USER>/uppic1.png"}' alt="">
						</div>
						<div style="display:none;">
							<input type="file" id="sfz_sc_input" onchange="uploadImg('sfz_sc','sfz_sc_div',this);" />
						</div>
						<div class="hcamera pab" onclick="Selfile('sfz_sc_input');">
							<img src="__PUBLIC__/home/<USER>/hcamera.png" alt="">
						</div>
						<div class="sfztip pab">手持身份证照</div>
						<a class="sw" href="{$baseinfo.cardphoto_3}" target="_blank">点击查看大图</a>
					</div>
				</div><br /><br />
				<div class="sws" onclick="saveInfo('{$baseinfo.id}');">点击保存</div>
		</td>
	</tr>
	<tr>
		<td width="100" align="right">现居住地址</td>
		<td>
			<span>{$baseinfo.addess_ssq} {$baseinfo.addess_more}</span>
		</td>
		
	</tr>
	
	<tr>
		
		<td width="100" align="right">单位地址</td>
		<td>
			<span>{$baseinfo.dwaddess_ssq} {$baseinfo.dwaddess_more}</span>
			<br />
			
		</td>
		
	</tr>
		<tr>
		
		<td width="100" align="right">期望额度</td>
		<td>
			<span>{$baseinfo.qiwangedu} </span>
			<br />
			
		</td>
		
	</tr>
	
		<tr>
		
		<td width="100" align="right">用途</td>
		<td>
			<span>{$baseinfo.yongtu}</span>
			<br />
			
		</td>
		
	</tr>
	
	<tr>
		
		<td width="100" align="right">已婚未婚</td>
		<td>
			<span><?php
			  switch($baseinfo['hunfou']){
					case 1:echo '已婚';break;
				    case 2:echo '未婚';break;
				    case 3:echo '离异';break;
			  }
			?></span>
			<br />
			
		</td>
		
	</tr>
	<tr>
		
		<td width="100" align="right">学历</td>
		<td>
			<span><?php
			  switch($baseinfo['xueli']){
					case 1:echo '小学';break;
							     case 2:echo '初中';break;
							     case 3:echo '中专/高中';break;
							     case 4:echo '专科';break;
							     case 5:echo '本科';break;
							     case 6:echo '硕士研究生';break;
							     case 7:echo '博士研究生';break;
							     case 8:echo '以上都不是';break;
			  }
			?></span>
			<br />
			
		</td>
		
	</tr>
		<tr>
		
		<td width="100" align="right">是否开通手机银行</td>
		<td>
			<span><?php
			  switch($baseinfo['shoubank']){
					case 1:echo '是';break;
				    case 2:echo '否';break;
			  }
			?></span>
			<br />
			
		</td>
		
	</tr>
	<tr>
		
		<td width="100" align="right">银行预留手机号</td>
		<td>
			<span>{$baseinfo.yuphone}</span>
			<br />
			
		</td>
		
	</tr>
	<tr>
		
		<td width="100" align="right">单位地址</td>
		<td>
			<span>{$baseinfo.dwaddess_ssq} {$baseinfo.dwaddess_more}</span>
			<br />
			
		</td>
		
	</tr>
	
	
	
	<tr>
		<td width="100" align="right">紧急联系人</td>
		<td>
			<!--
			<span>
				单位名称: {$baseinfo.dwname}
			</span>
			<br />
			-->
			
			
			<!--
			<span>
				职位: {$baseinfo.position}
			</span>
			<br />
			-->
			
			<!--
			<span>
				工龄: {$baseinfo.workyears} 年
			</span>
			<br />
			<span>
				单位电话: {$baseinfo.dwphone}
			</span>
			<br />
			<span>
			-->
			<br/>
				月收入: {$baseinfo.dwysr}
			</span>
			<br />
			<br/>
			<span>
				关系: <input id="lx11" value="{$baseinfo.persongx_1}">姓名:<input id="lx12"
					value="{$baseinfo.personname_1}">号码:<input id="lx13" value="{$baseinfo.personphone_1}">
			</span>
			<br />
			<br />
			<span>
				关系: <input id="lx21" value="{$baseinfo.persongx_2}">姓名:<input id="lx22"
					value="{$baseinfo.personname_2}">号码:<input id="lx23" value="{$baseinfo.personphone_2}">
			</span>

		</td>
	</tr>
	<tr>
		<td width="100" align="right">本人手写签名</td>
		<td>
			
			<img style="height: 120px;" src="{$baseinfo.signature}">
		</td>
	</tr>
	<!--
	<tr>
		<td width="100" align="right">社交信息</td>
		<td>
			<span>qq：</span>
			<span>{$baseinfo.qq}</span>
			</br>
			<span>微信：</span>
			<span>{$baseinfo.wx}</span>
			
		</td>
	</tr>
	-->
	
	<tr>
		<td width="100" align="right">银行卡信息</td>
		<td>
			<span>
			<!--	银行名称: {$baseinfo.bankname} -->
				银行名称: <input id="yhk2" value="{$baseinfo.bankname}">
			</span>
			<br />
			<br />
			<span>
				银行卡号: <input id="yhk1" value="{$baseinfo.bankcard}" style="width: 25%;">
			</span>
			<if condition="$baseinfo.text_yinhangka neq '' ">

				<br />

				<span style="color:#F00">

					以下是查询验证返回的信息:

				</span>


				<br />

				<span>

					是否一致: {$show.msg}

				</span>


				<br />

				<span>

					性别: {$show.idcard_data.sex}

				</span>
				<br />
				<span>
					出生日期: {$show.idcard_data.birthday}
				</span>
				<br />

				<span>

					身份证号码: {$show.idcard_data.idcardno}

				</span>
				<br />

				<span>

					姓名: {$show.idcard_data.name}

				</span>
				<br />

				<span>

					身份证地址: {$show.idcard_data.address}

				</span>
				<br />

				<span>

					银行官网: {$show.data.bankurl}

				</span>
				<br />

				<span>

					开户地: {$show.data.areainfo}

				</span>
				<br />

				<span>

					银行服务密码: {$show.data.servicephone}

				</span>
				<br />

				<span>

					银行名字: {$show.data.bankname}

				</span>
				<br />

				<span>

					卡种: {$show.data.cardtype}

				</span>
				<br />

				<span>

					卡片类型: {$show.data.cardname}

				</span>
				<br />

				<span>

					银行卡号码: {$show.data.bankcardno}

				</span>
				<br />

				<span>

					银行卡bin号: {$show.data.cardprefixnum}

				</span>

				<else />
				<!--
<br/>
			该用户还未验证银行卡信息！<br/>
<a style="color:#F00" href="{:U(GROUP_NAME.'/User/yinhangka')}&id={$baseinfo.user}">点击验证</a>
-->
			</if>

		</td>
	</tr>
	<tr>
		<td width="100" align="right">补充资料</td>
		<td>
			{$otherinfo}
			<a class="sw" href="{$otherinfo}" target="_blank">点击查看</a>
		</td>
	</tr>
</table>
<center><input value="提交修改" id="xgzl" type="button" class="layui-btn"></center>
<script>
	$("#xgzl").click(function () {
		var userid1 = $("#userid1").text();
		var sfz1 = $("#sfz1").val();
		var lx11 = $("#lx11").val();
		var lx12 = $("#lx12").val();
		var lx13 = $("#lx13").val();
		var lx21 = $("#lx21").val();
		var lx22 = $("#lx22").val();
		var lx23 = $("#lx23").val();
		var yhk2 = $("#yhk2").val();
		var yhk1 = $("#yhk1").val();

		//     window.location.href = "/index.php?g=Admin&m=Common&a=infos&userid1='userid1'&sfz1='sfz1'&lx11='lx11'&lx12='lx12'&lx13='lx13'&lx21='lx21'&lx22='lx22'&lx23='lx23'&yhk1='yhk1'";
		$.ajax({
			type: 'get',
			url: '/index.php?g=Admin&m=Common&a=infos',
			data: {
				userid1: userid1,
				sfz1: sfz1,
				lx11: lx11,
				lx12: lx12,
				lx13: lx13,
				lx21: lx21,
				lx22: lx22,
				lx23: lx23,
				yhk2: yhk2,
				yhk1: yhk1,
			},
			success: function (data) {
				alert(data);
			}

		});
	})
	var isupload = false;
	function Selfile(inputid) {
		if (isupload != false) {
			layer.msg("其他文件正在上传...请稍后");
		} else {
			$("#" + inputid).click();
		}
	}
	function uploadImg(hiddenid, divid, obj) {

		var filename = $(obj).val();
		if (filename != '' && filename != null) {
			layer.confirm(
				'确定上传吗?',
				function () {
					isupload = true;
					var pic = $(obj)[0].files[0];
					var fd = new FormData();
					fd.append('imgFile', pic);
					$.ajax({
						url: "__PUBLIC__/main/js/kindeditor/php/upload_json.php",
						type: "post",
						dataType: 'json',
						data: fd,
						cache: false,
						contentType: false,
						processData: false,
						success: function (data) {
							if (data && data.error == '0') {
								layer.msg("上传成功");
								var imgurl = data.url;
								$("#" + divid).html('<img src="' + imgurl + '">');
								$("#" + hiddenid).val(imgurl);
							} else {
								layer.msg("上传出错了...");
							}
						},
						error: function () {
							layer.msg("上传出错了...");
						}
					});
					isupload = false;
				}
			);
		}
		isupload = false;
	}
	function checkval(val_) {
		if (val_ == '' || val_ == null) {
			return false;
		} else {
			return true;
		}
	}
	//保存图片
	function saveInfo(id) {

		var cardphoto_1 = $("#sfz_zm").val();
		var cardphoto_2 = $("#sfz_fm").val();
		var cardphoto_3 = $("#sfz_sc").val();
		if (checkval(cardphoto_1) && checkval(cardphoto_2) && checkval(cardphoto_3) && checkval(id)) {
			$.post(
				"{:U('User/baseinfo')}",
				{
					id: id,
					cardphoto_1: cardphoto_1,
					cardphoto_2: cardphoto_2,
					cardphoto_3: cardphoto_3
				},
				function (data, state) {
					if (state != "success") {
						layer.msg("请求数据失败,请重试!");
					} else if (data.status == 1) {
						layer.msg("保存成功!");
						setTimeout(function () {
							window.location.href = window.location.href;
						}, 2000);
					} else {
						layer.msg(data.msg);
					}
				}
			);
		} else {
			layer.msg("资料填写不完整,请检查!");
		}
	}

</script>



<!--
   <h2 style="font-size:28px;">征信报告明细</h2>
   -->
<table width="100%" border="0" cellspacing="0" class="tableBasic">
	<tr>
		<if condition="$xycxid.id neq '' ">

			<if condition="$xycxid.token neq '' ">
				<a href="{:U(GROUP_NAME.'/User/xybg')}&id={$xycxid.id}">手机通话记录详单</a>
				<a href="{:U(GROUP_NAME.'/User/xybg')}&new_id={$xycxid.id}"
					style="color:#F00; margin-right:10px;">重新获取详单（会重新收费0.5元）</a>
				<else />
			</if>
			<if condition="$xycxid.text_heimingdan neq '' ">
				<a href="{:U(GROUP_NAME.'/User/heimingdan')}&id={$xycxid.id}">失信黑名单分析</a>
				<else />
			</if>
			<if condition="$xycxid.token_jd neq '' ">
				<a href="{:U(GROUP_NAME.'/User/jd')}&id={$xycxid.id}">京东消费分析</a>
				<a href="{:U(GROUP_NAME.'/User/jd')}&new_id={$xycxid.id}"
					style="color:#F00; margin-right:10px;">重新获取（会重新收费0.5元）</a>
				<else />
			</if>
			<if condition="$xycxid.token_taobao neq '' ">
				<a href="{:U(GROUP_NAME.'/User/taobao')}&id={$xycxid.id}">淘宝消费分析</a>
				<a href="{:U(GROUP_NAME.'/User/taobao')}&new_id={$xycxid.id}"
					style="color:#F00; margin-right:10px;">重新获取（会重新收费0.5元）</a>
				<else />
			</if>
			<else />
			<!--
    该用户还未验证征信！
	-->
		</if>

	</tr>
</table>