function taocheck(){$("#form1").validate({errorPlacement:function(e,r){$(r).closest("form").find("label[for='"+r.attr("id")+"']").append(e)},errorLabelContainer:"#messageBox",errorElement:"span",rules:{user:{required:!0,minlength:4},password:{required:!0,minlength:4,maxlength:16}},messages:{user:{required:" 请输入账户",minlength:" (不能少于 3 个字母)"},password:{required:" 请输入密码",minlength:"请输入4-16位密码",maxlength:"请输入4-16位密码"}}})}function checkemail(){$("#login-form").validate({errorPlacement:function(e,r){$(r).closest("form").find("label[for='"+r.attr("id")+"']").append(e)},errorLabelContainer:"#messageBox",errorElement:"span",rules:{email:{required:!0,email:!0},password:{required:!0,minlength:4,maxlength:16}},messages:{email:{required:" 请输入邮箱账号",email:" 请输入一个有效的邮箱账号"},password:{required:" 请输入密码",minlength:"请输入4-16位密码",maxlength:"请输入4-16位密码"}}})}var on=!0;$(".mui-icon").click(function(){console.log(1),on?($("#password").attr("type","text"),$("#eye").css("color","#fb6f00"),on=!1):($("#password").attr("type","password"),$("#eye").css("color","#999"),on=!0)});var namereg=/^([\`\·\.\u4e00-\u9fa5])+$/;$.validator.addMethod("namecheck",function(e,r,n){return!!namereg.test(e)},"姓名必须为汉字");var phonereg=/^1[3|4|5|7|8][0-9]\d{8}$/;$.validator.addMethod("phonecheck",function(e,r,n){return!!phonereg.test(e)},"必须是合法手机号");var validator=$("#infoForm1").validate({wrapper:"div",ignore:"",rules:{xingming1:{required:!0,namecheck:!0},phone1:{required:!0,phonecheck:!0},xingming2:{required:!0,namecheck:!0},phone2:{required:!0,phonecheck:!0},pengyou:{required:!0,namecheck:!0},pyphone:{required:!0,phonecheck:!0}},messages:{xingming1:{required:" 请输入姓名",namecheck:"姓名必须为汉字"},phone1:{required:"请输入亲属手机号",phonecheck:"请输入合法手机号"},xingming2:{required:" 请输入姓名",namecheck:"姓名必须为汉字"},phone2:{required:"请输入亲属手机号",phonecheck:"请输入合法手机号"},pengyou:{required:" 请输入姓名",namecheck:"姓名必须为汉字"},pyphone:{required:"请输入朋友手机号",phonecheck:"请输入合法手机号"},errorPlacement:function(e,r){e.appendTo(r.parent())}}}),pwdreg=/[a-zA-Z0-9_]{6,16}/;$.validator.addMethod("pwdcheck",function(e,r,n){return!!pwdreg.test(e)},"请设置6-16位密码");var forgetpwdcheck=$("#back-form").validate({wrapper:"div",ignore:"",errorLabelContainer:"#messageBox",errorElement:"span",rules:{account:{required:!0},checkma:{required:!0},password:{required:!0,pwdcheck:!0}},messages:{account:{required:" 请输入用户名/手机号"},checkma:{required:"请输入验证码"},password:{required:"请输入密码",pwdcheck:"请设置6-16位密码"},errorPlacement:function(e,r){e.appendTo(r.parent())}}});