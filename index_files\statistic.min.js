var BfbStatistic=function(t,e){var n=+new Date;try{n+=":"+"xxyx-xyxx-4xxx-yxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0,n="x"===t?e:3&e|8;return n.toString(16)})}catch(i){}for(var r=document.cookie.split("; "),a=r.length,o="",c=0;a>c;c++)if(0==r[c].indexOf("__bsi")){o=r[c].slice(6);break}var s={type:{defaults:"defaults",page_view:"page_view",performance:"performance",churn_rate:"churn_rate",exception:"exception"},logid:n,ssl_info:o,url:"https://www.baifubao.com/tongji/performance/stat.jpg",user_action_url:"https://www.baifubao.com/h5chanpin_stat"};!function(){location.host&&!/baifubao\.com$/.test(location.host)&&(s.url="https://qianbao.baidu.com/tongji/performance/stat.jpg",s.user_action_url="https://qianbao.baidu.com/h5chanpin_stat")}();var u=e.documentElement,d={tempImgList:[],createImg:function(n,i){var r=this.tempImgList,a=t.Image?new Image:e.createElement("img");r.push(a),i&&this.onload(a,i),a.src=n},each:function(t,e){for(var n in t)if(t.hasOwnProperty(n)){var i=t[n];e.call(i,n,i)}return t},extend:function(){var t,e,n,i,r,a,o=arguments[0]||{},c=1,s=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[1]||{},c=2),"object"!=typeof o&&"function"===!this.type(o)&&(o={}),s===c&&(o=this,--c);s>c;c++)if(null!=(t=arguments[c]))for(e in t)n=o[e],i=t[e],o!==i&&(u&&i&&(this.isPlainObject(i)||(r=this.isArray(i)))?(r?(r=!1,a=n&&this.isArray(n)?n:[]):a=n&&this.isPlainObject(n)?n:{},o[e]=this.extend(u,a,i)):void 0!==i&&(o[e]=i));return o},isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},isMobile:function(){return new RegExp("android|avantgo|bada/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge|maemo|midp|mmp|operam(ob|in)i|palm(os)?|phone|p(ixi|re)/|plucker|pocket|psp|symbian|treo|up.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino","gi").test(navigator.userAgent)},isPlainObject:function(t){if(!t||"object"!==this.type(t)||t.nodeType||null!=t&&t==t.window)return!1;try{if(t.constructor&&!Object.prototype.hasOwnProperty.call(t,"constructor")&&!Object.prototype.hasOwnProperty.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}var n;for(n in t);return void 0===n||Object.prototype.hasOwnProperty.call(t,n)},obj2str:function(t){var e=[];switch(!0){case"undefined"==typeof t:e="";break;case"string"==typeof t:e='"'+t.replace(/([\"\\])/g,"\\$1").replace(/(\n)/g,"\\n").replace(/(\r)/g,"\\r").replace(/(\t)/g,"\\t")+'"';break;case"object"==typeof t:if("[object Array]"!==Object.prototype.toString.call(t)){for(var n in t)t.hasOwnProperty(n)&&e.push('"'+n+'":'+this.obj2str(t[n]));e="{"+e.join()+"}"}else{for(var i=0;i<t.length;i++)e.push(this.obj2str(t[i]));e="["+e.join()+"]"}break;default:e=t.toString().replace(/\"\:/g,'":""')}return e},on:function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent?t.attachEvent("on"+e,n):t["on"+e]=n},onload:function(e,n){var i=!(!t.attachEvent||t.opera),r=!1,a=function(){r||(r=!0,e.onload=e.onerror=null,n&&n())};if(e.onload=e.onerror=function(){a()},i)var o=t.setInterval(function(){return r?void(o&&t.clearInterval(o)):void(("loaded"===e.readyState||"complete"===e.readyState)&&(o&&t.clearInterval(o),a()))},10)},param:function(t,e){var n=function(t){var n=[];for(var i in t){var r=t[i];"undefined"!=typeof r&&n.push(encodeURIComponent(i)+"="+encodeURIComponent(r))}return(e||s.url)+"?"+n.join("&")},i=n(t);return i.length>2083&&(t.refer="",i=n(t),i.length>2083&&t.stack&&(t.stack="",i=n(t),i.length>2083))?void 0:i},sendData:function(n,i){n=n||{};var r=t.statisticExtendData||{},a=n.pageId||r.pageId||t.statisticPageId||"";if(a){var o=d.extend({type:s.type.defaults,refer:e.referrer||"",pageId:a,clientTime:+new Date,spNo:t.statisticSpNo||"",logid:s.logid,isMobile:d.isMobile(),ssl_info:s.ssl_info},r,n),c=d.param(o);c?d.createImg(c,i):i&&i()}else i&&i()},toUpperCaseFirst:function(t){return t.replace(/(^|\s+)\w/g,function(t){return t.toUpperCase()})},type:function(t){for(var e=["Boolean","Number","String","Function","Array","Date","RegExp","Object"],n=0,i=e.length;i>n;n++)e["[object "+e[n]+"]"]=e[n].toLowerCase();return null==t?String(t):e[Object.prototype.toString.call(t)]||"object"},unique:function(t,e){for(var n=[],i={},r=0,a=t.length;a>r;r++){var o=t[r];i[o[e]]||(i[o[e]]=!0,n.push(o))}return n}},l={init:function(){try{this.initPageView()}catch(t){}try{this.bindImgOnloadEvent()}catch(t){}try{this.bindDomReadyEvent()}catch(t){}try{this.bindWindowOnloadEvent()}catch(t){}},bindDomReadyEvent:function(){var n=this,i=!1,r=!(!t.attachEvent||t.opera),a=function(){i||(i=!0,n.domReadyEndTime=+new Date)};r?(!function(){try{u.doScroll("left")}catch(t){return void setTimeout(arguments.callee,0)}a()}(),e.onreadystatechange=function(){"complete"==e.readyState&&(e.onreadystatechange=null,a())}):e.addEventListener("DOMContentLoaded",function(){a()},!1)},bindWindowOnloadEvent:function(){var n=this,i=function(){var e=t.statisticStartTime,n=this.domReadyEndTime||+new Date,i=this.firstScreenEndTime||n,r=t.statisticWhiteScreenEndTime||e+1,a={type:s.type.performance,isTiming:!1,startTime:e,whiteScreenTime:r-e,firstScreenTime:i-e,domReadyTime:n-e,onloadTime:+new Date-e};d.sendData(a,null)};"complete"===e.readyState?i.call(this):d.on(t,"load",function(){i.call(n)})},bindImgOnloadEvent:function(){var t=this.getStyleImgList(),e=this.getTagImgList(),n=t.concat(e);n=d.unique(n,"url");for(var i=this,r=0,a=n.length;a>r;r++)!function(t){d.createImg(t.url,function(){var e=t.element;if(!i.isHidden(e)&&i.checkIsInFirstScreen(e)){var n=+new Date;(!i.firstScreenEndTime||n>i.firstScreenEndTime)&&(i.firstScreenEndTime=n)}})}(n[r])},checkIsInFirstScreen:function(t){var n=this.getClient(t);return n.left>=0&&n.left<(u.clientWidth||e.body.clientWidth)&&n.top>=0&&n.top<(u.clientHeight||e.body.clientHeight)},getClient:function(t){if(t.getBoundingClientRect){var n=t.getBoundingClientRect();return{left:n.left-u.clientLeft,top:n.top-u.clientTop}}for(var i=t.offsetLeft,r=t.offsetTop,a=t.offsetParent;null!==a&&a!==e.body;)i+=a.offsetLeft,r+=a.offsetTop,a=a.offsetParent;return{left:i-(u.scrollLeft||e.body.scrollLeft),top:r-(u.scrollTop||e.body.scrollTop)}},getCurrentStyle:function(t,e){try{return t.currentStyle?t.currentStyle[e]:document.defaultView.getComputedStyle(t,null)[e]||document.defaultView.getComputedStyle(t,null).getPropertyValue(e)}catch(n){return null}},getStyleImgList:function(){for(var t=[],n=e.body.getElementsByTagName("*"),i=0,r=n.length;r>i;i++){var a=n[i];if("IMG"!==a.tagName&&!this.isHidden(a)){var o=this.getCurrentStyle(a,"backgroundImage");if(o&&-1!==o.indexOf("url")&&o.length<500){var c=o.split(/\(|\)/g)[1];c=c.replace(/\"+/g,""),t.push({element:a,url:c,type:"style"})}}}return t},getTagImgList:function(){for(var t=[],n=e.body.getElementsByTagName("img"),i=0,r=n.length;r>i;i++){var a=n[i],o=a.getAttribute("src");o&&o.length<500&&!this.isCache(o)&&t.push({element:a,url:o,type:"tag"})}return t},initPageView:function(){d.sendData({type:s.type.page_view},null)},isCache:function(t){return!t||-1!==t.indexOf("?")},isHidden:function(t){var e=this.getCurrentStyle(t,"display"),n=this.getCurrentStyle(t,"visibility");return"none"===e||"hidden"===n}},h={infoTemp:{},init:function(){d.on(t,"error",this.sendErrorInfo)},checkValid:function(t){try{var e=d.obj2str(t);return/^(\s*|script\s+error\.*)$/i.test(t.message)||/unexpected.*number.*\'\.[0-9]*\'/i.test(t.message)||/^\s*$/i.test(t.filename)||h.infoTemp[e]?!1:h.infoTemp[e]=!0}catch(n){}return!1},getErrorInfo:function(t){try{var e=t[0];return"string"==typeof e?[t[0],t[1],t[2],t[3],""]:[e.message||e.description,e.filename||e.fileName||e.sourceURL,e.lineno||e.lineNumber||e.line,e.colno||e.columnNumber,e.error?e.error.stack:e.stack||e.stacktrace]}catch(n){return!1}},sendErrorInfo:function(){var t=h.getErrorInfo(arguments);if(t&&d.isArray(t)){var e={type:s.type.exception,message:encodeURIComponent(t[0]||""),filename:encodeURIComponent(t[1]||""),pos:[t[2]||0,t[3]||0],stack:encodeURIComponent(t[4]||"")};h.checkValid(e)&&d.sendData(e,null)}}},f={cacheKey:"xiechengxiong_statistic_user_action_device_info",init:function(){this.isBalance=/^h5\_balance/.test(t.statisticPageId||""),this.cuid="",this.ua="",this.taskList=[],this.taskBridgeList=[],this.isIOS=/iphone|ipad|ipod/i.test(navigator.userAgent),this.checkRuntimeReady(),this.initCuid(),this.initTimeout()},checkBridgeEnv:function(e){this.checkBridgeTimer&&clearTimeout(this.checkBridgeTimer),(t.Agent&&t.Agent.invoke||t.BLightApp)&&this.hasRuntimeReady?(this.canSendBridgeData=!0,this.canSendData=!0,this.doTaskBridge(),e&&e()):this.checkBridgeTimer=setTimeout(function(){f.checkBridgeEnv(e)},10)},checkRuntimeReady:function(){if(this.isIOS){document.addEventListener("runtimeready",function(){f.hasRuntimeReady=!0},!1),this.checkRuntimReadyTimer&&clearTimeout(this.checkRuntimReadyTimer);var n=function(){f.checkRuntimReadyTimer=setTimeout(function(){f.hasRuntimeReady=!0},5e3)};"complete"===e.readyState?n():d.on(t,"load",function(){n()})}else f.hasRuntimeReady=!0},checkValid:function(){this.checkValidTimer&&clearTimeout(this.checkValidTimer),this.checkValidTime=this.checkValidTime||0,this.checkValidTime+=100,1!==+(t.statisticUserActionSwitcher||0)||this.isBalance&&1!==+(!!t.statisticPageId||0)?this.checkValidTime<1e4&&(this.checkValidTimer=setTimeout(function(){f.checkValid()},100)):(this.canSendData=!0,this.doTask())},checkIsNewSdkVersion:function(){var t=!0;try{var e=navigator.userAgent.toLocaleLowerCase(),n=e.match(/baiduwallet\-((\d|\.)+)\-/i);if(n){var i=n[1],r="6.4.0.0";i=i.split("."),r=r.split(".");for(var a=0;4>a;a++){var o=parseInt(i[a],10),c=parseInt(r[a],10);if(c>o){t=!1;break}}}else t=!1}catch(s){t=!1}return t},initCuid:function(){try{var t=function(e){try{try{"string"==typeof e&&(e=JSON.parse(e)),sessionStorage.setItem(f.cacheKey,JSON.stringify(e))}catch(n){}var i=e.cnt.data;f.cuid=i.cuid||"",f.ua=i.ua||""}catch(n){}f.sendPageEvent(),t=new Function};this.checkBridgeEnv(function(){f.invokeBridge("getDeviceInfo",t)});var e=sessionStorage.getItem(this.cacheKey);this.checkIsNewSdkVersion()?this.sendPageEvent():e&&t(e)}catch(n){}},invokeBridge:function(e,n,i){var r=t.Agent,a=t.BLightApp;if(i=i||function(){},r&&r.invoke)"function"==typeof n&&(i=n,n={}),r.invoke(e,d.extend(!0,{},n),function(t,e){i(e)});else if(a){for(var o="xx"+1e18*Math.random();t[o];)o="xx"+1e18*Math.random();if("function"==typeof n?(i=n,n=""):n=n||"",t[o]=function(t){i(t)},a.invokeBdWalletNative){n=n||{},n.method_name=e;try{a.invokeBdWalletNative(JSON.stringify(n),o,o)}catch(c){alert(c)}}else a[e]?(n&&(n=JSON.stringify(n)),a[e](n,o,o)):i()}else i()},initTimeout:function(){function n(){var t=500;/android/i.test(navigator.userAgent.toLowerCase())&&(t=100),setTimeout(function(){f.sendPageEvent()},t)}"complete"===e.readyState?n():d.on(t,"load",function(){n()})},doTask:function(){try{var t=this.taskList[0];t&&(this.taskList.splice(0,1),this.sendUserActionData.apply(this,t))}catch(e){}},doTaskBridge:function(){try{var t=this.taskBridgeList[0];t&&(this.taskBridgeList.splice(0,1),this.sendBridgeData.apply(this,t))}catch(e){}},sendPageEvent:function(){this.isBalance&&this.sendUserActionData({en:t.statisticPageId||"",eg:"in"}),this.sendPageEvent=new Function,this.checkValid()},sendUserActionData:function(t,e){try{if(!t||!t.en||/^\s*$/.test(t.en)||t.ev&&"array"!==d.type(t.ev))return;t.et=t.et||+new Date;var n=new Function,i=new Function;"function"==typeof e?n=e:"object"==typeof e&&(n=e.remote||n,i=e.bridge||i),this.sendRemoteData(t,n),this.sendBridgeData(t,i)}catch(r){e&&e()}},sendRemoteData:function(t,e){try{if(!this.canSendData||this.posting)return void this.taskList.push([t,e]);d.each(d.extend({},t),function(e,n){""===n&&delete t[e]});var n=d.extend(!0,{cu:this.cuid||"",ua:this.ua||navigator.userAgent||"",et:+new Date},t),i=d.param({params:d.obj2str(n)},s.user_action_url);if(i){this.posting=!0;var r=!1,a=function(){r||(r=!0,f.posting=!1,e&&e(),f.doTask())};setTimeout(a,500),d.createImg(i,a)}else e&&e()}catch(o){e&&e()}},sendBridgeData:function(t,e){try{if(!this.canSendBridgeData||this.bridgePosting)return void this.taskBridgeList.push([t,e]);this.bridgePosting=!0;var n=!1,i=function(t){n||(n=!0,f.bridgePosting=!1,f.doTaskBridge(),e&&e(t))};t.cu="",t.ua="",setTimeout(i,100),this.invokeBridge("customerService",{data:t},i)}catch(r){e&&e()}}};try{l.init(),h.init(),f.init()}catch(i){}return{sendData:function(){d.sendData.apply(d,arguments)},sendUserActionData:function(){f.sendUserActionData.apply(f,arguments)},sendErrorInfo:function(){h.sendErrorInfo.apply(h,arguments)}}}(window,document);