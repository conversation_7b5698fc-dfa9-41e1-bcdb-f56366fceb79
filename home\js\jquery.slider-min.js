!function(){Function.prototype.inheritFrom=function(t,e){var i=function(){};if(i.prototype=t.prototype,this.prototype=new i,this.prototype.constructor=this,this.prototype.baseConstructor=t,this.prototype.superClass=t.prototype,e)for(var s in e)this.prototype[s]=e[s]},Number.prototype.jSliderNice=function(t){var e,i=/^(-)?(\d+)([\.,](\d+))?$/,s=Number(this),n=String(s),o="",r=" ";if(e=n.match(i)){var a=e[2],h=e[4]?Number("0."+e[4]):0;if(h){var l=Math.pow(10,t?t:2);if(h=Math.round(h*l),sNewDecPart=String(h),o=sNewDecPart,sNewDecPart.length<t)for(var u=t-sNewDecPart.length,c=0;c<u;c++)o="0"+o;o=","+o}else if(t&&0!=t){for(var c=0;c<t;c++)o+="0";o=","+o}var d;if(Number(a)<1e3)d=a+o;else{var c,p="";for(c=1;3*c<a.length;c++)p=r+a.substring(a.length-3*c,a.length-3*(c-1))+p;d=a.substr(0,3-3*c+a.length)+p+o}return e[1]?"-"+d:d}return n},this.jSliderIsArray=function(t){return"undefined"!=typeof t&&(t instanceof Array||!(t instanceof Object)&&"[object Array]"==Object.prototype.toString.call(t)||"number"==typeof t.length&&"undefined"!=typeof t.splice&&"undefined"!=typeof t.propertyIsEnumerable&&!t.propertyIsEnumerable("splice"))}}(),function(){var t={};this.jSliderTmpl=function e(i,s){var n=/\W/.test(i)?new Function("obj","var p=[],print=function(){p.push.apply(p,arguments);};with(obj){p.push('"+i.replace(/[\r\t\n]/g," ").split("<%").join("\t").replace(/((^|%>)[^\t]*)'/g,"$1\r").replace(/\t=(.*?)%>/g,"',$1,'").split("\t").join("');").split("%>").join("p.push('").split("\r").join("\\'")+"');}return p.join('');"):t[i]=t[i]||e(i);return s?n(s):n}}(),function(t){this.Draggable=function(){this._init.apply(this,arguments)},Draggable.prototype={oninit:function(){},events:function(){},onmousedown:function(){this.ptr.css({position:"absolute"})},onmousemove:function(t,e,i){this.ptr.css({left:e,top:i})},onmouseup:function(){},isDefault:{drag:!1,clicked:!1,toclick:!0,mouseup:!1},_init:function(){if(arguments.length>0){this.ptr=t(arguments[0]),this.outer=t(".draggable-outer"),this.is={},t.extend(this.is,this.isDefault);var e=this.ptr.offset();this.d={left:e.left,top:e.top,width:this.ptr.width(),height:this.ptr.height()},this.oninit.apply(this,arguments),this._events()}},_getPageCoords:function(t){return t.targetTouches&&t.targetTouches[0]?{x:t.targetTouches[0].pageX,y:t.targetTouches[0].pageY}:{x:t.pageX,y:t.pageY}},_bindEvent:function(t,e,i){this.supportTouches_?t.get(0).addEventListener(this.events_[e],i,!1):t.bind(this.events_[e],i)},_events:function(){var e=this;this.supportTouches_=t.browser.webkit&&navigator.userAgent.indexOf("Mobile")!=-1,this.events_={click:this.supportTouches_?"touchstart":"click",down:this.supportTouches_?"touchstart":"mousedown",move:this.supportTouches_?"touchmove":"mousemove",up:this.supportTouches_?"touchend":"mouseup"},this._bindEvent(t(document),"move",function(t){e.is.drag&&(t.stopPropagation(),t.preventDefault(),e._mousemove(t))}),this._bindEvent(t(document),"down",function(t){e.is.drag&&(t.stopPropagation(),t.preventDefault())}),this._bindEvent(t(document),"up",function(t){e._mouseup(t)}),this._bindEvent(this.ptr,"down",function(t){return e._mousedown(t),!1}),this._bindEvent(this.ptr,"up",function(t){e._mouseup(t)}),this.ptr.find("a").click(function(){if(e.is.clicked=!0,!e.is.toclick)return e.is.toclick=!0,!1}).mousedown(function(t){return e._mousedown(t),!1}),this.events()},_mousedown:function(e){this.is.drag=!0,this.is.clicked=!1,this.is.mouseup=!1;var i=this.ptr.offset(),s=this._getPageCoords(e);this.cx=s.x-i.left,this.cy=s.y-i.top,t.extend(this.d,{left:i.left,top:i.top,width:this.ptr.width(),height:this.ptr.height()}),this.outer&&this.outer.get(0)&&this.outer.css({height:Math.max(this.outer.height(),t(document.body).height()),overflow:"hidden"}),this.onmousedown(e)},_mousemove:function(t){this.is.toclick=!1;var e=this._getPageCoords(t);this.onmousemove(t,e.x-this.cx,e.y-this.cy)},_mouseup:function(e){this.is.drag&&(this.is.drag=!1,this.outer&&this.outer.get(0)&&(t.browser.mozilla?this.outer.css({overflow:"hidden"}):this.outer.css({overflow:"visible"}),t.browser.msie&&"6.0"==t.browser.version?this.outer.css({height:"100%"}):this.outer.css({height:"auto"})),this.onmouseup(e))}}}(jQuery),function(t){function e(){this.baseConstructor.apply(this,arguments)}t.slider=function(e,i){var s=t(e);return s.data("jslider")||s.data("jslider",new jSlider(e,i)),s.data("jslider")},t.fn.slider=function(e,i){function s(t){return void 0!==t}function n(t){return null!=t}var o,r=arguments;return this.each(function(){var a=t.slider(this,e);if("string"==typeof e)switch(e){case"value":if(s(r[1])&&s(r[2])){var h=a.getPointers();n(h[0])&&n(r[1])&&(h[0].set(r[1]),h[0].setIndexOver()),n(h[1])&&n(r[2])&&(h[1].set(r[2]),h[1].setIndexOver())}else if(s(r[1])){var h=a.getPointers();n(h[0])&&n(r[1])&&(h[0].set(r[1]),h[0].setIndexOver())}else o=a.getValue();break;case"prc":if(s(r[1])&&s(r[2])){var h=a.getPointers();n(h[0])&&n(r[1])&&(h[0]._set(r[1]),h[0].setIndexOver()),n(h[1])&&n(r[2])&&(h[1]._set(r[2]),h[1].setIndexOver())}else if(s(r[1])){var h=a.getPointers();n(h[0])&&n(r[1])&&(h[0]._set(r[1]),h[0].setIndexOver())}else o=a.getPrcValue();break;case"calculatedValue":var l=a.getValue().split(";");o="";for(var u=0;u<l.length;u++)o+=(u>0?";":"")+a.nice(l[u]);break;case"skin":a.setSkin(r[1])}else e||i||(jSliderIsArray(o)||(o=[]),o.push(slider))}),jSliderIsArray(o)&&1==o.length&&(o=o[0]),o||this};var i={settings:{from:1,to:10,step:1,smooth:!0,limits:!0,round:0,value:"5;7",dimension:""},className:"jslider",selector:".jslider-",template:jSliderTmpl('<span class="<%=className%>"><table><tr><td><div class="<%=className%>-bg"><i class="l"><i></i></i><i class="r"><i></i></i><i class="v"><i></i></i></div><div class="<%=className%>-pointer"><i></i></div><div class="<%=className%>-pointer <%=className%>-pointer-to"><i></i></div><div class="<%=className%>-label"><span><%=settings.from%></span></div><div class="<%=className%>-label <%=className%>-label-to"><span><%=settings.to%></span><%=settings.dimension%></div><div class="<%=className%>-value"><span></span><%=settings.dimension%></div><div class="<%=className%>-value <%=className%>-value-to"><span></span><%=settings.dimension%></div><div class="<%=className%>-scale"><%=scale%></div></td></tr></table></span>')};this.jSlider=function(){return this.init.apply(this,arguments)},jSlider.prototype={init:function(e,s){this.settings=t.extend(!0,{},i.settings,s?s:{}),this.inputNode=t(e).hide(),this.settings.interval=this.settings.to-this.settings.from,this.settings.value=this.inputNode.attr("value"),this.settings.calculate&&t.isFunction(this.settings.calculate)&&(this.nice=this.settings.calculate),this.settings.onstatechange&&t.isFunction(this.settings.onstatechange)&&(this.onstatechange=this.settings.onstatechange),this.is={init:!1},this.o={},this.create()},onstatechange:function(){},create:function(){var s=this;this.domNode=t(i.template({className:i.className,settings:{from:this.nice(this.settings.from),to:this.nice(this.settings.to),dimension:this.settings.dimension},scale:this.generateScale()})),this.inputNode.after(this.domNode),this.drawScale(),this.settings.skin&&this.settings.skin.length>0&&this.setSkin(this.settings.skin),this.sizes={domWidth:this.domNode.width(),domOffset:this.domNode.offset()},t.extend(this.o,{pointers:{},labels:{0:{o:this.domNode.find(i.selector+"value").not(i.selector+"value-to")},1:{o:this.domNode.find(i.selector+"value").filter(i.selector+"value-to")}},limits:{0:this.domNode.find(i.selector+"label").not(i.selector+"label-to"),1:this.domNode.find(i.selector+"label").filter(i.selector+"label-to")}}),t.extend(this.o.labels[0],{value:this.o.labels[0].o.find("span")}),t.extend(this.o.labels[1],{value:this.o.labels[1].o.find("span")}),s.settings.value.split(";")[1]||(this.settings.single=!0,this.domNode.addDependClass("single")),s.settings.limits||this.domNode.addDependClass("limitless"),this.domNode.find(i.selector+"pointer").each(function(t){var i=s.settings.value.split(";")[t];if(i){s.o.pointers[t]=new e(this,t,s);var n=s.settings.value.split(";")[t-1];n&&new Number(i)<new Number(n)&&(i=n),i=i<s.settings.from?s.settings.from:i,i=i>s.settings.to?s.settings.to:i,s.o.pointers[t].set(i,!0)}}),this.o.value=this.domNode.find(".v"),this.is.init=!0,t.each(this.o.pointers,function(t){s.redraw(this)}),function(e){t(window).resize(function(){e.onresize()})}(this)},setSkin:function(t){this.skin_&&this.domNode.removeDependClass(this.skin_,"_"),this.domNode.addDependClass(this.skin_=t,"_")},setPointersIndex:function(e){t.each(this.getPointers(),function(t){this.index(t)})},getPointers:function(){return this.o.pointers},generateScale:function(){if(this.settings.scale&&this.settings.scale.length>0){for(var t="",e=this.settings.scale,i=Math.round(100/(e.length-1)*10)/10,s=0;s<e.length;s++)t+='<span style="left: '+s*i+'%">'+("|"!=e[s]?"<ins>"+e[s]+"</ins>":"")+"</span>";return t}return""},drawScale:function(){this.domNode.find(i.selector+"scale span ins").each(function(){t(this).css({marginLeft:-t(this).outerWidth()/2})})},onresize:function(){var e=this;this.sizes={domWidth:this.domNode.width(),domOffset:this.domNode.offset()},t.each(this.o.pointers,function(t){e.redraw(this)})},limits:function(t,e){if(!this.settings.smooth){var i=100*this.settings.step/this.settings.interval;t=Math.round(t/i)*i}var s=this.o.pointers[1-e.uid];return s&&e.uid&&t<s.value.prc&&(t=s.value.prc),s&&!e.uid&&t>s.value.prc&&(t=s.value.prc),t<0&&(t=0),t>100&&(t=100),Math.round(10*t)/10},redraw:function(t){return!!this.is.init&&(this.setValue(),this.o.pointers[0]&&this.o.pointers[1]&&this.o.value.css({left:this.o.pointers[0].value.prc+"%",width:this.o.pointers[1].value.prc-this.o.pointers[0].value.prc+"%"}),this.o.labels[t.uid].value.html(this.nice(t.value.origin)),void this.redrawLabels(t))},redrawLabels:function(t){function e(t,e,s){return e.margin=-e.label/2,label_left=e.border+e.margin,label_left<0&&(e.margin-=label_left),e.border+e.label/2>i.sizes.domWidth?(e.margin=0,e.right=!0):e.right=!1,t.o.css({left:s+"%",marginLeft:e.margin,right:"auto"}),e.right&&t.o.css({left:"auto",right:0}),e}var i=this,s=this.o.labels[t.uid],n=t.value.prc,o={label:s.o.outerWidth(),right:!1,border:n*this.sizes.domWidth/100};if(!this.settings.single){var r=this.o.pointers[1-t.uid],a=this.o.labels[r.uid];switch(t.uid){case 0:o.border+o.label/2>a.o.offset().left-this.sizes.domOffset.left?(a.o.css({visibility:"hidden"}),a.value.html(this.nice(r.value.origin)),s.o.css({visibility:"visible"}),n=(r.value.prc-n)/2+n,r.value.prc!=t.value.prc&&(s.value.html(this.nice(t.value.origin)+"&nbsp;&ndash;&nbsp;"+this.nice(r.value.origin)),o.label=s.o.outerWidth(),o.border=n*this.sizes.domWidth/100)):a.o.css({visibility:"visible"});break;case 1:o.border-o.label/2<a.o.offset().left-this.sizes.domOffset.left+a.o.outerWidth()?(a.o.css({visibility:"hidden"}),a.value.html(this.nice(r.value.origin)),s.o.css({visibility:"visible"}),n=(n-r.value.prc)/2+r.value.prc,r.value.prc!=t.value.prc&&(s.value.html(this.nice(r.value.origin)+"&nbsp;&ndash;&nbsp;"+this.nice(t.value.origin)),o.label=s.o.outerWidth(),o.border=n*this.sizes.domWidth/100)):a.o.css({visibility:"visible"})}}if(o=e(s,o,n),a){var o={label:a.o.outerWidth(),right:!1,border:r.value.prc*this.sizes.domWidth/100};o=e(a,o,r.value.prc)}this.redrawLimits()},redrawLimits:function(){if(this.settings.limits){var t=[!0,!0];for(key in this.o.pointers)if(!this.settings.single||0==key){var e=this.o.pointers[key],i=this.o.labels[e.uid],s=i.o.offset().left-this.sizes.domOffset.left,n=this.o.limits[0];s<n.outerWidth()&&(t[0]=!1);var n=this.o.limits[1];s+i.o.outerWidth()>this.sizes.domWidth-n.outerWidth()&&(t[1]=!1)}for(var o=0;o<t.length;o++)t[o]?this.o.limits[o].fadeIn("fast"):this.o.limits[o].fadeOut("fast")}},setValue:function(){var t=this.getValue();this.inputNode.attr("value",t),this.onstatechange.call(this,t)},getValue:function(){if(!this.is.init)return!1;var e=this,i="";return t.each(this.o.pointers,function(t){void 0==this.value.prc||isNaN(this.value.prc)||(i+=(t>0?";":"")+e.prcToValue(this.value.prc))}),i},getPrcValue:function(){if(!this.is.init)return!1;var e="";return t.each(this.o.pointers,function(t){void 0==this.value.prc||isNaN(this.value.prc)||(e+=(t>0?";":"")+this.value.prc)}),e},prcToValue:function(t){if(this.settings.heterogeneity&&this.settings.heterogeneity.length>0)for(var e=this.settings.heterogeneity,i=0,s=this.settings.from,n=0;n<=e.length;n++){if(e[n])var o=e[n].split("/");else var o=[100,this.settings.to];if(o[0]=new Number(o[0]),o[1]=new Number(o[1]),t>=i&&t<=o[0])var r=s+(t-i)*(o[1]-s)/(o[0]-i);i=o[0],s=o[1]}else var r=this.settings.from+t*this.settings.interval/100;return this.round(r)},valueToPrc:function(t,e){if(this.settings.heterogeneity&&this.settings.heterogeneity.length>0)for(var i=this.settings.heterogeneity,s=0,n=this.settings.from,o=0;o<=i.length;o++){if(i[o])var r=i[o].split("/");else var r=[100,this.settings.to];if(r[0]=new Number(r[0]),r[1]=new Number(r[1]),t>=n&&t<=r[1])var a=e.limits(s+(t-n)*(r[0]-s)/(r[1]-n));s=r[0],n=r[1]}else var a=e.limits(100*(t-this.settings.from)/this.settings.interval);return a},round:function(t){return t=Math.round(t/this.settings.step)*this.settings.step,t=this.settings.round?Math.round(t*Math.pow(10,this.settings.round))/Math.pow(10,this.settings.round):Math.round(t)},nice:function(t){return t=t.toString().replace(/,/gi,"."),t=t.toString().replace(/ /gi,""),Number.prototype.jSliderNice?new Number(t).jSliderNice(this.settings.round).replace(/-/gi,"&minus;"):new Number(t)}},e.inheritFrom(Draggable,{oninit:function(t,e,i){this.uid=e,this.parent=i,this.value={},this.settings=this.parent.settings},onmousedown:function(t){this._parent={offset:this.parent.domNode.offset(),width:this.parent.domNode.width()},this.ptr.addDependClass("hover"),this.setIndexOver()},onmousemove:function(t,e){var i=this._getPageCoords(t);this._set(this.calc(i.x))},onmouseup:function(e){this.parent.settings.callback&&t.isFunction(this.parent.settings.callback)&&this.parent.settings.callback.call(this.parent,this.parent.getValue()),this.ptr.removeDependClass("hover")},setIndexOver:function(){this.parent.setPointersIndex(1),this.index(2)},index:function(t){this.ptr.css({zIndex:t})},limits:function(t){return this.parent.limits(t,this)},calc:function(t){var e=this.limits(100*(t-this._parent.offset.left)/this._parent.width);return e},set:function(t,e){this.value.origin=this.parent.round(t),this._set(this.parent.valueToPrc(t,this),e)},_set:function(t,e){e||(this.value.origin=this.parent.prcToValue(t)),this.value.prc=t,this.ptr.css({left:t+"%"}),this.parent.redraw(this)}})}(jQuery);