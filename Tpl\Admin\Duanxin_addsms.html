

<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
    <legend>通知短信模板</legend>
</fieldset>
<button type="button" id="addsms" class="layui-btn layui-btn-danger">&nbsp;&nbsp;增加&nbsp;&nbsp;</button>
<div class="layui-form">
<table width="100%" class="layui-table">
    <colgroup>
    <col width="150">
    <col width="150">
    <col width="200">
    <col>
    </colgroup>
    <thead>
        <tr>
            <th width="30">id</th width="30">
            <th width="150">短信内容</th width="150">
            <th width="50">增加时间</th width="150">
            <th width="40">操作</th width="50">
        </tr> 
    </thead>
    <tbody>
        <volist name="data" id="vo">
            <tr>
                <td>{$vo.id}</td>
                <td>{$vo.content}</td>
                <th>{$vo.addtime|date='Y-m-d H:i:s',###}</th>
                <td>
                    <button class="layui-btn layui-btn-sm layui-btn-normal"> <a href="javascript:smsdel({$vo.id})"><i class="layui-icon"></i>删除</a> </button>
                </td>
            </tr>
        </volist>
    </tbody>
</table width="100%">
</div>
<script>
    $('#addsms').on('click',function (){
        layer.open({
        title: '添加短信内容', 
        btn: ['确定'],
        area:['520px','200px'],
        content:'<div class="layui-form-item">' +
				'<label class="layui-form-mid" style="margin-left: 44px;">短信内容</label>' +
				'<input type="text" id="addsmss" required lay-verify="required" placeholder="请输入内容" autocomplete="off" class="layui-input" style="width: 67%;">  ' +
				'</div>',
            yes: function (index) {
                if($('#addsmss').val().length > 1){
                    $.post(
                        "{:U(GROUP_NAME.'/Duanxin/addsmsajax')}",
                        { edu: $('#addsmss').val() },
                        function (data, state) {
                            if (data.status != 1) {
                                layer.msg(data.info);
                            } else {
                                layer.close(index);
                                layer.msg("增加成功");
                                setTimeout(function () { location.reload(); }, 1000);
                            }
                        }
                    );
                }else{
                    layer.msg("短信内容不能为空!", {icon: 5, anim: 6});
                }
            }
        });
    });

    function smsdel(obj) {
        layer.open({
            title: '删除'
            ,content: '是否删除本条短信模板',
            yes: function (index){
                $.post(
                    "{:U(GROUP_NAME.'/Duanxin/delsmsajax')}",
                    { id: obj },
                    function (data, state) {
                        if (data.status != 1) {
                            layer.msg(data.info);
                        } else {
                            layer.close(index);
                            layer.msg("删除成功");
                            setTimeout(function () { location.reload(); }, 1000);
                        }
                    }
                );
            }
        }); 
    }
</script>

