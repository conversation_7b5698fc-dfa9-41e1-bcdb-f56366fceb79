<html>
	<head>
		<link rel="stylesheet" href="/Public/Manage/css/table.css">
		<title>转账截图： - 站长源码库（zzmaku.com） </title>
		<style>
			.nestable{
				margin: 0 auto;
				*margin: 20px !important;
				*border: 1px solid #5961c7;
				width: 1170px !important;
				height:888px !important;
				padding: 0px !important;
				background:url(Public/images/hd_bg.png) repeat-x;
				background-size: cover;
	
			}
			.nestable{
				background-color: #999;
			}
			.content{
				width: 100%;
				padding-top:300px !important;
				*margin: 16px auto;
			}
			.pp{
				-webkit-box-sizing:border-box;
				-moz-box-sizing:border-box;
				box-sizing:border-box;
				display: block;
				float:left;
				width: 100%;
				font-size: 12px;
				height:30px;
				line-height:30px;
			}
			.tits{
				float:left; 
				width:585px;
				text-align: right;
			}
			.co{float:left; }
			.copy{
				width: 100%;
				margin: 16px auto;
				text-align: center;
				}
			.copy button{
			 padding: 8px 25px;
			margin:15px auto;
			}
.copy a{
	width: 10%;
    padding: 8px 30px;
	margin:15px auto;
    height: 32px;
    font-size: 12px;
    font-weight: normal;
    text-align: center;
    border: 1px solid transparent;
    border-radius: 4px;
	background-color: #f2f2f2;
 line-height: 14px;
  text-decoration:none;
}
.copy a:hover{
  text-decoration:none;
}
		</style>
		<script src="/Public/Manage/js/html2canvas.js"></script>
	</head>
	<body>
		<div id="div_hd" class="nestable">
			<div class="content">
			<!--	<div class="pp"><div class="tits">转账批次号：</div><div class="co"><Somnus:block name="转账批次号" /></div></div>
		    -->
			<!--	<div class="pp"><div class="tits">转出单位：</div><div class="co"><Somnus:block name="转出单位" /></div></div>
			-->
			<!--	<div class="pp"><div class="tits">转出账户：</div><div class="co"><Somnus:block name="转出账户" /></div></div>
			-->
			<!--	<div class="pp"><div class="tits">转出账号地区：</div><div class="co"><Somnus:block name="转出账号地区" /></div></div>
			-->
				<div class="pp"><div class="tits">收款人姓名：</div><div class="co">{$info.name}</div></div>
				<div class="pp"><div class="tits">收款银行：</div><div class="co">{$info.bankname}</div></div>
				<div class="pp"><div class="tits">收款账户：</div><div class="co">{$info.bankcard}</div></div>
				<div class="pp"><div class="tits">币种：</div><div class="co">人民币</div></div>
				<div class="pp"><div class="tits">转账金额：</div><div class="co">{$order.money}</div></div>
				<div class="pp"><div class="tits">转账时间：</div><div class="co"><?=date('Y/m/d H:i:s')?></p></div></div>
				<div class="pp"><div class="tits">转账类型：</div><div class="co">签约金融企业--网贷放款预约转账</div></div>
				<div class="pp"><div class="tits">执行方式：</div><div class="co">实时到账</div></div>
				<div class="pp"><div class="tits">状态：</div><div class="co"><span style="color:#006600;">转账失败</span></div></div>
				<div class="pp"><div class="tits">银行备注：</div><div class="co">{$order.shuoming}</div></div>
				<div class="pp"><div class="tits">处理结果：</div><div class="co"><span style="color:#E53333;">未处理</span></div></div>
				<div class="pp"><div class="tits">用户备注：</div><div class="co"></div></div>
			</div>		
		</div>
			<div class="copy">
			
			<!-- <button class="but"  style="display:block;" >点我截屏</button> -->
			<a class="down" style="display:none;" href="" download="hd">下载</a>
			 <div id="box"></div>
			</div>
			
<script>
    $(document).ready(function () {

        html2canvas(document.getElementById('div_hd'), { 
            onrendered: function (canvas) { 
                var canvasData = canvas.toDataURL(); 
                var eg = new Image(); 
                eg.src = canvasData;
                $("button").on("click", function () {
					$(".down").css("display","block");
					$(".but").css("display","none");
					$box = $("#box");
                    $box.prepend(eg);
					document.querySelector(".down").setAttribute('href',canvasData);
                })
            }, 
            // useCORS: true// 此代码针对跨域问题 
        });
    })
            </script>
	</body>
</html>