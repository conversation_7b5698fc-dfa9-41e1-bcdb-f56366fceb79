<?php
/*
 * @Author: <PERSON>
 * @Name: 宅生科技
 * @Date: 2019-12-25 21:35:36
 * @LastEditTime : 2019-12-26 00:15:32
 * @LastEditors  : Jin Ou
 */
/**
 * Created by PhpStorm.
 * User: Somnus
 * Date: 2016/11/9
 * Time: 17:07
 */

class SystemAction extends CommonAction{

    public function index(){
        $this->title = '系统设置';
        // 验证码
        /*$smsapi = C('cfg_SMS_API'); //短信网关
		$user = C('cfg_SMS_USER'); //短信平台帐号
		$pass = C('cfg_sms_pass'); //短信平台密码
		$uid = C('cfg_sms_uid');
        $resultyzm = file_get_contents($smsapi."action=overage&userid=$uid&account=$user&password=$pass");
        if($resultyzm){
			$xmlyzm = simplexml_load_string($resultyzm);
			$jsonyzm = json_encode($xmlyzm);
			$arrayyzm = json_decode($jsonyzm,TRUE);
			$this->assign('yzm',$arrayyzm['overage']);
		}
        // 通知
        $smsapiTZ = C('cfg_SMSTZ_API'); //短信网关
		$userTZ = C('cfg_SMSTZ_USER'); //短信平台帐号
		$passTZ = C('cfg_smsTZ_pass'); //短信平台密码
		$uidTZ = C('cfg_smsTZ_uid');
        $resultTZ = file_get_contents($smsapiTZ."action=overage&userid=$uidTZ&account=$userTZ&password=$passTZ");
        if($resultTZ){
			$xmltz = simplexml_load_string($resultTZ);
			$jsontz = json_encode($xmltz);
			$arraytz = json_decode($jsontz,TRUE);
			$this->assign('tz',$arrayyzm['overage']);
		}*/
        
        
        if(IS_POST){
            $sitename = I('sitename','','trim');
            $sitetitle = I('sitetitle','','trim');
            if(empty($sitename) || empty($sitetitle)){
                $this->error('网站标题、网站名称不能为空!');
            }
            $file = CONF_PATH.'/config.site.php';
            $arr = array_keys($_POST);
            $siteConfig = array();
            for($i=0;$i<count($arr);$i++){
                $siteConfig['cfg_'.$arr[$i]] = htmlspecialchars($_POST[$arr[$i]]);
            }
            if(!writeArr($siteConfig,$file)){
                $this->error('保存失败!');
            }
            $this->success('保存成功!');
            exit;
        }
        $this->display();
    }
}