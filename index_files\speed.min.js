void function(a,b){function c(a,c,d){d=d||10;var e=new Date;e.setTime((new Date).getTime()+1e3*d),b.cookie=a+"="+escape(c)+";path=/;expires="+e.toGMTString()}function d(a){var c=b.cookie.match(new RegExp("(^| )"+a+"=([^;]*)(;|$)"));return null!=c?unescape(c[2]):null}function e(a,b,c){if(a.length===+a.length){for(var d=0,e=a.length;e>d;d++)if(b.call(c,d,a[d],a)===!1)return}else for(var f in a)if(a.hasOwnProperty(f)&&b.call(c,f,a[f],a)===!1)return}var f=a.alogObjectName||"alog",g=a[f]=a[f]||function(){a[f].l=a[f].l||+new Date,(a[f].q=a[f].q||[]).push(arguments)},h="speed";g("define",h,function(){var f=g.timestamp,i=g.tracker(h),j=i.get("send"),k=!1;i.set("protocolParameter",{fsItems:null,options:null,send:null,t:null,platform:null}),i.on("send",function(a){for(var b in a)0==a[b]?a[b]="0":"",/^(c_.*|ht|drt|lt|fs|wt|wtt)$/.test(b)&&a[b]&&(a[b]=Math.abs(f(a[b])))});var l=i.get("options"),m={product_id:l.product_id||"0",page_id:l.random<=l.sample?l.page_id:"0"};!function(){function a(a){var b=/(chrome)\/(\d+\.\d)/,c=/(\d+\.\d)?(?:\.\d)?\s+safari\/?(\d+\.\d+)?/,d=/(opera)(?:.*version)?[ \/]([\w.]+)/,e=/(msie) ([\w.]+)/,f=/(mozilla)(?:.*? rv:([\w.]+))?/,a=a.toLowerCase(),g=b.exec(a)||d.exec(a)||e.exec(a)||a.indexOf("compatible")<0&&f.exec(a)||[];return c.test(a)&&!/chrome/.test(a)&&(g[1]="safari",g[2]=RegExp.$1||RegExp.$2),{browser:g[1]||"unknown",version:g[2]||"0"}}function b(){var b=a(navigator.userAgent),c=b.browser;if("msie"==c)if(document.documentMode){var d=b.version.substring(0,1);window.performance?(c+="9.0",m.ebrowser=d+"9"+document.documentMode):(c+="8.0",m.ebrowser=d+"8"+document.documentMode)}else c+=b.version;var e={"msie6.0":16,"msie7.0":17,"msie8.0":18,"msie9.0":19,chrome:20,mozilla:30,safari:40,opera:50};m.browser=e[c]||0}b()}();for(var n=i.get("fsItems"),o=b.documentElement.clientHeight,p=i.get("fs"),q=0;n&&q<n.length;q++){var r=n[q],s=r.img,t=r.time,u=s.offsetTop||0;u>0&&o>u&&(p=t>p?t:p)}i.set("fs",p);var v=d("PMS_JT");if(v){c("PMS_JT","",-1);try{v=v.match(/{["']s["']:(\d+),["']r["']:["']([\s\S]+)["']}/),v=v&&v[1]&&v[2]?{s:parseInt(v[1]),r:v[2]}:{}}catch(w){v={}}v.r&&b.referrer.replace(/#.*/,"")!=v.r||f(v.s)<-100&&i.set("wt",v.s)}var x={dns:0,ct:0,st:0,tt:0};if(a.performance&&performance.timing){var y=performance.timing,z=y.domainLookupStart;x.dns=y.domainLookupEnd,x.ct=y.connectEnd,x.st=y.responseStart,x.tt=y.responseEnd,x.dct=y.domComplete,x.olt=y.loadEventEnd,e(x,function(a,b){x[a]=Math.max(b-z,0)}),f(z)<0&&(m.wtt=z)}e(x,function(a,b){m[a]=b});var A=a.screen;A&&(m._screen=A.width+"*"+A.height+"|"+A.availWidth+"*"+A.availHeight),m.mnt=(navigator.connection||navigator.mozConnection||navigator.webkitConnection||{type:"0"}).type;try{if(a.JSON&&a.localStorage&&localStorage.getItem("PMS_FT")){var B=JSON.parse(localStorage.getItem("PMS_FT")),C=[];e(B,function(a,b){var c=[];e(b,function(a,b){c.push('"'+a+'":'+b)},this),C.push('"'+a+'":{'+c.join(",")+"}")},this),m.fis_timing="{"+C.join(",")+"}",localStorage.removeItem("PMS_FT")}}catch(w){}return i.create({postUrl:l.log_path||"http://static.tieba.baidu.com/tb/pms/img/st.gif"}),j&&!k&&(k=!0,i.send("timing",m)),i})}(window,document);