

<!DOCTYPE html>

<html lang="en">

<head>

<meta charset="UTF-8">

<title> <Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>

<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">

<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">

<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">

<style>

.htan,.emask {

    display: none;

}

.htan-cont{

    text-align:center;

    height:80px;

}

.htan-cont .tex{

    color: #4c4c4c;

    line-height: 60px;

    font-size:18px;

}

.hgray {

	background: #cccccc;

}

.cominfo {

    padding-right: 0px;

}

.input-group {

    padding: 10px 0;

}

</style>

<style>

#shareit {

  -webkit-user-select: none;

  display: none;

  position: absolute;

  width: 100%;

  height: 100%;

  background: rgba(0,0,0,0.85);

  text-align: center;

  top: 0;

  left: 0;

  z-index: 105;

}

#shareit img {

  max-width: 100%;

}

.arrow {

  position: absolute;

  right: 1%;

  top: 5%;

}

#share-text {

  margin-top: 400px;

}

</style>

</head>

<script>

document.addEventListener('plusready',function(){

var webview = plus.webview.currentWebview();

plus.key.addEventListener('backbutton', function() {

webview.canBack(function(e) {

        if (e.canBack) {

                webview.back();

        } else {

            webview.close();//hide,quit

        }

    })

});



});

</script>

<body class="bg">

    <!-- header -->

    <header class="mui-bar mui-bar-nav hnav">

    	<a href="{:U('User/index')}" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>

	    <h1 class="mui-title">APP下载</h1>

	</header>

	<!-- header end-->

<div class="mui-content">

	<a href="                                          ">点击安卓版本下载</a>

    <br/>

    <br/>

    

		<a href="                                          ">点击苹果版本下载（请用浏览器(苹果自带的浏览器除外)下载，下载完然后描述文件点一下信任就可以安装了 或者下载下来用PP助手安装）</a>

      <br/>

</div>

<div style="clear:both"></div>

  <div style="margin-top:10px">

        <p>安卓扫码下载</p>

       

        <img src="__PUBLIC__/home/<USER>/anzuochkj.png">

        </div>

         <div style="margin-top:10px">

        <p>苹果扫码下载（请用浏览器(苹果自带的浏览器除外)下载，下载完然后描述文件点一下信任就可以安装了 或者下载下来用PP助手安装）</p>

       

        <img  src="__PUBLIC__/home/<USER>/pingguochkj.png">

           </div>

<!-- step -->

<!--share-->

<div id="shareit">

  <img class="arrow" src="__PUBLIC__/home/<USER>/guide1.png">

</div>

<!--share-->

<script src="__PUBLIC__/home/<USER>/jquery.js"></script>

<script>

//分享朋友圈

function sharewechat(){

	$("#shareit").show();

}

$("#shareit").click(function(){

	$("#shareit").hide();

});

</script>

<div style="display: none;">

	<Somnus:sitecfg name="sitecode"/>

</div>

</body>

</html>