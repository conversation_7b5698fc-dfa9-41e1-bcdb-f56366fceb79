<?php
class InfoAction extends CommonAction{
	private $userinfo;
	function _initialize(){
		$user = $this->getLoginUser();
		if(!$user){
			$this->redirect('User/login');
		}
		$Userinfo = D("userinfo");
		$info = $Userinfo->where(array('user' => $this->getLoginUser()))->find();
		if(!$info){
			$infoid = $Userinfo->add(array('user' => $this->getLoginUser()));
			$info = array('id' => $infoid,'user' => $this->getLoginUser());
		}
		$this->userinfo = $info;
	}
	
	public function index(){
		$Userinfo = D("userinfo");
		$info = $Userinfo->where(array('user' => $this->getLoginUser()))->find();
		$arr = array(
			'baseinfo' => 0,
			'unitinfo' => 0,
			'bankinfo' => 0,
			'zhimainfo'=> 0,
			'wechat'   => 0,
			'phoneinfo' => 0,
			'social'    => 0,
			'sign'=>0
		);
		//判断资料完整性
		if($info['name'] && $info['usercard'] && $info['cardphoto_1'] && $info['cardphoto_2'] && $info['cardphoto_3'] ){
			$arr['baseinfo'] = 1;
		}
		if( $info['personname_1'] && $info['personphone_1'] && $info['persongx_1'] && $info['personname_2'] && $info['personphone_2'] && $info['persongx_2']){
			$arr['unitinfo'] = 1;
		}
		if($info['bankcard'] && $info['bankname']){
			$arr['bankinfo'] = 1;
		}
		if($info['alipay']){
			$arr['zhimainfo'] = 1;
		}
		if($info['wechat']){
			$arr['wechat'] = 1;
		}
		if($info['phone']){
			$arr['phoneinfo'] = 1;
		}
		if($info['qq'] && $info['wx']){
			$arr['social'] = 1;
		}
		if($info['signature']){
			$arr['sign'] = 1;
		}
		$this->info = $arr;
		$Otherinfo = D("Otherinfo");
		$otherinfo = $Otherinfo->where(array('user' => $this->getLoginUser()))->find();
		$this->assign("otherinfo",$otherinfo);
		$this->display();
	}
	
	//身份信息
	//姓名、身份证号码、住址
	public function baseinfo(){
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$Userinfo = D("userinfo");
			$status = $Userinfo->where(array('user' => $this->getLoginUser()))->save($_POST);
			if(!$status){
				$data['msg'] = "操作失败";
			}else{
				$data['status'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo",$this->userinfo);
		$this->display();
	}

	//单位信息
	public function unitinfo(){
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$Userinfo = D("userinfo");
			$status = $Userinfo->where(array('user' => $this->getLoginUser()))->save($_POST);
		    //$sql=$Userinfo->getlastsql();echo $sql;exit;
			if(!$status){
				$data['msg'] = "操作失败";
			}else{
				$data['status'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo",$this->userinfo);
		$this->display();
	}

	//银行卡信息
	public function bankinfo(){
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$bankcard = I("bankcard",0,'trim');
			if(strlen($bankcard) < 16 || strlen($bankcard) > 20){
				$data['msg'] = "不是有效的银行卡号!";
			}else{
				$Userinfo = D("userinfo");
				$status = $Userinfo->where(array('user' => $this->getLoginUser()))->save($_POST);
				if(!$status){
					$data['msg'] = "操作失败";
				}else{
					$data['status'] = 1;
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		$userinfo = $this->userinfo;
		$arr = array(
			'bankinfo' => 0
		);
		if($userinfo['bankcard'] && $userinfo['bankname']){
			$arr['bankinfo'] = 1;
		}
		$this->info = $arr;
		$this->assign("userinfo",$userinfo);
		$this->display();
	}
	
	//芝麻信用授权确认
	public function zhimastepone(){
		$userinfo = $this->userinfo;
		if($userinfo['alipay']){
			$this->redirect('Info/index');
		}
		$this->display();
	}
	
	//芝麻信用授权
	public function zhimasteptwo(){
		$userinfo = $this->userinfo;
		if($userinfo['alipay']){
			$this->redirect('Info/index');
		}
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$code = I("code",'','trim');
			if(strlen($code) != 6){
				$data['msg'] = "短信验证码输入有误";
			}else{
				//判断验证码是否正确
				$Smscode = D("smscode");
				$info = $Smscode->where(array('phone' => $userinfo['user']))->order("sendtime desc")->find();
				if(!$info || $info['code'] != $code){
					$data['msg'] = "短信验证码输入有误";
				}elseif( (time()-30*60) > $info['sendtime']){
					$data['msg'] = "验证码已过期,请重新获取!";
				}else{
					$Userinfo = D("userinfo");
					$status = $Userinfo->where(array('user' => $userinfo['user']))->save(array('alipay' => '1'));
					if($status){
						$data['status'] = 1;
					}else{
						$data['msg'] = "授权失败!";
					}
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		$str = substr($userinfo['user'],0,3);
		$phone = $str;
		$str = substr($userinfo['user'],7,4);
		$phone .= '****' . $str;
		$this->phone = $phone;
		$this->assign("userinfo",$this->userinfo);
		$this->display();
	}

	public function otherinfo(){
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$Otherinfo = D("otherinfo");
			$_POST['user']= $this->getLoginUser();
			$_POST['addtime']= time();
			$status = $Otherinfo->data($_POST)->add();
			if(!$status){
				$data['msg'] = "操作失败";
			}else{
				$data['status'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$otherinfo = D('otherinfo')->where(array('user'=>$this->getLoginUser()))->find();
		$this->assign("otherinfo",$otherinfo);
		$this->display();

	}
	
	
	public function wechat(){
		$userinfo = $this->userinfo;
		if($userinfo['alipay']){
			$this->redirect('Info/index');
		}
		$code = I("code",'','trim');
		if($code && substr($code,0,1) == 'a'){
			$Userinfo = D("userinfo");
			$Userinfo->where(array('user' => $this->getLoginUser()))->save(array('wechat' => 1));
		}
		$this->redirect('Info/index');
	}
	
	
	public function phoneinfo(){
		$userinfo = $this->userinfo;
		// if($userinfo['phone']){
		// 	$this->redirect('Info/index');
		// }
		if(IS_POST){
			$data = array('status' => 0,'msg' => '未知错误');
			$code = I("code",'','trim');
			$pass = I("pass",'','trim');
			if(!$code){
				$data['msg'] = "请输入正确的验证码!";
			}else{
				if(!$pass){
					$data['msg'] = "请输入正确的服务密码!";
				}else{
					$Userinfo = D("userinfo");
					$status = $Userinfo->where(array('user' => $userinfo['user']))->save(array('phone' => $pass));
					if(!$status){
						$data['msg'] = "操作失败!";
					}else{
						$data['status'] = 1;
					}
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo",$userinfo);
		$this->display();
	}	
		//设计认证
		public function social(){
			if(IS_POST){
				$data = array('status' => 0,'msg' => '未知错误');
				$Userinfo = D("userinfo");
				$status = $Userinfo->where(array('user' => $this->getLoginUser()))->save($_POST);
				if(!$status){
					$data['msg'] = "操作失败";
				}else{
					$data['status'] = 1;
				}
				$this->ajaxReturn($data);
				exit;
			}
			$this->assign("userinfo",$this->userinfo);
			$this->display();
		}
		//手写签名
		public function qm(){
			if(IS_POST){
				$data = array('status' => 0,'msg' => '未知错误');
				$Userinfo = D("userinfo");
				$status = $Userinfo->where(array('user' => $this->getLoginUser()))->save($_POST);
				if(!$status){
					$data['msg'] = "操作失败";
				}else{
					$data['status'] = 1;
				}
				$this->ajaxReturn($data);
				exit;
			}
			$this->assign("userinfo",$this->userinfo);
			$this->display();
		}
		
		public function contract(){
			$Order = D("order");
			$Contract = D('contract');
			$Userinfo = D("userinfo");
			$user = $this->getLoginUser();
			if(!$user){
				$this->redirect('User/login');
			}else{
				$order = $Order->where(array('user'=>$user))->find();
				$userinfo = $Userinfo->where(array('user'=>$user))->find();
				if($order){
					$datas = $Contract->where(array('id'=>1))->find();
			        $datas = $datas['contract'];
			        $sign = '<img src=\'' . $userinfo['signature'] . '\' width=\'110px\' />';
			        $fuwufei = C('cfg_fuwufei');
			        $fuwufei = explode(",",$fuwufei);
			        $rixi = round($fuwufei[$order['months']-1] / 30,2);
			        // var_dump($fuwufei[$order['months']-1]);
			        $datas = str_replace('{ 合同编号 }' , $order['ordernum'] , $datas);
			        $datas = str_replace('{ 合同日期 }' , date('Y-m-d h:i:s',$order['addtime']) , $datas);
			        $datas = str_replace('{ 借款方 }' , $userinfo['name'] , $datas);
			        $datas = str_replace('{ 身份证号 }' , $userinfo['usercard'] , $datas);
			        $datas = str_replace('{ 手机号码 }' , $order['user'] , $datas);
			        $datas = str_replace('{ 个人签名 }' , $sign , $datas);
			        $datas = str_replace('{ 借款日期 }' , date('Y-m-d h:i:s',$order['addtime']) , $datas);
			        $datas = str_replace('{ 借款金额 }' , $order['money'] , $datas);
			        $datas = str_replace('{ 借款期限 }' , $order['months'] , $datas);
			        $datas = str_replace('{ 传人用户名 }' , $order['user'] , $datas);
			        $datas = str_replace('{ 收款银行 }' , $order['bank'] , $datas);
			        $datas = str_replace('{ 收款账号 }' , $order['banknum'] , $datas);
			        $datas = str_replace('{ 收款姓名 }' , $userinfo['name'] , $datas);
			        $datas = str_replace('{ 每期还款 }' , $order['monthmoney'] , $datas);
			        $datas = str_replace('{ 日利率 }' , $rixi , $datas);
			        $datas = str_replace('{ 还款日期 }' , date('d',$order['addtime']) , $datas);
			        
			        
			        
			        $this->datas = $datas;
				}
				$this->order = $order;
				$this->display();
			}
		}
}
