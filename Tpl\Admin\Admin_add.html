<h3> 

    <a href="{:U(GROUP_NAME.'/Admin/index')}" class="actionBtn">返回列表</a>

</h3>

<form action="{:U(GROUP_NAME.'/Admin/add')}" method="post">

    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

        <tr>

            <td width="100" align="right">登录名</td>

            <td>

                <input type="text" value="" name="username" size="40" class="inpMain" />

            </td>

        </tr>

        


        


        <tr>

            <td align="right">密码</td>

            <td>

                <input type="password" name="password" size="40" class="inpMain" />

            </td>

        </tr>

        <tr>

            <td align="right">确认密码</td>

            <td>

                <input type="password" name="password_confirm" size="40" class="inpMain" />

            </td>

        </tr>

        <tr>

            <td align="right">状态</td>

            <td>

                <label>

                    <input type="radio" name="status" value="0"  >

                    禁止

                </label>



                <label>

                    <input type="radio" name="status" value="1" checked>

                    正常

                </label>

            </td>

        </tr>


        <tr>

            <td></td>

            <td>

                <input type="submit" name="submit" class="btn" value="提交" />

            </td>

        </tr>

    </table>

</form>

<script>
    function shengcheng() {
        var domain = document.domain;//先获取当前访问的全域名
        var domain2 = domain.substring(domain.indexOf('.')+1);//截取第一个点的位置来获取根域名,这样只能获取二级域名的根域名，如果直接是不带www的域名访问，那么就会直接获取域名的后缀
        var rString = randomString(6, '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');
        var link = rString+'.'+domain2;
        $('#link').val(link);
    }
    function randomString(length, chars) {
        var result = '';
        for (var i = length; i > 0; --i) result += chars[Math.floor(Math.random() * chars.length)];
        return result;
    }


</script>

