<style>
    .sfzwrap {
        border: 1px solid #ccc;
        border-radius: 8px;
        height: 200px;
        width: 310px;
        display: inline-block;
    }

    .sfzp img {
        height: 186px;
        width: 100%;
    }

    .sfztip {
        color: #fff;
        font-size: 14px;
        line-height: 28px;
        width: 162px;
        top: 74%;
        left: 50%;
        margin-left: -81px;
        background: #9cf;
        opacity: .5;
        padding: 0 9px;
        text-align: center;
    }

    .prel {
        position: relative
    }

    .pab {
        position: absolute
    }

    .sfzp {
        margin: 6px
    }

    .sc {
        margin-bottom: 10px
    }

    .sfzp img {
        height: 186px
    }

    @media screen and (max-width: 361px) {
        .sfzwrap {
            height: 172px
        }

        .sfzp img {
            height: 158px
        }
    }

    .hcamera {
        width: 60px;
        height: 60px;
        top: 50%;
        left: 50%;
        margin-left: -30px;
        margin-top: -30px
    }

    .hcamera img {
        width: 100%
    }

    .sfzwrap input {
        position: absolute;
        width: 100%;
        height: 120px;
        opacity: 0
    }

    .ban,
    .list {
        position: fixed;
        text-align: center
    }

    .hsub {
        padding: 10px 0
    }

    .phoes {
        height: auto
    }

    .ban {
        width: 80%;
        height: 44px;
        line-height: 44px;
        border-radius: 10px;
        margin: 0 auto;
        background: #000;
        opacity: .8;
        color: #fff;
        font-size: 16px;
        top: 50%;
        left: 10%
    }
</style>
<script type="text/javascript">
    $(function () { $(".idTabs").idTabs(); });
</script>
<div class="idTabs">
    <ul class="tab">
        <li><a href="#main">系统设置</a></li>
        <li><a href="#daikuan">贷款设置</a></li>
        <li><a href="#api">短信接口设置</a></li>
        <li><a href="#youhuiquan">优惠券</a></li>
        <li><a href="#weixin">二维码收款设置</a></li>
        <li><a href="#bank">银行卡收款设置</a></li>
    </ul>
    <div class="items">
        <form action="{:U(GROUP_NAME.'/System/index')}" method="post">
            <div id="main">
                <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                    <tr>
                        <th width="131">名称</th>
                        <th>内容</th>
                    </tr>
                    <tr>
                        <td align="right">站点名称</td>
                        <td>
                            <input type="text" class="layui-input" name="sitename" value="{:C('cfg_sitename')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">站点标题</td>
                        <td>
                            <input type="text" class="layui-input" name="sitetitle" value="{:C('cfg_sitetitle')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">站点地址</td>
                        <td>
                            <input type="text" class="layui-input" name="siteurl" value="{:C('cfg_siteurl')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">站点关键字</td>
                        <td>
                            <input type="text" class="layui-input" name="sitekeywords" value="{:C('cfg_sitekeywords')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">站点描述</td>
                        <td>
                            <input type="text" class="layui-input" name="sitedescription"
                                value="{:C('cfg_sitedescription')}" size="80" class="inpMain" />
                        </td>
                    </tr>

                    <tr>
                        <td align="right">是否关闭网站</td>
                        <td>
                            <label for="siteclosed_0">
                                <input type="radio" name="siteclosed" id="siteclosed_0" value="0" <if
                                    condition="C('cfg_siteclosed') eq 0">checked</if> >
                                否
                            </label>
                            <label for="siteclosed_1">
                                <input type="radio" name="siteclosed" id="siteclosed_1" value="1" <if
                                    condition="C('cfg_siteclosed') eq 1">checked</if> >
                                是
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">网站关闭提示</td>
                        <td>
                            <textarea name="siteclosemsg" cols="83" rows="8"
                                class="textArea layui-textarea" />{:C('cfg_siteclosemsg')}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">ICP备案证书号</td>
                        <td>
                            <input type="text" class="layui-input" name="siteicp" value="{:C('cfg_siteicp')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">统计/客服代码调用</td>
                        <td>
                            <textarea name="sitecode" cols="83" rows="8"
                                class="textArea layui-textarea" />{:C('cfg_sitecode')}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">客服链接</td>
                        <td>
                            <input type="text" class="layui-input" name="kefu_link" value="{:C('cfg_kefu_link')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">客服qq</td>
                        <td>
                            <input type="text" class="layui-input" name="kefu_qq" value="{:C('cfg_kefu_qq')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">客服wx</td>
                        <td>
                            <input type="text" class="layui-input" name="kefu_wx" value="{:C('cfg_kefu_wx')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">App下载链接</td>
                        <td>
                            <input type="text" class="layui-input" name="download_link"
                                value="{:C('cfg_download_link')}" size="80" class="inpMain" />
                        </td>
                    </tr>
                </table>
            </div>
            <!-------------->
            <div id="daikuan">
                <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                    <tr>
                        <th width="131">名称</th>
                        <th>内容</th>
                    </tr>
                    <tr>
                        <td align="right">贷款最小金额</td>
                        <td>
                            <input type="text" class="layui-input" name="minmoney" value="{:C('cfg_minmoney')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">贷款最大金额</td>
                        <td>
                            <input type="text" class="layui-input" name="maxmoney" value="{:C('cfg_maxmoney')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">初始显示金额</td>
                        <td>
                            <input type="text" class="layui-input" name="definamoney" value="{:C('cfg_definamoney')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">允许选择月份</td>
                        <td>
                            <input type="text" class="layui-input" name="dkmonths" value="{:C('cfg_dkmonths')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">初始选择月份</td>
                        <td>
                            <input type="text" class="layui-input" name="definamonth" value="{:C('cfg_definamonth')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">服务费率</td>
                        <td>
                            <input type="text" class="layui-input" name="fuwufei" value="{:C('cfg_fuwufei')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>

                    <tr>

                        <td align="right">服务管理费(百分比)</td>

                        <td>

                            <input type="text" class="layui-input" name="gulifei" value="{:C('cfg_gulifei')}" size="80"
                                class="inpMain" />

                        </td>
                    <tr>

                        <td align="right">逾期一天的费用</td>

                        <td>

                            <input type="text" class="layui-input" name="yuqifei" value="{:C('cfg_yuqifei')}" size="80"
                                class="inpMain" />

                        </td>

                    </tr>

                    <tr>

                        <td align="right">推荐人提成比例如（0.05）</td>

                        <td>

                            <input type="text" class="layui-input" name="fenxiao" value="{:C('cfg_fenxiao')}" size="80"
                                class="inpMain" />

                        </td>

                    </tr>

                    <tr>
                        <td align="right">中级VIP会员费用</td>
                        <td>
                            <input type="text" class="layui-input" name="shenhefei" value="{:C('cfg_shenhefei')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>

                    <tr>
                        <td align="right">高级VIP会员费用</td>
                        <td>
                            <input type="text" class="layui-input" name="gaoji" value="{:C('cfg_gaoji')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>

                    <tr>
                        <td align="right">每月还款日</td>
                        <td>
                            <input type="text" class="layui-input" name="huankuanri" value="{:C('cfg_huankuanri')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">是否自动拒绝</td>
                        <td>
                            <label for="siteclosed_0">
                                <input type="radio" name="autodisdk" id="autodisdk_0" value="0" <if
                                    condition="C('cfg_autodisdk') eq 0">checked</if> >
                                否
                            </label>
                            <label for="siteclosed_1">
                                <input type="radio" name="autodisdk" id="autodisdk_1" value="1" <if
                                    condition="C('cfg_autodisdk') eq 1">checked</if> >
                                是
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">自动拒绝天数</td>
                        <td>
                            <input type="text" class="layui-input" name="autodisdkday" value="{:C('cfg_autodisdkday')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">拒绝提交等待天数</td>
                        <td>
                            <input type="text" class="layui-input" name="disdkdleyday" value="{:C('cfg_disdkdleyday')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                </table>
            </div>
            <div id="api">
                <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                    <tr>
                        <th width="131">名称</th>
                        <th>内容</th>
                    </tr>
                    <tr>
                        <td align="right">验证码短信剩余量</td>
                        <td>
                        	<span style="color:red">{$yzm}</span>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验证码短信网关</td>
                        <td>
                            <input type="text" class="layui-input" name="SMS_API" value="{:C('cfg_SMS_API')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>

                    <tr>
                        <td align="right">验证码短信用户名</td>
                        <td>
                            <input type="text" class="layui-input" name="SMS_USER" value="{:C('cfg_SMS_USER')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验证码短信密码</td>
                        <td>
                            <input type="text" class="layui-input" name="sms_pass" value="{:C('cfg_sms_pass')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验证码短信平台uid</td>
                        <td>
                            <input type="text" class="layui-input" name="sms_uid" value="{:C('cfg_sms_uid')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验证码短信签名</td>
                        <td>
                            <input type="text" class="layui-input" name="sms_name" value="{:C('cfg_sms_name')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    
                    <!---->
                    <tr>
                        <td align="right">通知短信剩余量</td>
                        <td>
                            <span style="color:red">{$tz}</span>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">通知短信网关</td>
                        <td>
                            <input type="text" class="layui-input" name="SMSTZ_API" value="{:C('cfg_SMSTZ_API')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>

                    <tr>
                        <td align="right">通知短信用户名</td>
                        <td>
                            <input type="text" class="layui-input" name="SMSTZ_USER" value="{:C('cfg_SMSTZ_USER')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">通知短信密码</td>
                        <td>
                            <input type="text" class="layui-input" name="smsTZ_pass" value="{:C('cfg_smsTZ_pass')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">通知短信平台uid</td>
                        <td>
                            <input type="text" class="layui-input" name="smsTZ_uid" value="{:C('cfg_smsTZ_uid')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">通知短信签名</td>
                        <td>
                            <input type="text" class="layui-input" name="smsTZ_name" value="{:C('cfg_smsTZ_name')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    
                    
                    
                    <tr>
                        <td align="right">当日获取最大次数</td>
                        <td>
                            <input type="text" class="layui-input" name="smsmaxcount" value="{:C('cfg_smsmaxcount')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验证码短信开关</td>
                        <td>
                            <input type="text" class="layui-input" name="sms_off" value="{:C('cfg_sms_off')}" size="80"
                                class="inpMain" /><span>1为确认，2为取消</span>
                        </td>
                    </tr>
                    
                    
                    
                    
                </table>
            </div>
            <!--  -->
            <div id="youhuiquan">
                <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                    <tr>
                        <th width="131">名称</th>
                        <th>内容</th>
                    </tr>
                    <tr>
                        <td align="right">是否开启优惠券</td>
                        <td>
                            <label for="Discount_0">
                                <input type="radio" name="Discount" id="Discount_0" value="0" <if
                                    condition="C('cfg_Discount') eq 0">checked</if> >
                                否
                            </label>
                            <label for="Discount_1">
                                <input type="radio" name="Discount" id="Discount_1" value="1" <if
                                    condition="C('cfg_Discount') eq 1">checked</if> >
                                是
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">优惠券免息数</td>
                        <td>
                            <input type="text" class="layui-input" name="Discountcount"
                                value="{:C('cfg_Discountcount')}" size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">优惠券有效天数</td>
                        <td>
                            <input type="text" class="layui-input" name="Discountday" value="{:C('cfg_Discountday')}"
                                size="80" class="inpMain" />
                        </td>
                    </tr>
                </table>
            </div>

            <!-- 微信二维码 -->
            <div  id="weixin"> 
            <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                <tr>
                    <td width="131" align="right">二维码</td>
                <td>
                    <div class="sfzwrap prel">
                        <div class="phoes" id="mode1" class="uploader-list">
                            <input name="weixin" type="hidden" id="sfz_zm" value="{:C('cfg_weixin')}" />
                            <div class="sfzp" id="sfz_zm_div">
                                <img src="{:C('cfg_weixin')}" alt="" / width="156" height="158">
                            </div>
                            <div style="display:none;">
                                <input type="file" id="sfz_zm_input"
                                    onchange="uploadImg('sfz_zm','sfz_zm_div',this);" />
                            </div>
                            <div class="hcamera pab" onclick="Selfile('sfz_zm_input');">
                                <img src="__PUBLIC__/home/<USER>/hcamera.png" alt="" width="60" height="60">
                            </div>
                            <div class="sfztip pab">微信二维码上传</div>
                        </div>
                    </div>
                </td>
            </tr>
            </table>
         </div>
            <div id="bank">
                <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                    <tr>
                        <th width="131">名称</th>
                        <th>内容</th>
                    </tr>
                    <tr>
                        <td align="right">收款银行名称</td>
                        <td>
                            <input type="text" name="bank_name" value="{:C('cfg_bank_name')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">收款银行户名</td>
                        <td>
                            <input type="text" name="bank_names" value="{:C('cfg_bank_names')}" size="80"
                                class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">收款银行账号</td>
                        <td>
                            <input type="text" name="bank_num" value="{:C('cfg_bank_num')}" size="80" class="inpMain" />
                        </td>
                    </tr>
                    <tr>
                        <td align="right">验资费用</td>
                        <td>
                            <input type="text" name="yzfpay" value="{:C('cfg_yzfpay')}" size="80" class="inpMain" />
                        </td>
                    </tr>

                </table>
            </div>

            <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
                <tr>
                    <td width="131"></td>
                    <td>
                        <input class="btn" type="submit" value="提交" />
                    </td>
                </tr>
            </table>
        </form>
    </div>
</div>
<script>
    var isupload = false;
    function showalert(msg) {
        $("#messageBox").html(msg);
        $("#messageBox").show();
        setTimeout(function () {
            $("#messageBox").hide();
        }, 2000);
    }
    function Selfile(inputid) {
        if (isupload != false) {
            showalert("其他文件正在上传...请稍后");
        } else {
            $("#" + inputid).click();
        }
    }
    function uploadImg(hiddenid, divid, obj) {
        var filename = $(obj).val();
        if (filename != '' && filename != null) {
            isupload = true;
            var pic = $(obj)[0].files[0];
            var fd = new FormData();
            fd.append('imgFile', pic);
            $.ajax({
                url: "__PUBLIC__/main/js/kindeditor/php/upload_json.php",
                type: "post",
                dataType: 'json',
                data: fd,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data && data.error == '0') {
                        showalert("上传成功");
                        var imgurl = data.url;
                        $("#" + divid).html('<img src="' + imgurl + '">');
                        $("#" + hiddenid).val(imgurl);
                    } else {
                        showalert("上传出错了...");
                    }
                },
                error: function () {
                    showalert("上传出错了...");
                }
            });
            isupload = false;
        }
        isupload = false;
    }
</script>