$(document).ready(function(){
    var account,
        password,
        code,
        referral,
        dpassword,
        msg,
        timer,
        forget,
        gtime,
        gstate = 0
    ;

    // 弹窗

    // 倒计时
    function myTimer(){
        var sec = 3;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }

    // 验证码收取倒计时
    function gainTime(){
        if(gstate == 0){
            gstate = -1;
            var des = 60;
            gtime = setInterval(function(){
                $('.gain-button').html(des--+'-秒');
                if(des == 0){
                    clearInterval(gtime);
                    $('.gain-button').html('获取');
                    gstate = 0;
                }
            },1000);
        }
    }
    // 验证码操作
    $('.gain-button').unbind("click").on('click',function(){
        account = $("#account").val();
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");


        if(account.length != 11){
            msg = '手机号码长度应为11位';
            message(msg);
            return;
        }

        if(gstate == 0){
            $.post( 
                "/index.php/User/sendsmscode",
                {
                    phone:account,
                    type:"login"
                },
                function(data,state){
                    if(state != "success"){
                        message("网络请求失败,请重试");
                    }else if(data.status != 1){
                        message(data.msg);
                    }else{
                        message("发送成功");
                        gainTime();
                    }
                }
            );
        }    
    });

    // 注册操作
    $("#register-button").unbind("click").on("click",function(){
        account = $("#account").val();
        password = $("#password").val();
        code = $("#code").val();
        referral = $("#referral").val(); 

        mesg_default();

        
        if (account == ''  || password == '' || code == '' || referral == '') {
            msg = '请完整信息';
            message(msg);

            return;
        } else {
            if(account.length != 11){ 
                if (msg == '') {
                    msg = '手机号码长度应为11位';
                } else {
                    msg += '</br>手机号码长度应为11位';
                }
            }
            if(password.length > 15 || password.length <6){ 
                if (msg == '') {
                    msg = '密码长度应为6-15位';
                } else {
                    msg += '</br>密码长度应为6-15位';
                }
            }
            if(sms_off == 1){
            	if(code.length != 6){ 
	                if (msg == '') {
	                    msg = '验证码长度应为6位';
	                } else {
	                    msg += '</br>验证码长度应为6位';
	                }
	            }
            }
            

            if (msg != '') {
                message(msg);
                return;
            }
            
            
        }
        
        $.post(
            "/index.php/User/signup",
            {
                phone:account,
                password:password,
                code:code,
                yao_ma:referral
            },
            function (data,state){

                if(state != "success"){
    
                    message("请求失败,请重试");
    
                    return false;
    
                }else if(data.status == 0){
    
                    message(data.msg);
    
                    return false;
    
                }else{
    
                    window.location.href = "/index.php/Index/index";
    
                }
    
            }
        );
    });

    // 登录操作
    $("#login-button").unbind("click").on("click",function(){
        account = $("#account").val();
        password = $("#password").val();

        mesg_default();

        if(account.length == 0 || password.length == 0){
            msg = '请完整信息';
            message(msg);

            return;
        }

        if (account.length != 11) {
            if (msg == '') {
                msg = '账号长度应为11位';
            } else {
                msg += '</br>账号长度应为11位';
            }
        }
        if (msg != '') {
            message(msg);
            return;
        }

        //console.log('yes');

        $.post(
            "/index.php/User/login",
            {
                phone:account,
                password:password
            },
            function (data,state){
                if(state != "success"){
                    message("请求失败,请稍后重试!");
                }else if(data.status != 1){
                    message(data.msg);
                }else{
                    window.location.href = "index.php/Index/index";
                }
                return false;
               
            }

        );
    });
  //判断用户是否存在
//   $(".gain-button").unbind("click").on("click",function(){
//      account = $("#account").val();
//      mesg_default();
//      if (account.length == 0) {
//         msg = '请完整信息';
//         message(msg);

//         return;
//     }
//     if (account.length != 11) {
//         if (msg == '') {
//             msg = '账号长度应为11位';
//         } else {
//             msg += '</br>账号长度应为11位';
//         }
//     }
//     $.post(
//         "/index.php/User/checkuser",
//         {
//             phone:account
//         },
//         function (data,state){
//             if(state != "success"){
//                 message("网络请求失败");
//                 return false;
//             }
//             if(data.status != 1){
//                 message("用户不存在,请先注册!");
//                 return false;
//             }else{
//                 //请求发送短信
//                 $.post(
//                     "/index.php/User/sendsmscode",
//                     {
//                         phone:account,
//                         type:"backpwd"
//                     },
//                     function (data,state){
//                         if(state != "success"){
//                             message("网络请求失败,请重试!");
//                             return false;
//                         }else if(data.status == 1){
//                             gainTime();
//                         }else{
//                             message(data.msg);
//                             return false;
//                         }
//                     }
//                 );
//             }
//         }
//     );

//   });

    // 忘记密码操作
    $("#forget-button").unbind("click").on("click",function(){
        account = $("#account").val();
        code = $("#code").val();
        password = $("#password").val();
        dpassword = $("#dpassword").val();

        mesg_default();

        if (account.length == 0 || code.length == 0 || password.length == 0 || dpassword == 0) {
            msg = '请完整信息';
            message(msg);

            return;
        }

        if (account.length != 11) {
            if (msg == '') {
                msg = '账号长度应为11位';
            } else {
                msg += '</br>账号长度应为11位';
            }
        }
        if(code.length != 6){ 
            if (msg == '') {
                msg = '验证码长度应为6位';
            } else {
                msg += '</br>验证码长度应为6位';
            }
        }
        if(password.length > 15 || password.length <6){ 
            if (msg == '') {
                msg = '密码长度应为6-15位';
            } else {
                msg += '</br>密码长度应为6-15位';
            }
        }
        if (dpassword != password) {
            if (msg == '') {
                msg = '两次密码输入不同';
            } else {
                msg += '</br>两次密码输入不同';
            }
        }

        if (msg != '') {
            message(msg);
            return;
        }

       	//请求修改密码
		$.post(
            "/index.php/User/backpwd",
			{
				phone:account,
				code:code,
				password:password
			},
			function (data,state){
				if(state != "success"){
					message("网络请求失败,请重试");
					return false;
				}else if(data.status == 1){
					message("密码修改成功,请登录!");
					setTimeout(function(){
						window.location.href = 'index.php/User/login';
					},2000);
				}else{
					message(data.msg);
					return false;
				}
			}
		);
    });


    

})
