<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title> <Somnus:sitecfg name="sitetitle"/>  - 站长源码库（zzmaku.com） </title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">
<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/pay-2b02ca7987.css">
<style>
	.container .inpifo {
	    border-bottom: none;
	}
	.btop{
		border-top: 1px solid #e4e4e4;
	}
	.input-group .form-control {		 
	    line-height: 14px;
	}
	input[type=password]::-ms-input-placeholder,input[type=text]::-ms-input-placeholder{
		padding-top: 4px;
	}
	input[type=password]::-webkit-input-placeholder,input[type=text]::-webkit-input-placeholder{
		padding-top: 4px;

	}
	.container .input-group input {
	    float: right;
	    padding-right: 5px;
	}
</style>
</head>
<script>
document.addEventListener('plusready',function(){
var webview = plus.webview.currentWebview();
plus.key.addEventListener('backbutton', function() {
webview.canBack(function(e) {
        if (e.canBack) {
                webview.back();
        } else {
            webview.close();//hide,quit
        }
    })
});

});
</script>
   	<!-- header -->
 	<header class="mui-bar mui-bar-nav hnav">
		<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left" href="{:U('Info/index')}"></a>
		<h1 class="mui-title">法律责任</h1>
	</header>
   <!-- header end-->
<body class="bg">
  	
<div style="width:85%;margin:0 auto">
<br /><br /><br />
 <h3> &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &nbsp;法 &nbsp;&nbsp;律&nbsp;&nbsp;责&nbsp;&nbsp;任</h3>
    <hr width=70% size=3 color=bule alingn=center  />
      <br />
 <p> 为明确本产品用户（以下简称“用户”或“您”）与以下简称“本公司”或“我们”或“平台”）之间的权利义务关系，维护双方的合法权益，本着平等互利的原则，双方就本产品服务之相关事宜达成以下协议（下称“本协议”），以资共同遵守。本协议适用于用户注册、使用本公司产品、服务的全部活动，为避免误解，用户成功注册本产品即视为本产品用户，受本协议约束。</p>
<br />
  <p>在注册成为本产品用户前，请用户务必认真、仔细阅读，并对本协议全部内容作充分理解。用户成功注册或使用本产品，即视为用户已经充分理解和同意本协议全部内容，本协议立即在用户与本公司之间产生法律效力，用户注册使用本产品服务的全部活动将受到本协议的约束并承担相应的责任和义务。如用户不同意本协议内容，请不要注册或使用本产品。</p>
  <br />
  <p>用户须保证在注册或使用本产品时，已经年满18周岁且具备完全民事行为能力。如用户不具备前述条件，用户应终止注册或停止使用本产品。用户若通过本人注册的账户为其他不具备前述条件的任何第三方借款，本公司有权拒绝提供服务，已提供服务的，本公司有权终止并保留追究责任的权利，因此产生的任何法律责任由用户自行承担。</p>
  <br />
  <p>本协议包括以下所有条款，同时也包括本产品已经发布的或者将来可能发布的各类规则（平台规则）。所有规则均为本协议不可分割的一部分，与本协议具有同等法律效力。</p>
  <br />
 <p> 用户在此确认知悉并同意本公司有权根据需要不时修改、增加或删减本协议。本公司将采用在本产品公示的方式通知用户该等修改、增加或删减，用户有义务注意该等公示。一经本产品公示，视为已经通知到用户。用户同意并确认，本公司可能以页面消息、微信、短消息等方式向用户发送将来可能发布的各类规则，该等规则构成本协议的一部分。若用户在本协议及各类规则变更后继续使用本产品服务的，视为用户已仔细认真阅读、充分理解并同意接受修改、增加或删减后的本协议及各类规则，且用户承诺遵守修改、增加或删减后的本协议及各类规则内容，并承担相应的责任和义务。若用户不同意修改、增加或删减后的本协议或各类规则内容，应立即停止使用本产品服务，本公司保留中止、终止或限制用户继续使用本产品服务的权利，但该等终止、中止或限制行为并不豁免用户在本产品已经发生的行为下所应承担的责任和义务。本公司不承担任何因此导致的法律责任。</p>
  <br />
 <p> 一、账户管理</p>
  
 <p> 1.1. 用户注册本产品时请按照本公司要求准确提供个人信息，并在取得注册账户（下称“该账户”）后及时更新用户准确、最新、完整的身份信息及相关资料，包括不限于手机号码、身份证号码、亲属联系人及社会联系人姓名、职业、银行账户等信息，以便本公司与用户进行及时、有效联系。</p>
  
 <p> 1.2. 用户应当使用自身合法的身份信息进行注册，若用户冒用、盗用、拾得他人身份证件办理本公司提供的产品/服务的，用户对此承担所有法律责任；本公司仅对用户的身份信息承担形式审查责任，且仅在自身业务职责范围内承担法律责任。</p>
  
 <p> 1.3. 该账户仅供用户本人使用，用户对使用该账户或密码进行的一切操作及言论负完全的责任。用户须对该账户、密码、身份信息等进行妥善保管，对于因密码、身份信息、校验码等泄露所致的损失由用户自行承担。如用户存在遗失手机或身份证件或银行卡以及其他可能危及本产品账户资金安全或发现有他人冒用或盗用用户的账户登录名及密码或任何其他未经合法授权的情形，应立即以有效方式通知本公司，向本公司申请暂停相关服务。除非另有法律规定或经司法裁判，且征得本公司同意，否则用户不得以任何方式转让、赠与或继承（相关的财产权益除外）其账号及密码等个人信息。</p>
  
 <p> 1.4. 用户不得通过本人注册的账户为任何第三方借款，用户充分知悉并承诺，不得以本人的账户出租、出借给他人，且用户充分知悉：若用户以本人账户出租、出借给他人使用，用户仍应承担《借款协议》项下的还款及其他义务。</p>
  
  <p>1.5. 若用户有上述违反本协议约定情形的，产生的任何法律责任均由用户承担，本公司对此不承担任何法律责任</p>
  
 <p> 1.6. 在需要终止使用本产品时，用户可以申请注销本产品账户，用户应当依照本公司规定的程序进行账户注销。本产品账户注销将导致本公司终止为用户提供本产品及相关服务，本协议约定的双方的权利义务终止，但依本协议其他条款另行约定不得终止的或依其性质不能终止的除外。</p>
  
 <p> 1.7. 存在以下情形的，本公司有权拒绝用户注销账户的申请并应将拒绝理由告知用户：</p>
  
 <p> 1.该账户尚存在未了结的权利义务关系；</p>
  
 <p> 2.注销该账户会损害本公司、本公司用户或他人的合法权益；</p>
  
 <p> 3.本公司认为不适宜注销该账户的其他情形。</p>
  <br />
  <p>二、服务内容</p>
  
 <p> 本产品是由本公司投资并运营的提供自然人间借款信息及撮合服务平台。本公司为用户提供信息发布、信用咨询、合同管理、资金代管、还款管理，以及促成用户与第三方出借人达成交易的居间服务。</p>
  
  <p>2.1. 信用评估：信用评估服务是指本公司为用户提供的通过读取和分析用户的个人公开信息、用户授权本公司使用的个人隐私信息及其他授权信息来评估用户信用状况的服务。为使本公司顺利分析与用户信用信息相关的个人信息，用户在此不可撤销地授权本公司采集、读取、分析、使用及处理用户的以下信息：</p>
  
 <p> 1、甲方的身份信息；</p>
  
  <p>2、甲方的手机账单、清单、实名制等信息；</p>
  
 <p> 3、甲方的银行卡信息；</p>
  
 <p> 4、其他有助于乙方授予甲方信用额度的信息。</p>
  
  <p>2.2. 信息发布：用户注册成为本产品用户后，可以按照平台规则委托本公司将其借款需求信息通过本公司公开发布，即发出借款要约。</p>
  
 <p> 2.3. 借款申请审批：用户应当按照本公司要求的程序进行申请，包括但不限于银行储蓄卡绑定、持证自拍、本公司工作人员或本公司指定的机构及人员与用户通过微信、电话进行核实等。用户完成上述申请程序后，本公司将对用户的申请进行审批。</p>
  
 <p> 2.4. 代付：在订立借款合同后，本公司接受第三方出借人委托，将用户借款款项存入用户指定的账户内。</p>
  
 <p> 2.5. 代扣：在订立借款合同后，用户委托本公司及本公司授权/聘请的具备相关业务资质的第三方从用户银行账户上代为扣取应还/应付款项，并用于向第三方出借人支付还款。</p>
  
 <p> 2.6. 查询：本公司将对用户在本产品中的所有操作进行记录，不论该操作之目的最终是否实现。用户可以在本产品中查询其注册用户名下的个人信息及借贷交易记录。</p>
  
 <p> 2.7. 交易：用户在平台申请借款或其他服务时，需遵从用户与本公司及第三方出借人达成的借款协议及其他任何协议。</p>
  
 <p> 2.8. 广告：提供服务的过程中，本公司可以自行或由第三方广告商向用户发送广告、推广或宣传信息（包括商业与非商业信息），其方式和范围可不经向用户特别通知而变更。对服务中出现的广告信息，用户应审慎判断其真实性和可靠性，除法律明确规定外，用户应对依该广告信息进行的交易负责。</p>
  <br />
 <p> 三、信息授权</p>
  
 <p> 为顺利对用户信用进行评估，以审核用户借款申请，用户授权本公司从用户的手机通讯运营商（包括但不限于中国移动、中国电信、中国联通等）及其他第三方获取用户的相关个人信息，具体如下：</p>
  
 <p> 3.1. 用户须在申请借款的过程中根据平台提示输入个人手机通讯运营商的服务密码、验证码等信息，且授权本公司或本公司聘用的其他第三方机构使用上述服务密码、验证码等信息获取用户的手机消费账单、清单、实名制等。用户知悉并同意本公司或本公司聘用的其他第三方机构使用用户授权的手机号码、服务密码、验证码等信息获取用户的相关信息是评估审核用户的借款申请所必要的条件。</p>
  
 <p> 3.2. 在本产品使用中，如用户输入学历信息、学信网账户名及密码等，即表示同意为本产品之目的，向本公司或本公司聘用的其他第三方机构授权使用用户的学信网账户，本公司或本公司聘用的其他第三方机构将可能通过用户所授权的学信网账户查看并读取用户的学籍信息；如用户在授权时尚未注册学信网账户，本公司及本公司聘用的其他第三方机构将基于用户的授权代用户申请注册学信网账户。</p>
  
 <p> 3.3. 在本产品使用中，如用户同意向本公司提交、绑定或授权用户的银行卡信息／账户，本公司将可能：</p>
  
 <p> 1）查询并核对用户的账户信息。</p>
 <p> 2）查询并读取用户银行卡账户中的交易信息。</p>
 <p> 3）基于《借款协议》通过用户所授权或绑定的银行卡账户进行代收与代付服务。</p>
  
 <p> 3.4. 本公司有权依据《征信业管理条例》及相关法律法规，向第三方支付/征信/金融机构合法了解、获取、核实用户的信用信息，所获取的个人信用信息仅在本产品中使用。</p><br /><br />
</div>
</body>
</html>