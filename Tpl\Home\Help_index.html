<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>在线客服 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/cservice.css">

</head>

<body id="kf">

	<div class="comm_top_nav" data-am-sticky>
		<div class="am-g">
			<b>
				<div class="am-u-sm-2"> &nbsp;</div>
				<div class="am-u-sm-8">在线客服咨询</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>

	</div>

	<div class="kf_menu">
		<div class="am-g">
		 	
		 	<!--
		 	<div class="am-u-sm-6">
				<div class="kf_img">
					<img src="__PUBLIC__/home/<USER>/picture/kf_qq.png" alt="">
				</div>
				<div class="kf_text">
					<div class="kf_text_t">
						QQ咨询
					</div>
					<span class="f_number kf_text_n">
						{:C('cfg_kefu_qq')} </span>
				</div>
			</div>
			-->
			
		<center>	
		<div class="am-u-sm-6">
		</center>
		
		<center>
		<div class="kf_img">
      <a href="{:C('cfg_kefu_link')}"><img src="__PUBLIC__/home/<USER>/picture/kf_zxkf.png"  alt=""></a>
		</center>
		</div>
		
				<div class="jobtime">
					<div class="kf_text_t">
						<button  onclick="window.location.href='{:C('cfg_kefu_link')}'" class="am-btn">联系客服</button>
					</div>
					<!--
					<span class="f_number kf_text_n">{:C('cfg_kefu_link')} </span>
					-->
				</div>
			</div>
			
			
			
			
			
			
			
			
			
		<!--
			<div class="am-u-sm-6">
				<div class="kf_img">
					<img src="__PUBLIC__/home/<USER>/picture/kf_wechat.png" alt="">
				</div>
				<div class="kf_text">
					<div class="kf_text_t">
						微信咨询
					</div>
					<span class="f_number kf_text_n">
						{:C('cfg_kefu_wx')} </span>
				</div>
			</div>
			
			-->
			
			
		</div>
	</div>

	<div class="jobtime">在线客服上班时间为【9:00】至【21:00】</div>


	<div class="jobtime">
	<!--	<button  onclick="window.location.href='{:C('cfg_kefu_link')}'" class="am-btn">在线客服咨询</button> -->

	</div>

	<div class="message">

	</div>
	<!-- 底部导航条 -->
	<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">
		<ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">
			<li class="nva_sy">
				<a href="/" class="">
					<img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="消息">

					<span class="am-navbar-label">首页</span>
				</a>
			</li>
			<li class="nva_qb">
					<a href="{:U('Qianbao/index')}" class="">
					<img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="消息">

					<span class="am-navbar-label">钱包</span>
				</a>
			</li>
			<li class="nva_kf">
					<a href="{:U('Help/index')}" class="">
					<img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="消息">
					<span class="am-navbar-label">客服</span>
				</a>
			</li>
			<li class="nva_wd">
					<a href="{:U('User/index')}" class="">
					<img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="消息">

					<span class="am-navbar-label">我的</span>
				</a>
			</li>




		</ul>
	</div>


	<div id="kefu"></div>
	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script>
		var upload_type,
			width = $(window).width(),
			timer,
			msg
			;

		$("#kf #bm-nav .nva_kf a img").attr('src', '__PUBLIC__/home/<USER>/picture/1-2.png');


		$(".head_height").height($(".head_height").width());

		$(window).resize(function () {
			$(".head_height").height($(".head_height").width());
		});
		// 弹窗

		// 倒计时
		function myTimer() {
			var sec = 3;
			clearInterval(timer);
			timer = setInterval(function () {
				console.log(sec--);
				if (sec == 1) {
					$(".message").addClass("m-hide");
					$(".message").removeClass("m-show");
				}
				if (sec == 0) {
					$(".message").hide();
					$(".message").removeClass("m-hide");
					clearInterval(timer);
				}
			}, 1000);
		}

		// 弹窗内容
		function message(data) {
			msg = $(".message p").html(data);
			$(".message").addClass("m-show");
			$(".message").show();

			myTimer();

		}

		// 初始化弹窗
		function mesg_default() {
			msg = '';
			$(".message").hide();
			$(".message").removeClass("m-show");
			$(".message").removeClass("m-hide");
		}


		//图像上传
		$('.head_input').change(function () {
			// upload_type = $(this).data("type");
			var file = this.files[0];
			var iname = $(this).val();
			//后台传值需要
			var size = file.size / 1024;
			//获取文件大小 用来判断是否超过多少kb
			var URL = window.URL || window.webkitURL;
			var blob = URL.createObjectURL(file);
			var image = new Image();
			image.src = blob;
			//console.log(blob);
			image.onload = function () {
				getUrlBase64(blob, size);
			};
			//将图片转为base64
			function getUrlBase64(url, size) {
				var canvas = document.createElement("canvas");   //创建canvas DOM元素
				var ctx = canvas.getContext("2d");
				var img = new Image;
				img.crossOrigin = 'Anonymous';
				img.src = url;
				img.onload = function () {
					var w = this.width, h = this.height, scale = w / h;
					w = w > 600 ? 600 : w;
					h = w / scale;
					canvas.height = h; //指定画板的高度,自定义
					canvas.width = w; //指定画板的宽度，自定义
					ctx.drawImage(img, 0, 0, w, h); //参数可自定义
					if (size > 200) {
						//判断 如果图片大图200kb就压缩 否则就不压缩
						var dataURL = canvas.toDataURL("image/jpeg", 0.9);
						//压缩主要代码 第二个参数表示压缩比例，指为1.0时表示不压缩
					} else {
						var dataURL = canvas.toDataURL("image/jpeg");
					}
					//显示预览
					// var img_div = $('#'+upload_type).parent(".upload_box");

					// img_div.css({"background":"url('"+dataURL+"')","background-repeat": "no-repeat","background-size": "auto 100%","background-position":" center center"});




					var oFormData = new FormData();
					// FormData()方法向后台传值
					oFormData.append("uId", upload_type);

					oFormData.append('base64', dataURL);

					$.ajax({
						type: 'post',
						url: 'uphead',
						data: oFormData,
						cache: false,  // 不缓存
						contentType: false, // jQuery不要去处理发送的数据
						processData: false, // jQuery不要去设置Content-Type请求头
						success: function (data) {
							console.log(data.image_url);
							// var obj = JSON.parse(data);
							// $('#sfz'+upload_type).val(data.image_url);

							$('.head').attr('src', data.image_url);

							mesg_default();

							msg = data.mesg;

							message(msg);



						},
						error: function (err) {
							console.log(err);
						}
					});

					canvas = null;
				};
			};
		});
	</script>
	
	  <div style="display: none;">
    <Somnus:sitecfg name="sitecode" />
  </div>
	
	
	
</body>

</html>