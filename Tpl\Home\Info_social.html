<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">


	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">





	<title>社交认证 - 站长源码库（zzmaku.com） </title>

	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myinfo.css">


</head>

<body>

	<div class="comm_top_nav" data-am-sticky="">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace(document.referrer);"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">社交认证</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>

	</div>

	<div class="phone">
		<form action="" enctype="multipart/form-data">

			<div class="input_text_group">
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">QQ号</div>
						<div class="am-u-sm-8 f_number">
							<input type="number" id="qq" value="{$userinfo.qq}" placeholder="请输入">
						</div>
					</div>
				</div>
				<div class="input_text_list">
					<div class="am-g">
						<div class="am-u-sm-4">微信号</div>
						<div class="am-u-sm-8 f_number">
							<input type="text" id="wechat" value="{$userinfo.wx}" placeholder="请输入">
						</div>
					</div>
				</div>
			</div>


			<div style="height: 70px;"></div>

			<div class="fix_bottom">
				<div class="am-g">
					<button type="button" class="am-btn am-btn-block" id="social-button" onclick="saveInfo();">
						确认提交
					</button>
				</div>
			</div>
		</form>
	</div>


	<div class="message">
		<p></p>
	</div>



	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<script type="text/javascript">
	 // 弹窗

    // 倒计时
    function myTimer(){
		var sec = 3;
		var timer;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }	
	function saveInfo(){
	var qq = $("#qq").val();
	var wechat = $("#wechat").val();
	mesg_default();
	if(qq != '' && qq != null && wechat != '' && wechat != null){
		$.post(
			"{:U('Info/social')}",
			{
				qq:qq,
				wx:wechat
			},
			function (data,state){
				if(state != "success"){
					message("请求数据失败,请重试!");
				}else if(data.status == 1){
					message("保存成功!");
					window.location.href = "{:U('Info/index')}";
				}else{
					message(data.msg);
				}
			}
		);
	}else{
		message("社交认证填写不完整!");
	}
}
	
	
	</script>


</body>

</html>