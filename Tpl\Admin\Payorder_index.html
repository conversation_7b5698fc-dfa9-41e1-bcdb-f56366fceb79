
<div class="filter">

    <form action="{:U(GROUP_NAME.'/Payorder/index')}" method="post">

        <input name="keyword" type="text" class="inpMain" placeholder="用户名" value="{$keyword}" size="20" />
 solid
          <input name="sday" id="sday" type="text" placeholder="开始时间" class="inpMain  day1" value="{$sday}" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="16" />



        <input name="eday" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="eday" type="text" placeholder="结束时间" class="inpMain  day1" value="{$eday}" size="16" />

        <input name="submit" class="btnGray" type="submit" value="筛选" />

    </form>

    <div style="font-size:18px;color:#000;border:1px dashed #d9d9d9; background-color:#fff">订单总计：{$sumcont}笔&nbsp;/&nbsp;金额总计：{$sumamount}元</div>

</div>

<div id="list">

        <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">

            <tr>

                <th width="50" align="center">ID</th>

                <th width="150" align="center">订单号</th>

                <th width="100" align="center">用户名</th>

                <th width="80">支付类型</th>

                <th width="80">金额</th>

                <th width="150" align="center">创建日期</th>

                <th width="80">状态</th>

                <th align="center">操作</th>

            </tr>

            <volist name="list" id="vo">

                <tr>

                    <td align="center">{$vo.id}</td>

                    <td align="center">{$vo.ordernum}</td>

                    <td align="center">{$vo.user}</td>
<!--
					<td align="center">{$vo.type}</td>
	-->				
						<td align="center">VIP费用</td>

					<td align="center">{$vo.money}</td>

                    <td align="center">{$vo.addtime|date='Y-m-d',###}</td>

                    <td align="center">

                    	<php>

                    		if($vo['status']){

                    			echo "已支付";

                    		}else{

                    			echo "自行查收";

                    		}

                    	</php>

                    </td>

                    <td align="center">

                        <a href="javascript:del('{$vo.ordernum}','{:U(GROUP_NAME.'/Payorder/del',array('id'=>$vo['id']))}');">删除订单</a>

                    </td>

                </tr>

            </volist>

        </table>

</div>

<div class="clear"></div>

<div class="pager">

    {$page}

</div>

</div>

<script>

    function del(num,jumpurl){

        layer.confirm(

                '确定要删除订单:'+num+'吗?',

                function (){

                    window.location.href = jumpurl;

                }

        );

    }

</script>