var timer, msg,
    rll, loan_min, loan_max, month_default, jkje_default,
    mqhk, jkje, jksj , Discountmonth
    ;
// 弹窗

//优惠券月份
// if(!Discountmonth){
//     console.log('sssssss')
// }



// 倒计时
function myTimer() {
    var sec = 3;
    clearInterval(timer);
    timer = setInterval(function () {
        console.log(sec--);
        if (sec == 1) {
            $(".message").addClass("m-hide");
            $(".message").removeClass("m-show");
        }
        if (sec == 0) {
            $(".message").hide();
            $(".message").removeClass("m-hide");
            clearInterval(timer);
        }
    }, 1000);
}

// 弹窗内容
function message(data) {
    msg = $(".message p").html(data);
    $(".message").addClass("m-show");
    $(".message").show();

    myTimer();

}

// 初始化弹窗
function mesg_default() {
    msg = '';
    $(".message").hide();
    $(".message").removeClass("m-show");
    $(".message").removeClass("m-hide");
}
var cop = $('#cop');
// $.post('cop_post',
//     {

//     },
//     function (data,status) {
//         var obj = JSON.parse(data);
//         if (obj.code == 0) {
//             $(".cop_number").html(obj.coupon);
//             var date = new Date(obj.overtime*1000);
//             var Y = date.getFullYear() + '年';
//             var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '月';
//             var D = date.getDate() + '日';
//             $(".overtime").html(Y+M+D);

//             cop.modal({
//                 closeViaDimmer: 1,
//                 dimmer:1
//             });
//         }

//     }
// );
// $("#cop_btn").unbind('click').on('click', function () {
//     mesg_default();
//     $.post('{:U('User/index')}'),
//         {},
//         function (data, status) {
//             var obj = JSON.parse(data);
//             message(obj.mesg);

//             if (obj.code == 0) {
//                 cop.modal('close');
//             }
//         };
// });


// 身份信息显示
// $.ajax({
// 	type:'post',
// 	url:'show_loan_setting',
// 	success: function(data){
// rll = data[0].settingrule;


// loan_min = data[1].settingrule;
// loan_max = data[2].settingrule;
loan_min = $('#min').text();
loan_max = $('#max').text();
// month_default = data[3].settingrule;
// month_default = $('#jksj_group a span')[4].html();
month_default = $('.jk_time_add').data('time')
$('#jksj_group').on('click', function () {
    month_default = $('.jk_time_add').data('time')
    $feilv = $('#feilv').text();
    var feilvarr = $feilv.split(",");
    rll = (feilvarr[month_default - 1] / 30) / 100;
    $('#rixiss').html((feilvarr[month_default - 1] / 30).toFixed(3))
    $('#yuess').html((feilvarr[month_default - 1]))
})
jkje_default = $('#jkje').text();
$feilv = $('#feilv').text();
var feilvarr = $feilv.split(",");
rll = (feilvarr[month_default - 1] / 30) / 100;
// console.log(feilvarr);
// console.log(feilvarr[6])
// console.log(month_default);
// 初始化
$('#jksj').html(month_default);
jksj = $('#jksj').html();

//console.log('1'+jksj);

$.fn.RangeSlider = function (cfg) {
    this.sliderCfg = {
        min: cfg && !isNaN(parseFloat(cfg.min)) ? Number(cfg.min) : null,
        max: cfg && !isNaN(parseFloat(cfg.max)) ? Number(cfg.max) : null,
        step: cfg && Number(cfg.step) ? cfg.step : 1,
        callback: cfg && cfg.callback ? cfg.callback : null
    };
    var $input = $(this);
    var min = this.sliderCfg.min;
    var max = this.sliderCfg.max;
    var step = this.sliderCfg.step;
    var callback = this.sliderCfg.callback;
    var $value = jkje_default;

    mqhk = (parseFloat($value) / parseFloat(jksj) + parseFloat($value) * parseFloat(rll) * 30).toFixed(2);
    //console.log(parseFloat(rll));

    $input.attr('min', min)
        .attr('max', max)
        .attr('step', step);

    $input.val($value).css('background-size', ($value - min) * 100.0 / (max - min) + '% 100%');
    $("#amount").val($value);
    $("#jkje").html($value);
    $("#dzje").html($value);
    $('.p_jkje').html($value);
    $("#mqhk").html(Math.round(mqhk));
    $("#input_mqhk").val(mqhk);
    $('#rixiss').html((feilvarr[month_default - 1] / 30).toFixed(3))
    $('#yuess').html((feilvarr[month_default - 1]))
    


    $input.bind("input", function (e) {
        $input.attr('value', this.value);
        $input.css('background-size', (this.value - min) * 100.0 / (max - min) + '% 100%');
        //$input.css( 'background', 'linear-gradient(to right, #059CFA, white ' + this.value * 100.0 / max + '%, white)' );

        if ($.isFunction(callback)) {
            callback(this);
        }
    });
};

var change = function ($input) {
    /*拖动滑块的事件，内容可自行定义*/
    $("#amount").val($input.value);
    $("#jkje").html($input.value);
    $("#dzje").html($input.value);
    $('.p_jkje').html($input.value);
    //console.log('1'+jksj);
    mqhk = (parseFloat($input.value) / parseFloat(jksj) + parseFloat($input.value) * parseFloat(rll) * 30).toFixed(2);
    $("#mqhk").html(Math.round(mqhk));
    $("#input_mqhk").val(mqhk);
};

$('#slider').RangeSlider({ min: loan_min, max: loan_max, step: 10, callback: change });

// $('#amount').bind("input", function (e) {
//     var value = 0;
//     // 过滤下输入内容，因为个别特殊机型手机在input设置了只能输入数字后还是能输入符号
//     if (/^[1-9][0-9]*/.test(this.value)) {
//         value = this.value;
//     }
//     // 这里保证输入最大值为550，与滑块一致
//     if (this.value > 550) {
//         value = 550;
//         $("#amount").val(550);
//     }
//     // 这里设置滑块的值和css
//     $("#slider").val(value).css('background-size', value * 100.0 / 550 + '% 100%');
// });

$('#edit').on('click', function () {
    var value = 0;
    $(".input_jkje").val($("#jkje").html());
    $('#my-prompt').modal({
        relatedTarget: this,
        onConfirm: function (e) {
            var edata = parseInt(e.data);
            value = edata;
            console.log(value);
            if (edata > loan_max) {
                value = loan_max;
                $(".input_jkje").val(loan_max);
            }
            if (edata < loan_min || isNaN(edata)) {
                value = loan_min;
                $(".input_jkje").val(loan_min);
            }
            $("#slider").val(value).css('background-size', (value - loan_min) * 100.0 / (loan_max - loan_min) + '% 100%');

            $("#jkje").html(value);
            $("#dzje").html(value);
            mqhk = (parseFloat(value) / parseFloat(jksj) + parseFloat(value) * parseFloat(rll) * 30).toFixed(2);
            $("#mqhk").html(Math.round(mqhk));
            $("#input_mqhk").val(mqhk);
        },
        onCancel: function (e) {

        }
    });
});

$('.input_jkje').on('keyup', function () {
    var o = $(this).val();
    var temp_amount = '';
    if (/[^\d]/.test(o)) { //替换非数字字符
        var temp_amount = o.replace(/[^\d]/g, '');
    } else if (/^[0]*/g.test(o)) {
        var temp_amount = RegExp.rightContext;
    }
    $(this).val(temp_amount);
});



// 借款时间
$('.jk_time').unbind('click').on('click', function () {

    jksj = $(this).data('time');
    $('#jksj').html(jksj);
    $('.p_jksj').html(jksj);
    mqhk = (parseFloat($('#jkje').html()) / parseFloat(jksj) + parseFloat($('#jkje').html()) * parseFloat(rll) * 30).toFixed(2);
    // console.log(parseFloat($('#jkje').html())+'/'+ parseFloat(jksj) +'+'+ parseFloat($('#jkje').html()) +'*'+ parseFloat(rll) +'*'+ 30 +' = '+mqhk)
    $("#mqhk").html(Math.round(mqhk));
    $("#input_mqhk").val(mqhk);

    $('.jk_time').removeClass('jk_time_add');
    $(this).addClass('jk_time_add');
});
$("#q-button").unbind('click').on('click',function () {
    $(".p_jkje").html($("#jkje").html());
    //优惠券免息
    if(Discountmonth == $("#jksj").html()){
        $(".p_mqhk").html($("#jkje").html());
    }else{
        $(".p_mqhk").html(Math.round($('#mqhk').html() * $("#jksj").html()));
    }
    
    $(".p_jksj").html($("#jksj").html());
    // $.post("u_info",
    //     {

    //     },
    //     function(data,status){

    //         // console.log(data);
    //         var obj = JSON.parse(data);
    //         var coupon,cop_text;
    //         if (parseInt($("#jksj").html()) < parseInt(obj.coupon) || parseInt(obj.coupon) == 0){
    //             coupon = '';
    //             cop_text = "暂无免息券";
    //         } else {
    //             coupon = "(&nbsp;-"+Math.round($('#jkje').html() * obj.coupon * rll * 30)+"元)";
    //             cop_text = "使用"+obj.coupon+"期免息券";
    //         }
    //         $(".cop_text").html(cop_text);
    //         $(".p_del_cop").html(coupon);
    //         $(".p_u_fullname").html(obj.fullname);
    //         $(".p_u_showbank").html(obj.showbank);
    //         $(".p_u_banknumber").html(obj.banknumber.slice(-4));

    //         // console.log(Math.round($("#mqhk").html() * $("#jksj").html()));
    //         // console.log(obj.coupon);
    //     }

    // );
});
$("#qm-button").unbind('click').on('click', function () {
    setTimeout(function () {
        window.location.href = '../Mine/myinfo_qm';
    }, 1500);
});
$("#index-button").unbind('click').on('click', function () {
    jkje = $("#jkje").html();
    jksj = $("#jksj").html();
    mesg_default();
    //提交获取支付订单号
    $.post(
        "/index.php/Order/daikuan/trueorder/1",
        {
            money: jkje,
            month:jksj
        },
        function (data,state){
            if(state != "success"){
               message('请求数据失败,请重试!');
            }else if(data.status != 1){
                message(data.msg);
            }else{
                window.location.href = data.payurl;
            }
        }
    );
});

$("#quota-button").unbind('click').on('click', function () {
    $.post('quota_check',
        {

        },
        function (data, state) {

        }

    );
});

