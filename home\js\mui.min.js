var mui=function(t,e){var i=/complete|loaded|interactive/,s=/^#([\w-]+)$/,n=/^\.([\w-]+)$/,o=/^[\w-]+$/,r=/translate(?:3d)?\((.+?)\)/,a=/matrix(3d)?\((.+?)\)/,l=function(e,i){if(i=i||t,!e)return c();if("object"==typeof e)return l.isArrayLike(e)?c(l.slice.call(e),null):c([e],null);if("function"==typeof e)return l.ready(e);if("string"==typeof e)try{if(e=e.trim(),s.test(e)){var n=t.getElementById(RegExp.$1);return c(n?[n]:[])}return c(l.qsa(e,i),e)}catch(o){}return c()},c=function(t,e){return t=t||[],Object.setPrototypeOf(t,l.fn),t.selector=e||"",t};l.uuid=0,l.data={},l.extend=function(){var t,i,s,n,o,r,a=arguments[0]||{},c=1,h=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[c]||{},c++),"object"==typeof a||l.isFunction(a)||(a={}),c===h&&(a=this,c--);h>c;c++)if(null!=(t=arguments[c]))for(i in t)s=a[i],n=t[i],a!==n&&(u&&n&&(l.isPlainObject(n)||(o=l.isArray(n)))?(o?(o=!1,r=s&&l.isArray(s)?s:[]):r=s&&l.isPlainObject(s)?s:{},a[i]=l.extend(u,r,n)):n!==e&&(a[i]=n));return a},l.noop=function(){},l.slice=[].slice,l.filter=[].filter,l.type=function(t){return null==t?String(t):h[{}.toString.call(t)]||"object"},l.isArray=Array.isArray||function(t){return t instanceof Array},l.isArrayLike=function(t){var e=!!t&&"length"in t&&t.length,i=l.type(t);return"function"!==i&&!l.isWindow(t)&&("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t)},l.isWindow=function(t){return null!=t&&t===t.window},l.isObject=function(t){return"object"===l.type(t)},l.isPlainObject=function(t){return l.isObject(t)&&!l.isWindow(t)&&Object.getPrototypeOf(t)===Object.prototype},l.isEmptyObject=function(t){for(var i in t)if(i!==e)return!1;return!0},l.isFunction=function(t){return"function"===l.type(t)},l.qsa=function(e,i){return i=i||t,l.slice.call(n.test(e)?i.getElementsByClassName(RegExp.$1):o.test(e)?i.getElementsByTagName(e):i.querySelectorAll(e))},l.ready=function(e){return i.test(t.readyState)?e(l):t.addEventListener("DOMContentLoaded",function(){e(l)},!1),this},l.buffer=function(t,e,i){function s(){n&&(n.cancel(),n=0),o=l.now(),t.apply(i||this,arguments),r=l.now()}var n,o=0,r=0,e=e||150;return l.extend(function(){!o||r>=o&&l.now()-r>e||o>r&&l.now()-o>8*e?s():(n&&n.cancel(),n=l.later(s,e,null,arguments))},{stop:function(){n&&(n.cancel(),n=0)}})},l.each=function(t,e,i){if(!t)return this;if("number"==typeof t.length)[].every.call(t,function(t,i){return e.call(t,i,t)!==!1});else for(var s in t)if(i){if(t.hasOwnProperty(s)&&e.call(t[s],s,t[s])===!1)return t}else if(e.call(t[s],s,t[s])===!1)return t;return this},l.focus=function(t){l.os.ios?setTimeout(function(){t.focus()},10):t.focus()},l.trigger=function(t,e,i){return t.dispatchEvent(new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0})),this},l.getStyles=function(t,e){var i=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?i.getPropertyValue(e)||i[e]:i},l.parseTranslate=function(t,e){var i=t.match(r||"");return i&&i[1]||(i=["","0,0,0"]),i=i[1].split(","),i={x:parseFloat(i[0]),y:parseFloat(i[1]),z:parseFloat(i[2])},e&&i.hasOwnProperty(e)?i[e]:i},l.parseTranslateMatrix=function(t,e){var i=t.match(a),s=i&&i[1];i?(i=i[2].split(","),"3d"===s?i=i.slice(12,15):(i.push(0),i=i.slice(4,7))):i=[0,0,0];var n={x:parseFloat(i[0]),y:parseFloat(i[1]),z:parseFloat(i[2])};return e&&n.hasOwnProperty(e)?n[e]:n},l.hooks={},l.addAction=function(t,e){var i=l.hooks[t];return i||(i=[]),e.index=e.index||1e3,i.push(e),i.sort(function(t,e){return t.index-e.index}),l.hooks[t]=i,l.hooks[t]},l.doAction=function(t,e){l.isFunction(e)?l.each(l.hooks[t],e):l.each(l.hooks[t],function(t,e){return!e.handle()})},l.later=function(t,e,i,s){e=e||0;var n,o,r=t,a=s;return"string"==typeof t&&(r=i[t]),n=function(){r.apply(i,l.isArray(a)?a:[a])},o=setTimeout(n,e),{id:o,cancel:function(){clearTimeout(o)}}},l.now=Date.now||function(){return+new Date};var h={};return l.each(["Boolean","Number","String","Function","Array","Date","RegExp","Object","Error"],function(t,e){h["[object "+e+"]"]=e.toLowerCase()}),window.JSON&&(l.parseJSON=JSON.parse),l.fn={each:function(t){return[].every.call(this,function(e,i){return t.call(e,i,e)!==!1}),this}},"function"==typeof define&&define.amd&&define("mui",[],function(){return l}),l}(document);!function(t,e){function i(i){this.os={};var s=[function(){var t=i.match(/(MicroMessenger)\/([\d\.]+)/i);return t&&(this.os.wechat={version:t[2].replace(/_/g,".")}),!1},function(){var t=i.match(/(Android);?[\s\/]+([\d.]+)?/);return t&&(this.os.android=!0,this.os.version=t[2],this.os.isBadAndroid=!/Chrome\/\d/.test(e.navigator.appVersion)),this.os.android===!0},function(){var t=i.match(/(iPhone\sOS)\s([\d_]+)/);if(t)this.os.ios=this.os.iphone=!0,this.os.version=t[2].replace(/_/g,".");else{var e=i.match(/(iPad).*OS\s([\d_]+)/);e&&(this.os.ios=this.os.ipad=!0,this.os.version=e[2].replace(/_/g,"."))}return this.os.ios===!0}];[].every.call(s,function(e){return!e.call(t)})}i.call(t,navigator.userAgent)}(mui,window),function(t,e){function i(i){this.os=this.os||{};var s=i.match(/Html5Plus/i);s&&(this.os.plus=!0,t(function(){e.body.classList.add("mui-plus")}),i.match(/StreamApp/i)&&(this.os.stream=!0,t(function(){e.body.classList.add("mui-plus-stream")})))}i.call(t,navigator.userAgent)}(mui,document),function(t){"ontouchstart"in window?(t.isTouchable=!0,t.EVENT_START="touchstart",t.EVENT_MOVE="touchmove",t.EVENT_END="touchend"):(t.isTouchable=!1,t.EVENT_START="mousedown",t.EVENT_MOVE="mousemove",t.EVENT_END="mouseup"),t.EVENT_CANCEL="touchcancel",t.EVENT_CLICK="click";var e=1,i={},s={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"},n=function(){return!0},o=function(){return!1},r=function(e,i){return e.detail?e.detail.currentTarget=i:e.detail={currentTarget:i},t.each(s,function(t,i){var s=e[t];e[t]=function(){return this[i]=n,s&&s.apply(e,arguments)},e[i]=o},!0),e},a=function(t){return t&&(t._mid||(t._mid=e++))},l={},c=function(e,s,n,o){return function(n){for(var o=i[e._mid][s],a=[],l=n.target,c={};l&&l!==document&&l!==e&&(!~["click","tap","doubletap","longtap","hold"].indexOf(s)||!l.disabled&&!l.classList.contains("mui-disabled"));l=l.parentNode){var h={};t.each(o,function(i,s){c[i]||(c[i]=t.qsa(i,e)),c[i]&&~c[i].indexOf(l)&&(h[i]||(h[i]=s))},!0),t.isEmptyObject(h)||a.push({element:l,handlers:h})}c=null,n=r(n),t.each(a,function(e,i){l=i.element;var o=l.tagName;return"tap"===s&&"INPUT"!==o&&"TEXTAREA"!==o&&"SELECT"!==o&&(n.preventDefault(),n.detail&&n.detail.gesture&&n.detail.gesture.preventDefault()),t.each(i.handlers,function(e,i){t.each(i,function(t,e){e.call(l,n)===!1&&(n.preventDefault(),n.stopPropagation())},!0)},!0),!n.isPropagationStopped()&&void 0},!0)}},h=function(t,e){var i=l[a(t)],s=[];if(i){if(s=[],e){var n=function(t){return t.type===e};return i.filter(n)}s=i}return s},u=/^(INPUT|TEXTAREA|BUTTON|SELECT)$/;t.fn.on=function(e,s,n){return this.each(function(){var o=this;a(o),a(n);var r=!1,h=i[o._mid]||(i[o._mid]={}),d=h[e]||(h[e]={});t.isEmptyObject(d)&&(r=!0);var p=d[s]||(d[s]=[]);if(p.push(n),r){var f=l[a(o)];f||(f=[]);var g=c(o,e,s,n);f.push(g),g.i=f.length-1,g.type=e,l[a(o)]=f,o.addEventListener(e,g),"tap"===e&&o.addEventListener("click",function(t){if(t.target){var e=t.target.tagName;if(!u.test(e))if("A"===e){var i=t.target.href;i&&~i.indexOf("tel:")||t.preventDefault()}else t.preventDefault()}})}})},t.fn.off=function(e,s,n){return this.each(function(){var o=a(this);if(e)if(s)if(n){var r=i[o]&&i[o][e]&&i[o][e][s];t.each(r,function(t,e){return a(e)===a(n)?(r.splice(t,1),!1):void 0},!0)}else i[o]&&i[o][e]&&delete i[o][e][s];else i[o]&&delete i[o][e];else i[o]&&delete i[o];i[o]?(!i[o][e]||t.isEmptyObject(i[o][e]))&&h(this,e).forEach(function(t){this.removeEventListener(t.type,t),delete l[o][t.i]}.bind(this)):h(this).forEach(function(t){this.removeEventListener(t.type,t),delete l[o][t.i]}.bind(this))})}}(mui),function(t,e,i){t.targets={},t.targetHandles=[],t.registerTarget=function(e){return e.index=e.index||1e3,t.targetHandles.push(e),t.targetHandles.sort(function(t,e){return t.index-e.index}),t.targetHandles},e.addEventListener(t.EVENT_START,function(e){for(var s=e.target,n={};s&&s!==i;s=s.parentNode){var o=!1;if(t.each(t.targetHandles,function(i,r){var a=r.name;o||n[a]||!r.hasOwnProperty("handle")?n[a]||r.isReset!==!1&&(t.targets[a]=!1):(t.targets[a]=r.handle(e,s),t.targets[a]&&(n[a]=!0,r.isContinue!==!0&&(o=!0)))}),o)break}}),e.addEventListener("click",function(e){for(var s=e.target,n=!1;s&&s!==i&&("A"!==s.tagName||(t.each(t.targetHandles,function(t,i){return i.name,i.hasOwnProperty("handle")&&i.handle(e,s)?(n=!0,e.preventDefault(),!1):void 0}),!n));s=s.parentNode);})}(mui,window,document),function(t){String.prototype.trim===t&&(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),Object.setPrototypeOf=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t}}(),function(){function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var i=document.createEvent("Events"),s=!0;for(var n in e)"bubbles"===n?s=!!e[n]:i[n]=e[n];return i.initEvent(t,s,!0),i}"undefined"==typeof window.CustomEvent&&(t.prototype=window.Event.prototype,window.CustomEvent=t)}(),Function.prototype.bind=Function.prototype.bind||function(t){var e=Array.prototype.splice.call(arguments,1),i=this,s=function(){var n=e.concat(Array.prototype.splice.call(arguments,0));return this instanceof s?void i.apply(this,n):i.apply(t,n)};return s.prototype=i.prototype,s},function(t){"classList"in t.documentElement||!Object.defineProperty||"undefined"==typeof HTMLElement||Object.defineProperty(HTMLElement.prototype,"classList",{get:function(){function t(t){return function(i){var s=e.className.split(/\s+/),n=s.indexOf(i);t(s,n,i),e.className=s.join(" ")}}var e=this,i={add:t(function(t,e,i){~e||t.push(i)}),remove:t(function(t,e){~e&&t.splice(e,1)}),toggle:t(function(t,e,i){~e?t.splice(e,1):t.push(i)}),contains:function(t){return!!~e.className.split(/\s+/).indexOf(t)},item:function(t){return e.className.split(/\s+/)[t]||null}};return Object.defineProperty(i,"length",{get:function(){return e.className.split(/\s+/).length}}),i}})}(document),function(t){if(!t.requestAnimationFrame){var e=0;t.requestAnimationFrame=t.webkitRequestAnimationFrame||function(i,s){var n=(new Date).getTime(),o=Math.max(0,16.7-(n-e)),r=t.setTimeout(function(){i(n+o)},o);return e=n+o,r},t.cancelAnimationFrame=t.webkitCancelAnimationFrame||t.webkitCancelRequestAnimationFrame||function(t){clearTimeout(t)}}}(window),function(t,e,i){if((t.os.android||t.os.ios)&&!e.FastClick){var s=function(t,e){return"LABEL"===e.tagName&&e.parentNode&&(e=e.parentNode.querySelector("input")),!(!e||"radio"!==e.type&&"checkbox"!==e.type||e.disabled)&&e};t.registerTarget({name:i,index:40,handle:s,target:!1});var n=function(i){var s=t.targets.click;if(s){var n,o;document.activeElement&&document.activeElement!==s&&document.activeElement.blur(),o=i.detail.gesture.changedTouches[0],n=document.createEvent("MouseEvents"),n.initMouseEvent("click",!0,!0,e,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),n.forwardedTouchEvent=!0,s.dispatchEvent(n),i.detail&&i.detail.gesture.preventDefault()}};e.addEventListener("tap",n),e.addEventListener("doubletap",n),e.addEventListener("click",function(e){return t.targets.click&&!e.forwardedTouchEvent?(e.stopImmediatePropagation?e.stopImmediatePropagation():e.propagationStopped=!0,e.stopPropagation(),e.preventDefault(),!1):void 0},!0)}}(mui,window,"click"),function(t,e){t(function(){if(t.os.ios){var i="mui-focusin",s="mui-bar-tab",n="mui-bar-footer",o="mui-bar-footer-secondary",r="mui-bar-footer-secondary-tab";e.addEventListener("focusin",function(a){if(!(t.os.plus&&window.plus&&plus.webview.currentWebview().children().length>0)){var l=a.target;if(l.tagName&&("TEXTAREA"===l.tagName||"INPUT"===l.tagName&&("text"===l.type||"search"===l.type||"number"===l.type))){if(l.disabled||l.readOnly)return;e.body.classList.add(i);for(var c=!1;l&&l!==e;l=l.parentNode){var h=l.classList;if(h&&h.contains(s)||h.contains(n)||h.contains(o)||h.contains(r)){c=!0;break}}if(c){var u=e.body.scrollHeight,d=e.body.scrollLeft;setTimeout(function(){window.scrollTo(d,u)},20)}}}}),e.addEventListener("focusout",function(t){var s=e.body.classList;s.contains(i)&&(s.remove(i),setTimeout(function(){window.scrollTo(e.body.scrollLeft,e.body.scrollTop)},20))})}})}(mui,document),function(t){t.namespace="mui",t.classNamePrefix=t.namespace+"-",t.classSelectorPrefix="."+t.classNamePrefix,t.className=function(e){return t.classNamePrefix+e},t.classSelector=function(e){return e.replace(/\./g,t.classSelectorPrefix)},t.eventName=function(e,i){return e+(t.namespace?"."+t.namespace:"")+(i?"."+i:"")}}(mui),function(t,e){t.gestures={session:{}},t.preventDefault=function(t){t.preventDefault()},t.stopPropagation=function(t){t.stopPropagation()},t.addGesture=function(e){return t.addAction("gestures",e)};var i=Math.round,s=Math.abs,n=Math.sqrt,o=(Math.atan,Math.atan2),r=function(t,e,i){i||(i=["x","y"]);var s=e[i[0]]-t[i[0]],o=e[i[1]]-t[i[1]];return n(s*s+o*o)},a=function(t,e){if(t.length>=2&&e.length>=2){var i=["pageX","pageY"];return r(e[1],e[0],i)/r(t[1],t[0],i)}return 1},l=function(t,e,i){i||(i=["x","y"]);var s=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return 180*o(n,s)/Math.PI},c=function(t,e){return t===e?"":s(t)>=s(e)?t>0?"left":"right":e>0?"up":"down"},h=function(t,e){var i=["pageX","pageY"];return l(e[1],e[0],i)-l(t[1],t[0],i)},u=function(t,e,i){return{x:e/t||0,y:i/t||0}},d=function(e,i){t.gestures.stoped||t.doAction("gestures",function(s,n){t.gestures.stoped||t.options.gestureConfig[n.name]!==!1&&n.handle(e,i)})},p=function(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1},f=function(t,e,i){for(var s=[],n=[],o=0;o<t.length;){var r=e?t[o][e]:t[o];n.indexOf(r)<0&&s.push(t[o]),n[o]=r,o++}return i&&(s=e?s.sort(function(t,i){return t[e]>i[e]}):s.sort()),s},g=function(t){var e=t.length;if(1===e)return{x:i(t[0].pageX),y:i(t[0].pageY)};for(var s=0,n=0,o=0;e>o;)s+=t[o].pageX,n+=t[o].pageY,o++;return{x:i(s/e),y:i(n/e)}},m=function(){return t.options.gestureConfig.pinch},v=function(e){for(var s=[],n=0;n<e.touches.length;)s[n]={pageX:i(e.touches[n].pageX),pageY:i(e.touches[n].pageY)},n++;return{timestamp:t.now(),gesture:e.gesture,touches:s,center:g(e.touches),deltaX:e.deltaX,deltaY:e.deltaY}},w=function(e){var i=t.gestures.session,s=e.center,n=i.offsetDelta||{},o=i.prevDelta||{},r=i.prevTouch||{};(e.gesture.type===t.EVENT_START||e.gesture.type===t.EVENT_END)&&(o=i.prevDelta={x:r.deltaX||0,y:r.deltaY||0},n=i.offsetDelta={x:s.x,y:s.y}),e.deltaX=o.x+(s.x-n.x),e.deltaY=o.y+(s.y-n.y)},b=function(e){var i=t.gestures.session,s=e.touches,n=s.length;i.firstTouch||(i.firstTouch=v(e)),m()&&n>1&&!i.firstMultiTouch?i.firstMultiTouch=v(e):1===n&&(i.firstMultiTouch=!1);var o=i.firstTouch,u=i.firstMultiTouch,d=u?u.center:o.center,p=e.center=g(s);e.timestamp=t.now(),e.deltaTime=e.timestamp-o.timestamp,e.angle=l(d,p),e.distance=r(d,p),w(e),e.offsetDirection=c(e.deltaX,e.deltaY),e.scale=u?a(u.touches,s):1,e.rotation=u?h(u.touches,s):0,L(e)},y=25,L=function(e){var i,n,o,r,a=t.gestures.session,l=a.lastInterval||e,h=e.timestamp-l.timestamp;if(e.gesture.type!=t.EVENT_CANCEL&&(h>y||void 0===l.velocity)){var d=l.deltaX-e.deltaX,p=l.deltaY-e.deltaY,f=u(h,d,p);n=f.x,o=f.y,i=s(f.x)>s(f.y)?f.x:f.y,r=c(d,p)||l.direction,a.lastInterval=e}else i=l.velocity,n=l.velocityX,o=l.velocityY,r=l.direction;e.velocity=i,e.velocityX=n,e.velocityY=o,e.direction=r},T={},E=function(t){for(var e=0;e<t.length;e++)!t.identifier&&(t.identifier=0);return t},x=function(e,i){var s=E(t.slice.call(e.touches||[e])),n=e.type,o=[],r=[];if(n!==t.EVENT_START&&n!==t.EVENT_MOVE||1!==s.length){var a=0,o=[],r=[],l=E(t.slice.call(e.changedTouches||[e]));i.target=e.target;var c=t.gestures.session.target||e.target;if(o=s.filter(function(t){return p(t.target,c)}),n===t.EVENT_START)for(a=0;a<o.length;)T[o[a].identifier]=!0,a++;for(a=0;a<l.length;)T[l[a].identifier]&&r.push(l[a]),(n===t.EVENT_END||n===t.EVENT_CANCEL)&&delete T[l[a].identifier],a++;if(!r.length)return!1}else T[s[0].identifier]=!0,o=s,r=s,i.target=e.target;o=f(o.concat(r),"identifier",!0);var h=o.length,u=r.length;return n===t.EVENT_START&&h-u===0&&(i.isFirst=!0,t.gestures.touch=t.gestures.session={target:e.target}),i.isFinal=(n===t.EVENT_END||n===t.EVENT_CANCEL)&&h-u===0,i.touches=o,i.changedTouches=r,!0},S=function(e){var i={gesture:e},s=x(e,i);s&&(b(i),d(e,i),t.gestures.session.prevTouch=i,e.type!==t.EVENT_END||t.isTouchable||(t.gestures.touch=t.gestures.session={}))};e.addEventListener(t.EVENT_START,S),e.addEventListener(t.EVENT_MOVE,S),e.addEventListener(t.EVENT_END,S),e.addEventListener(t.EVENT_CANCEL,S),e.addEventListener(t.EVENT_CLICK,function(e){(t.os.android||t.os.ios)&&(t.targets.popover&&e.target===t.targets.popover||t.targets.tab||t.targets.offcanvas||t.targets.modal)&&e.preventDefault()},!0),t.isScrolling=!1;var _=null;e.addEventListener("scroll",function(){t.isScrolling=!0,_&&clearTimeout(_),_=setTimeout(function(){t.isScrolling=!1},250)})}(mui,window),function(t,e){var i=0,s=function(s,n){var o=t.gestures.session,r=this.options,a=t.now();switch(s.type){case t.EVENT_MOVE:a-i>300&&(i=a,o.flickStart=n.center);break;case t.EVENT_END:case t.EVENT_CANCEL:n.flick=!1,o.flickStart&&r.flickMaxTime>a-i&&n.distance>r.flickMinDistince&&(n.flick=!0,n.flickTime=a-i,n.flickDistanceX=n.center.x-o.flickStart.x,n.flickDistanceY=n.center.y-o.flickStart.y,t.trigger(o.target,e,n),t.trigger(o.target,e+n.direction,n))}};t.addGesture({name:e,index:5,handle:s,options:{flickMaxTime:200,flickMinDistince:10}})}(mui,"flick"),function(t,e){var i=function(i,s){var n=t.gestures.session;if(i.type===t.EVENT_END||i.type===t.EVENT_CANCEL){var o=this.options;s.swipe=!1,s.direction&&o.swipeMaxTime>s.deltaTime&&s.distance>o.swipeMinDistince&&(s.swipe=!0,t.trigger(n.target,e,s),t.trigger(n.target,e+s.direction,s))}};t.addGesture({name:e,index:10,handle:i,options:{swipeMaxTime:300,swipeMinDistince:18}})}(mui,"swipe"),function(t,e){var i=function(i,s){var n=t.gestures.session;switch(i.type){case t.EVENT_START:break;case t.EVENT_MOVE:if(!s.direction||!n.target)return;n.lockDirection&&n.startDirection&&n.startDirection&&n.startDirection!==s.direction&&("up"===n.startDirection||"down"===n.startDirection?s.direction=s.deltaY<0?"up":"down":s.direction=s.deltaX<0?"left":"right"),n.drag||(n.drag=!0,t.trigger(n.target,e+"start",s)),t.trigger(n.target,e,s),t.trigger(n.target,e+s.direction,s);break;case t.EVENT_END:case t.EVENT_CANCEL:n.drag&&s.isFinal&&t.trigger(n.target,e+"end",s)}};t.addGesture({name:e,index:20,handle:i,options:{fingers:1}})}(mui,"drag"),function(t,e){var i,s,n=function(n,o){var r=t.gestures.session,a=this.options;switch(n.type){case t.EVENT_END:if(!o.isFinal)return;var l=r.target;if(!l||l.disabled||l.classList&&l.classList.contains("mui-disabled"))return;if(o.distance<a.tapMaxDistance&&o.deltaTime<a.tapMaxTime){if(t.options.gestureConfig.doubletap&&i&&i===l&&s&&o.timestamp-s<a.tapMaxInterval)return t.trigger(l,"doubletap",o),s=t.now(),void(i=l);t.trigger(l,e,o),s=t.now(),i=l}}};t.addGesture({name:e,index:30,handle:n,options:{fingers:1,tapMaxInterval:300,tapMaxDistance:5,tapMaxTime:250}})}(mui,"tap"),function(t,e){var i,s=function(s,n){var o=t.gestures.session,r=this.options;switch(s.type){case t.EVENT_START:clearTimeout(i),i=setTimeout(function(){t.trigger(o.target,e,n)},r.holdTimeout);break;case t.EVENT_MOVE:n.distance>r.holdThreshold&&clearTimeout(i);break;case t.EVENT_END:case t.EVENT_CANCEL:clearTimeout(i)}};t.addGesture({name:e,index:10,handle:s,options:{fingers:1,holdTimeout:500,holdThreshold:2}})}(mui,"longtap"),function(t,e){var i,s=function(s,n){var o=t.gestures.session,r=this.options;switch(s.type){case t.EVENT_START:t.options.gestureConfig.hold&&(i&&clearTimeout(i),i=setTimeout(function(){n.hold=!0,t.trigger(o.target,e,n)},r.holdTimeout));break;case t.EVENT_MOVE:break;case t.EVENT_END:case t.EVENT_CANCEL:i&&(clearTimeout(i)&&(i=null),t.trigger(o.target,"release",n))}};t.addGesture({name:e,index:10,handle:s,options:{fingers:1,holdTimeout:0}})}(mui,"hold"),function(t,e){var i=function(i,s){var n=this.options,o=t.gestures.session;switch(i.type){case t.EVENT_START:break;case t.EVENT_MOVE:if(t.options.gestureConfig.pinch){if(s.touches.length<2)return;o.pinch||(o.pinch=!0,t.trigger(o.target,e+"start",s)),t.trigger(o.target,e,s);var r=s.scale,a=s.rotation,l="undefined"==typeof s.lastScale?1:s.lastScale,c=1e-12;r>l?(l=r-c,t.trigger(o.target,e+"out",s)):l>r&&(l=r+c,t.trigger(o.target,e+"in",s)),Math.abs(a)>n.minRotationAngle&&t.trigger(o.target,"rotate",s)}break;case t.EVENT_END:case t.EVENT_CANCEL:t.options.gestureConfig.pinch&&o.pinch&&2===s.touches.length&&(o.pinch=!1,t.trigger(o.target,e+"end",s))}};t.addGesture({name:e,index:10,handle:i,options:{minRotationAngle:0}})}(mui,"pinch"),function(t){function e(t,e){var i="MUI_SCROLL_POSITION_"+document.location.href+"_"+e.src,s=parseFloat(localStorage.getItem(i))||0;s&&!function(t){e.onload=function(){window.scrollTo(0,t)}}(s),setInterval(function(){var t=window.scrollY;s!==t&&(localStorage.setItem(i,t+""),s=t)},100)}t.global=t.options={gestureConfig:{tap:!0,doubletap:!1,longtap:!1,hold:!1,flick:!0,swipe:!0,drag:!0,pinch:!1}},t.initGlobal=function(e){return t.options=t.extend(!0,t.global,e),this};var i={},s=!1;t.init=function(e){return s=!0,t.options=t.extend(!0,t.global,e||{}),t.ready(function(){t.doAction("inits",function(e,s){var n=!(i[s.name]&&!s.repeat);n&&(s.handle.call(t),i[s.name]=!0)})}),this},t.addInit=function(e){return t.addAction("inits",e)},t.addInit({name:"iframe",index:100,handle:function(){var e=t.options,i=e.subpages||[];!t.os.plus&&i.length&&n(i[0])}});var n=function(i){var s=document.createElement("div");s.className="mui-iframe-wrapper";var n=i.styles||{};"string"!=typeof n.top&&(n.top="0px"),"string"!=typeof n.bottom&&(n.bottom="0px"),s.style.top=n.top,s.style.bottom=n.bottom;var o=document.createElement("iframe");o.src=i.url,o.id=i.id||i.url,o.name=o.id,s.appendChild(o),document.body.appendChild(s),t.os.wechat&&e(s,o)};t(function(){var e=document.body.classList,i=[];t.os.ios?(i.push({os:"ios",version:t.os.version}),e.add("mui-ios")):t.os.android&&(i.push({os:"android",version:t.os.version}),e.add("mui-android")),t.os.wechat&&(i.push({os:"wechat",version:t.os.wechat.version}),e.add("mui-wechat")),i.length&&t.each(i,function(i,s){var n="";s.version&&t.each(s.version.split("."),function(i,o){n=n+(n?"-":"")+o,e.add(t.className(s.os+"-"+n))})})})}(mui),function(t){var e={swipeBack:!1,preloadPages:[],preloadLimit:10,keyEventBind:{backbutton:!0,menubutton:!0}},i={autoShow:!0,duration:t.os.ios?200:100,aniShow:"slide-in-right"};t.options.show&&(i=t.extend(!0,i,t.options.show)),t.currentWebview=null,t.isHomePage=!1,t.extend(!0,t.global,e),t.extend(!0,t.options,e),t.waitingOptions=function(e){return t.extend(!0,{},{autoShow:!0,title:""},e)},t.showOptions=function(e){return t.extend(!0,{},i,e)},t.windowOptions=function(e){return t.extend({scalable:!1,bounce:""},e)},t.plusReady=function(t){return window.plus?setTimeout(function(){t()},0):document.addEventListener("plusready",function(){t()},!1),this},t.fire=function(e,i,s){e&&(""!==s&&(s=s||{},t.isPlainObject(s)&&(s=JSON.stringify(s||{}).replace(/\'/g,"\\u0027").replace(/\\/g,"\\u005c"))),e.evalJS("typeof mui!=='undefined'&&mui.receive('"+i+"','"+s+"')"))},t.receive=function(e,i){if(e){try{i&&(i=JSON.parse(i))}catch(s){}t.trigger(document,e,i)}};var s=function(e){if(!e.preloaded){t.fire(e,"preload");for(var i=e.children(),s=0;s<i.length;s++)t.fire(i[s],"preload");e.preloaded=!0}},n=function(e,i,s){if(s){if(!e[i+"ed"]){t.fire(e,i);for(var n=e.children(),o=0;o<n.length;o++)t.fire(n[o],i);e[i+"ed"]=!0}}else{t.fire(e,i);for(var n=e.children(),o=0;o<n.length;o++)t.fire(n[o],i)}};t.openWindow=function(e,i,o){if("object"==typeof e?(o=e,e=o.url,i=o.id||e):"object"==typeof i?(o=i,i=e):i=i||e,!t.os.plus)return void(t.os.ios||t.os.android?window.top.location.href=e:window.parent.location.href=e);if(window.plus){o=o||{};var r,a,l=o.params||{},c=null,h=null;if(t.webviews[i]&&(h=t.webviews[i],plus.webview.getWebviewById(i)&&(c=h.webview)),h&&c)return r=h.show,r=o.show?t.extend(r,o.show):r,c.show(r.aniShow,r.duration,function(){s(c),n(c,"pagebeforeshow",!1)}),h.afterShowMethodName&&c.evalJS(h.afterShowMethodName+"('"+JSON.stringify(l)+"')"),c;if(o.createNew!==!0){if(c=plus.webview.getWebviewById(i))return r=t.showOptions(o.show),r.autoShow&&c.show(r.aniShow,r.duration,function(){s(c),n(c,"pagebeforeshow",!1)}),c;if(!e)throw new Error("webview["+i+"] does not exist")}var u=t.waitingOptions(o.waiting);if(u.autoShow&&(a=plus.nativeUI.showWaiting(u.title,u.options)),o=t.extend(o,{id:i,url:e}),c=t.createWindow(o),r=t.showOptions(o.show),r.autoShow){var d=function(){a&&a.close(),c.show(r.aniShow,r.duration,function(){s(c),n(c,"pagebeforeshow",!1)}),c.showed=!0,o.afterShowMethodName&&c.evalJS(o.afterShowMethodName+"('"+JSON.stringify(l)+"')")};e?c.addEventListener("loaded",d,!1):d()}return c}},t.createWindow=function(e,i){if(window.plus){var s,n=e.id||e.url;if(e.preload){t.webviews[n]&&t.webviews[n].webview.getURL()?s=t.webviews[n].webview:(e.createNew!==!0&&(s=plus.webview.getWebviewById(n)),s||(s=plus.webview.create(e.url,n,t.windowOptions(e.styles),t.extend({preload:!0},e.extras)),e.subpages&&t.each(e.subpages,function(e,i){var n=i.id||i.url;if(n){var o=plus.webview.getWebviewById(n);o||(o=plus.webview.create(i.url,n,t.windowOptions(i.styles),t.extend({preload:!0},i.extras))),s.append(o)}}))),t.webviews[n]={webview:s,preload:!0,show:t.showOptions(e.show),afterShowMethodName:e.afterShowMethodName};var o=t.data.preloads,r=o.indexOf(n);if(~r&&o.splice(r,1),o.push(n),o.length>t.options.preloadLimit){var a=t.data.preloads.shift(),l=t.webviews[a];l&&l.webview&&t.closeAll(l.webview),delete t.webviews[a]}}else i!==!1&&(s=plus.webview.create(e.url,n,t.windowOptions(e.styles),e.extras),e.subpages&&t.each(e.subpages,function(e,i){var n=i.id||i.url,o=plus.webview.getWebviewById(n);o||(o=plus.webview.create(i.url,n,t.windowOptions(i.styles),i.extras)),s.append(o)}));return s}},t.preload=function(e){return e.preload||(e.preload=!0),t.createWindow(e)},t.closeOpened=function(e){var i=e.opened();if(i)for(var s=0,n=i.length;n>s;s++){var o=i[s],r=o.opened();r&&r.length>0?t.closeOpened(o):o.parent()!==e&&o.close("none")}},t.closeAll=function(e,i){t.closeOpened(e),i?e.close(i):e.close()},t.createWindows=function(e){t.each(e,function(e,i){t.createWindow(i,!1)})},t.appendWebview=function(e){if(window.plus){var i,s=e.id||e.url;return t.webviews[s]||(plus.webview.getWebviewById(s)||(i=plus.webview.create(e.url,s,e.styles,e.extras)),plus.webview.currentWebview().append(i),t.webviews[s]=e),i}},t.webviews={},t.data.preloads=[],t.plusReady(function(){t.currentWebview=plus.webview.currentWebview()}),t.addInit({name:"5+",index:100,handle:function(){var e=t.options,i=e.subpages||[];t.os.plus&&t.plusReady(function(){t.each(i,function(e,i){t.appendWebview(i)}),plus.webview.currentWebview()===plus.webview.getWebviewById(plus.runtime.appid)&&(t.isHomePage=!0,setTimeout(function(){s(plus.webview.currentWebview())},300)),t.os.ios&&t.options.statusBarBackground&&plus.navigator.setStatusBarBackground(t.options.statusBarBackground),t.os.android&&parseFloat(t.os.version)<4.4&&null==plus.webview.currentWebview().parent()&&document.addEventListener("resume",function(){var t=document.body;t.style.display="none",setTimeout(function(){t.style.display=""},10)})})}}),window.addEventListener("preload",function(){var e=t.options.preloadPages||[];t.plusReady(function(){t.each(e,function(e,i){t.createWindow(t.extend(i,{preload:!0}))})})}),t.supportStatusbarOffset=function(){return t.os.plus&&t.os.ios&&parseFloat(t.os.version)>=7},t.ready(function(){t.supportStatusbarOffset()&&document.body.classList.add("mui-statusbar")})}(mui),function(t,e){t.addBack=function(e){return t.addAction("backs",e)},t.addBack({name:"browser",index:100,handle:function(){return e.history.length>1&&(e.history.back(),!0)}}),t.back=function(){("function"!=typeof t.options.beforeback||t.options.beforeback()!==!1)&&t.doAction("backs")},e.addEventListener("tap",function(e){var i=t.targets.action;i&&i.classList.contains("mui-action-back")&&(t.back(),t.targets.action=!1)}),e.addEventListener("swiperight",function(e){var i=e.detail;t.options.swipeBack===!0&&Math.abs(i.angle)<3&&t.back()})}(mui,window),function(t,e){t.os.plus&&t.os.android&&t.addBack({name:"mui",index:5,handle:function(){if(t.targets._popover&&t.targets._popover.classList.contains("mui-active"))return t(t.targets._popover).popover("hide"),!0;var e=document.querySelector(".mui-off-canvas-wrap.mui-active");if(e)return t(e).offCanvas("close"),!0;var i=t.isFunction(t.getPreviewImage)&&t.getPreviewImage();return i&&i.isShown()?(i.close(),!0):t.closePopup()}}),t.__back__first=null,t.addBack({name:"5+",index:10,handle:function(){if(!e.plus)return!1;var i=plus.webview.currentWebview(),s=i.parent();return s?s.evalJS("mui&&mui.back();"):i.canBack(function(s){s.canBack?e.history.back():i.id===plus.runtime.appid?t.__back__first?(new Date).getTime()-t.__back__first<2e3&&plus.runtime.quit():(t.__back__first=(new Date).getTime(),mui.toast("再按一次退出应用"),setTimeout(function(){t.__back__first=null},2e3)):i.preload?i.hide("auto"):t.closeAll(i)}),!0}}),t.menu=function(){var i=document.querySelector(".mui-action-menu");if(i)t.trigger(i,t.EVENT_START),t.trigger(i,"tap");else if(e.plus){var s=t.currentWebview,n=s.parent();n&&n.evalJS("mui&&mui.menu();")}};var i=function(){t.back()},s=function(){t.menu()};t.plusReady(function(){t.options.keyEventBind.backbutton&&plus.key.addEventListener("backbutton",i,!1),t.options.keyEventBind.menubutton&&plus.key.addEventListener("menubutton",s,!1)}),t.addInit({name:"keyEventBind",index:1e3,handle:function(){t.plusReady(function(){t.options.keyEventBind.backbutton||plus.key.removeEventListener("backbutton",i),t.options.keyEventBind.menubutton||plus.key.removeEventListener("menubutton",s)})}})}(mui,window),function(t){t.addInit({name:"pullrefresh",index:1e3,handle:function(){var e=t.options,i=e.pullRefresh||{},s=i.down&&i.down.hasOwnProperty("callback"),n=i.up&&i.up.hasOwnProperty("callback");if(s||n){var o=i.container;if(o){var r=t(o);1===r.length&&(t.os.plus&&t.os.android?t.plusReady(function(){var e=plus.webview.currentWebview();if(n){var o={};o.up=i.up,o.webviewId=e.id||e.getURL(),r.pullRefresh(o)}if(s){var a=e.parent(),l=e.id||e.getURL();if(a){n||r.pullRefresh({webviewId:l});var c={webviewId:l};c.down=t.extend({},i.down),c.down.callback="_CALLBACK",a.evalJS("mui&&mui(document.querySelector('.mui-content')).pullRefresh('"+JSON.stringify(c)+"')")}}}):r.pullRefresh(i))}}}})}(mui),function(t,e,i){var s="application/json",n="text/html",o=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,r=/^(?:text|application)\/javascript/i,a=/^(?:text|application)\/xml/i,l=/^\s*$/;t.ajaxSettings={type:"GET",beforeSend:t.noop,success:t.noop,error:t.noop,complete:t.noop,context:null,xhr:function(t){return new e.XMLHttpRequest},accepts:{script:"text/javascript, application/javascript, application/x-javascript",json:s,xml:"application/xml, text/xml",html:n,text:"text/plain"},timeout:0,processData:!0,cache:!0};var c=function(t,e){var i=e.context;return e.beforeSend.call(i,t,e)!==!1&&void 0},h=function(t,e,i){i.success.call(i.context,t,"success",e),d("success",e,i)},u=function(t,e,i,s){s.error.call(s.context,i,e,t),d(e,i,s)},d=function(t,e,i){i.complete.call(i.context,e,t)},p=function(e,i,s,n){var o,r=t.isArray(i),a=t.isPlainObject(i);t.each(i,function(i,l){o=t.type(l),n&&(i=s?n:n+"["+(a||"object"===o||"array"===o?i:"")+"]"),!n&&r?e.add(l.name,l.value):"array"===o||!s&&"object"===o?p(e,l,s,i):e.add(i,l)})},f=function(e){e.processData&&e.data&&"string"!=typeof e.data&&(e.data=t.param(e.data,e.traditional)),!e.data||e.type&&"GET"!==e.type.toUpperCase()||(e.url=g(e.url,e.data),e.data=i)},g=function(t,e){return""===e?t:(t+"&"+e).replace(/[&?]{1,2}/,"?")},m=function(t){return t&&(t=t.split(";",2)[0]),t&&(t===n?"html":t===s?"json":r.test(t)?"script":a.test(t)&&"xml")||"text";
},v=function(e,s,n,o){return t.isFunction(s)&&(o=n,n=s,s=i),t.isFunction(n)||(o=n,n=i),{url:e,data:s,success:n,dataType:o}};t.ajax=function(s,n){"object"==typeof s&&(n=s,s=i);var o=n||{};o.url=s||o.url;for(var r in t.ajaxSettings)o[r]===i&&(o[r]=t.ajaxSettings[r]);f(o);var a=o.dataType;o.cache!==!1&&(n&&n.cache===!0||"script"!==a)||(o.url=g(o.url,"_="+t.now()));var d,p=o.accepts[a&&a.toLowerCase()],v={},w=function(t,e){v[t.toLowerCase()]=[t,e]},b=/^([\w-]+:)\/\//.test(o.url)?RegExp.$1:e.location.protocol,y=o.xhr(o),L=y.setRequestHeader;if(w("X-Requested-With","XMLHttpRequest"),w("Accept",p||"*/*"),(p=o.mimeType||p)&&(p.indexOf(",")>-1&&(p=p.split(",",2)[0]),y.overrideMimeType&&y.overrideMimeType(p)),(o.contentType||o.contentType!==!1&&o.data&&"GET"!==o.type.toUpperCase())&&w("Content-Type",o.contentType||"application/x-www-form-urlencoded"),o.headers)for(var T in o.headers)w(T,o.headers[T]);if(y.setRequestHeader=w,y.onreadystatechange=function(){if(4===y.readyState){y.onreadystatechange=t.noop,clearTimeout(d);var e,i=!1,s="file:"===b;if(y.status>=200&&y.status<300||304===y.status||0===y.status&&s&&y.responseText){a=a||m(o.mimeType||y.getResponseHeader("content-type")),e=y.responseText;try{"script"===a?(0,eval)(e):"xml"===a?e=y.responseXML:"json"===a&&(e=l.test(e)?null:t.parseJSON(e))}catch(n){i=n}i?u(i,"parsererror",y,o):h(e,y,o)}else{var r=y.status?"error":"abort",c=y.statusText||null;s&&(r="error",c="404"),u(c,r,y,o)}}},c(y,o)===!1)return y.abort(),u(null,"abort",y,o),y;if(o.xhrFields)for(var T in o.xhrFields)y[T]=o.xhrFields[T];var E=!("async"in o)||o.async;y.open(o.type.toUpperCase(),o.url,E,o.username,o.password);for(var T in v)L.apply(y,v[T]);return o.timeout>0&&(d=setTimeout(function(){y.onreadystatechange=t.noop,y.abort(),u(null,"timeout",y,o)},o.timeout)),y.send(o.data?o.data:null),y},t.param=function(t,e){var i=[];return i.add=function(t,e){this.push(encodeURIComponent(t)+"="+encodeURIComponent(e))},p(i,t,e),i.join("&").replace(/%20/g,"+")},t.get=function(){return t.ajax(v.apply(null,arguments))},t.post=function(){var e=v.apply(null,arguments);return e.type="POST",t.ajax(e)},t.getJSON=function(){var e=v.apply(null,arguments);return e.dataType="json",t.ajax(e)},t.fn.load=function(e,i,s){if(!this.length)return this;var n,r=this,a=e.split(/\s/),l=v(e,i,s),c=l.success;return a.length>1&&(l.url=a[0],n=a[1]),l.success=function(t){if(n){var e=document.createElement("div");e.innerHTML=t.replace(o,"");var i=document.createElement("div"),s=e.querySelectorAll(n);if(s&&s.length>0)for(var a=0,l=s.length;l>a;a++)i.appendChild(s[a]);r[0].innerHTML=i.innerHTML}else r[0].innerHTML=t;c&&c.apply(r,arguments)},t.ajax(l),this}}(mui,window),function(t){var e=document.createElement("a");e.href=window.location.href,t.plusReady(function(){t.ajaxSettings=t.extend(t.ajaxSettings,{xhr:function(t){if(t.crossDomain)return new plus.net.XMLHttpRequest;if("file:"!==e.protocol){var i=document.createElement("a");if(i.href=t.url,i.href=i.href,t.crossDomain=e.protocol+"//"+e.host!=i.protocol+"//"+i.host,t.crossDomain)return new plus.net.XMLHttpRequest}return new window.XMLHttpRequest}})})}(mui),function(t,e,i){t.offset=function(t){var s={top:0,left:0};return typeof t.getBoundingClientRect!==i&&(s=t.getBoundingClientRect()),{top:s.top+e.pageYOffset-t.clientTop,left:s.left+e.pageXOffset-t.clientLeft}}}(mui,window),function(t,e){t.scrollTo=function(t,i,s){i=i||1e3;var n=function(i){if(0>=i)return e.scrollTo(0,t),void(s&&s());var o=t-e.scrollY;setTimeout(function(){e.scrollTo(0,e.scrollY+o/i*10),n(i-10)},16.7)};n(i)},t.animationFrame=function(t){var e,i,s;return function(){e=arguments,s=this,i||(i=!0,requestAnimationFrame(function(){t.apply(s,e),i=!1}))}}}(mui,window),function(t){var e=!1,i=/xyz/.test(function(){xyz})?/\b_super\b/:/.*/,s=function(){};s.extend=function(t){function s(){!e&&this.init&&this.init.apply(this,arguments)}var n=this.prototype;e=!0;var o=new this;e=!1;for(var r in t)o[r]="function"==typeof t[r]&&"function"==typeof n[r]&&i.test(t[r])?function(t,e){return function(){var i=this._super;this._super=n[t];var s=e.apply(this,arguments);return this._super=i,s}}(r,t[r]):t[r];return s.prototype=o,s.prototype.constructor=s,s.extend=arguments.callee,s},t.Class=s}(mui),function(t,e,i){var s="mui-pull-top-pocket",n="mui-pull-bottom-pocket",o="mui-pull",r="mui-pull-loading",a="mui-pull-caption",l="mui-pull-caption-down",c="mui-pull-caption-refresh",h="mui-pull-caption-nomore",u="mui-icon",d="mui-spinner",p="mui-icon-pulldown",f="mui-block",g="mui-hidden",m="mui-visibility",v=r+" "+u+" "+p,w=r+" "+u+" "+p,b=r+" "+u+" "+d,y=['<div class="'+o+'">','<div class="{icon}"></div>','<div class="'+a+'">{contentrefresh}</div>',"</div>"].join(""),L={init:function(e,i){this._super(e,t.extend(!0,{scrollY:!0,scrollX:!1,indicators:!0,deceleration:.003,down:{height:50,contentinit:"下拉可以刷新",contentdown:"下拉可以刷新",contentover:"释放立即刷新",contentrefresh:"正在刷新..."},up:{height:50,auto:!1,contentinit:"上拉显示更多",contentdown:"上拉显示更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了",duration:300}},i))},_init:function(){this._super(),this._initPocket()},_initPulldownRefresh:function(){this.pulldown=!0,this.pullPocket=this.topPocket,this.pullPocket.classList.add(f),this.pullPocket.classList.add(m),this.pullCaption=this.topCaption,this.pullLoading=this.topLoading},_initPullupRefresh:function(){this.pulldown=!1,this.pullPocket=this.bottomPocket,this.pullPocket.classList.add(f),this.pullPocket.classList.add(m),this.pullCaption=this.bottomCaption,this.pullLoading=this.bottomLoading},_initPocket:function(){var t=this.options;t.down&&t.down.hasOwnProperty("callback")&&(this.topPocket=this.scroller.querySelector("."+s),this.topPocket||(this.topPocket=this._createPocket(s,t.down,w),this.wrapper.insertBefore(this.topPocket,this.wrapper.firstChild)),this.topLoading=this.topPocket.querySelector("."+r),this.topCaption=this.topPocket.querySelector("."+a)),t.up&&t.up.hasOwnProperty("callback")&&(this.bottomPocket=this.scroller.querySelector("."+n),this.bottomPocket||(this.bottomPocket=this._createPocket(n,t.up,b),this.scroller.appendChild(this.bottomPocket)),this.bottomLoading=this.bottomPocket.querySelector("."+r),this.bottomCaption=this.bottomPocket.querySelector("."+a),this.wrapper.addEventListener("scrollbottom",this))},_createPocket:function(t,i,s){var n=e.createElement("div");return n.className=t,n.innerHTML=y.replace("{contentrefresh}",i.contentinit).replace("{icon}",s),n},_resetPullDownLoading:function(){var t=this.pullLoading;t&&(this.pullCaption.innerHTML=this.options.down.contentdown,t.style.webkitTransition="",t.style.webkitTransform="",t.style.webkitAnimation="",t.className=w)},_setCaptionClass:function(t,e,i){if(!t)switch(i){case this.options.up.contentdown:e.className=a+" "+l;break;case this.options.up.contentrefresh:e.className=a+" "+c;break;case this.options.up.contentnomore:e.className=a+" "+h}},_setCaption:function(t,e){if(!this.loading){var i=this.options,s=this.pullPocket,n=this.pullCaption,o=this.pullLoading,r=this.pulldown,a=this;s&&(e?setTimeout(function(){n.innerHTML=a.lastTitle=t,r?o.className=w:(a._setCaptionClass(!1,n,t),o.className=b),o.style.webkitAnimation="",o.style.webkitTransition="",o.style.webkitTransform=""},100):t!==this.lastTitle&&(n.innerHTML=t,r?t===i.down.contentrefresh?(o.className=b,o.style.webkitAnimation="spinner-spin 1s step-end infinite"):t===i.down.contentover?(o.className=v,o.style.webkitTransition="-webkit-transform 0.3s ease-in",o.style.webkitTransform="rotate(180deg)"):t===i.down.contentdown&&(o.className=w,o.style.webkitTransition="-webkit-transform 0.3s ease-in",o.style.webkitTransform="rotate(0deg)"):(t===i.up.contentrefresh?o.className=b+" "+m:o.className=b+" "+g,a._setCaptionClass(!1,n,t)),this.lastTitle=t))}}};t.PullRefresh=L}(mui,document),function(t,e,i,s){var n="mui-scroll",o="mui-scrollbar",r="mui-scrollbar-indicator",a=o+"-vertical",l=o+"-horizontal",c="mui-active",h={quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(t){return Math.sqrt(1- --t*t)}},outCirc:{style:"cubic-bezier(0.075, 0.82, 0.165, 1)"},outCubic:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)"}},u=t.Class.extend({init:function(e,i){this.wrapper=this.element=e,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller&&this.scroller.style,this.stopped=!1,this.options=t.extend(!0,{scrollY:!0,scrollX:!1,startX:0,startY:0,indicators:!0,stopPropagation:!1,hardwareAccelerated:!0,fixedBadAndorid:!1,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|VIDEO)$/},momentum:!0,snapX:.5,snap:!1,bounce:!0,bounceTime:500,bounceEasing:h.outCirc,scrollTime:500,scrollEasing:h.outCubic,directionLockThreshold:5,parallaxElement:!1,parallaxRatio:.5},i),this.x=0,this.y=0,this.translateZ=this.options.hardwareAccelerated?" translateZ(0)":"",this._init(),this.scroller&&(this.refresh(),this.scrollTo(this.options.startX,this.options.startY))},_init:function(){this._initParallax(),this._initIndicators(),this._initEvent()},_initParallax:function(){this.options.parallaxElement&&(this.parallaxElement=i.querySelector(this.options.parallaxElement),this.parallaxStyle=this.parallaxElement.style,this.parallaxHeight=this.parallaxElement.offsetHeight,this.parallaxImgStyle=this.parallaxElement.querySelector("img").style)},_initIndicators:function(){var t=this;if(t.indicators=[],this.options.indicators){var e,i=[];t.options.scrollY&&(e={el:this._createScrollBar(a),listenX:!1},this.wrapper.appendChild(e.el),i.push(e)),this.options.scrollX&&(e={el:this._createScrollBar(l),listenY:!1},this.wrapper.appendChild(e.el),i.push(e));for(var s=i.length;s--;)this.indicators.push(new d(this,i[s]))}},_initSnap:function(){this.currentPage={},this.pages=[];for(var t=this.snaps,e=t.length,i=0,s=-1,n=0,o=0,r=0,a=0,l=0;e>l;l++){var h=t[l],u=h.offsetLeft,d=h.offsetWidth;(0===l||u<=t[l-1].offsetLeft)&&(i=0,s++),this.pages[i]||(this.pages[i]=[]),n=this._getSnapX(u),a=Math.round(d*this.options.snapX),o=n-a,r=n-d+a,this.pages[i][s]={x:n,leftX:o,rightX:r,pageX:i,element:h},h.classList.contains(c)&&(this.currentPage=this.pages[i][0]),n>=this.maxScrollX&&i++}this.options.startX=this.currentPage.x||0},_getSnapX:function(t){return Math.max(Math.min(0,-t+this.wrapperWidth/2),this.maxScrollX)},_gotoPage:function(t){this.currentPage=this.pages[Math.min(t,this.pages.length-1)][0];for(var e=0,i=this.snaps.length;i>e;e++)e===t?this.snaps[e].classList.add(c):this.snaps[e].classList.remove(c);this.scrollTo(this.currentPage.x,0,this.options.scrollTime)},_nearestSnap:function(t){if(!this.pages.length)return{x:0,pageX:0};var e=0,i=this.pages.length;for(t>0?t=0:t<this.maxScrollX&&(t=this.maxScrollX);i>e;e++){var s="left"===this.direction?this.pages[e][0].leftX:this.pages[e][0].rightX;if(t>=s)return this.pages[e][0]}return{x:0,pageX:0}},_initEvent:function(i){var s=i?"removeEventListener":"addEventListener";e[s]("orientationchange",this),e[s]("resize",this),this.scroller[s]("webkitTransitionEnd",this),this.wrapper[s](t.EVENT_START,this),this.wrapper[s](t.EVENT_CANCEL,this),this.wrapper[s](t.EVENT_END,this),this.wrapper[s]("drag",this),this.wrapper[s]("dragend",this),this.wrapper[s]("flick",this),this.wrapper[s]("scrollend",this),this.options.scrollX&&this.wrapper[s]("swiperight",this);var n=this.wrapper.querySelector(".mui-segmented-control");n&&mui(n)[i?"off":"on"]("click","a",t.preventDefault),this.wrapper[s]("scrollstart",this),this.wrapper[s]("refresh",this)},_handleIndicatorScrollend:function(){this.indicators.map(function(t){t.fade()})},_handleIndicatorScrollstart:function(){this.indicators.map(function(t){t.fade(1)})},_handleIndicatorRefresh:function(){this.indicators.map(function(t){t.refresh()})},handleEvent:function(e){if(this.stopped)return void this.resetPosition();switch(e.type){case t.EVENT_START:this._start(e);break;case"drag":this.options.stopPropagation&&e.stopPropagation(),this._drag(e);break;case"dragend":case"flick":this.options.stopPropagation&&e.stopPropagation(),this._flick(e);break;case t.EVENT_CANCEL:case t.EVENT_END:this._end(e);break;case"webkitTransitionEnd":this.transitionTimer&&this.transitionTimer.cancel(),this._transitionEnd(e);break;case"scrollstart":this._handleIndicatorScrollstart(e);break;case"scrollend":this._handleIndicatorScrollend(e),this._scrollend(e),e.stopPropagation();break;case"orientationchange":case"resize":this._resize();break;case"swiperight":e.stopPropagation();break;case"refresh":this._handleIndicatorRefresh(e)}},_start:function(e){if(this.moved=this.needReset=!1,this._transitionTime(),this.isInTransition){this.needReset=!0,this.isInTransition=!1;var i=t.parseTranslateMatrix(t.getStyles(this.scroller,"webkitTransform"));this.setTranslate(Math.round(i.x),Math.round(i.y)),t.trigger(this.scroller,"scrollend",this),e.preventDefault()}this.reLayout(),t.trigger(this.scroller,"beforescrollstart",this)},_getDirectionByAngle:function(t){return-80>t&&t>-100?"up":t>=80&&100>t?"down":t>=170||-170>=t?"left":t>=-35&&10>=t?"right":null},_drag:function(i){var s=i.detail;if((this.options.scrollY||"up"===s.direction||"down"===s.direction)&&t.os.ios&&parseFloat(t.os.version)>=8){var n=s.gesture.touches[0].clientY;if(n+10>e.innerHeight||10>n)return void this.resetPosition(this.options.bounceTime)}var o=isReturn=!1;if(this._getDirectionByAngle(s.angle),"left"===s.direction||"right"===s.direction?this.options.scrollX?(o=!0,this.moved||(t.gestures.session.lockDirection=!0,t.gestures.session.startDirection=s.direction)):this.options.scrollY&&!this.moved&&(isReturn=!0):"up"===s.direction||"down"===s.direction?this.options.scrollY?(o=!0,this.moved||(t.gestures.session.lockDirection=!0,t.gestures.session.startDirection=s.direction)):this.options.scrollX&&!this.moved&&(isReturn=!0):isReturn=!0,(this.moved||o)&&(i.stopPropagation(),s.gesture&&s.gesture.preventDefault()),!isReturn){this.moved?i.stopPropagation():t.trigger(this.scroller,"scrollstart",this);var r=0,a=0;this.moved?(r=s.deltaX-t.gestures.session.prevTouch.deltaX,a=s.deltaY-t.gestures.session.prevTouch.deltaY):(r=s.deltaX,a=s.deltaY);var l=Math.abs(s.deltaX),c=Math.abs(s.deltaY);l>c+this.options.directionLockThreshold?a=0:c>=l+this.options.directionLockThreshold&&(r=0),r=this.hasHorizontalScroll?r:0,a=this.hasVerticalScroll?a:0;var h=this.x+r,u=this.y+a;(h>0||h<this.maxScrollX)&&(h=this.options.bounce?this.x+r/3:h>0?0:this.maxScrollX),(u>0||u<this.maxScrollY)&&(u=this.options.bounce?this.y+a/3:u>0?0:this.maxScrollY),this.requestAnimationFrame||this._updateTranslate(),this.direction=s.deltaX>0?"right":"left",this.moved=!0,this.x=h,this.y=u,t.trigger(this.scroller,"scroll",this)}},_flick:function(e){if(this.moved){e.stopPropagation();var i=e.detail;if(this._clearRequestAnimationFrame(),"dragend"!==e.type||!i.flick){var s=Math.round(this.x),n=Math.round(this.y);if(this.isInTransition=!1,!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(s,n),"dragend"===e.type)return void t.trigger(this.scroller,"scrollend",this);var o=0,r="";return this.options.momentum&&i.flickTime<300&&(momentumX=this.hasHorizontalScroll?this._momentum(this.x,i.flickDistanceX,i.flickTime,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:s,duration:0},momentumY=this.hasVerticalScroll?this._momentum(this.y,i.flickDistanceY,i.flickTime,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:n,duration:0},s=momentumX.destination,n=momentumY.destination,o=Math.max(momentumX.duration,momentumY.duration),this.isInTransition=!0),s!=this.x||n!=this.y?((s>0||s<this.maxScrollX||n>0||n<this.maxScrollY)&&(r=h.quadratic),void this.scrollTo(s,n,o,r)):void t.trigger(this.scroller,"scrollend",this)}}}},_end:function(e){this.needReset=!1,(!this.moved&&this.needReset||e.type===t.EVENT_CANCEL)&&this.resetPosition()},_transitionEnd:function(e){e.target==this.scroller&&this.isInTransition&&(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,t.trigger(this.scroller,"scrollend",this)))},_scrollend:function(e){(0===this.y&&0===this.maxScrollY||Math.abs(this.y)>0&&this.y<=this.maxScrollY)&&t.trigger(this.scroller,"scrollbottom",this)},_resize:function(){var t=this;clearTimeout(t.resizeTimeout),t.resizeTimeout=setTimeout(function(){t.refresh()},t.options.resizePolling)},_transitionTime:function(e){if(e=e||0,this.scrollerStyle.webkitTransitionDuration=e+"ms",this.parallaxElement&&this.options.scrollY&&(this.parallaxStyle.webkitTransitionDuration=e+"ms"),this.options.fixedBadAndorid&&!e&&t.os.isBadAndroid&&(this.scrollerStyle.webkitTransitionDuration="0.001s",this.parallaxElement&&this.options.scrollY&&(this.parallaxStyle.webkitTransitionDuration="0.001s")),this.indicators)for(var i=this.indicators.length;i--;)this.indicators[i].transitionTime(e);e&&(this.transitionTimer&&this.transitionTimer.cancel(),this.transitionTimer=t.later(function(){t.trigger(this.scroller,"webkitTransitionEnd")},e+100,this))},_transitionTimingFunction:function(t){if(this.scrollerStyle.webkitTransitionTimingFunction=t,this.parallaxElement&&this.options.scrollY&&(this.parallaxStyle.webkitTransitionDuration=t),this.indicators)for(var e=this.indicators.length;e--;)this.indicators[e].transitionTimingFunction(t)},_translate:function(t,e){this.x=t,this.y=e},_clearRequestAnimationFrame:function(){this.requestAnimationFrame&&(cancelAnimationFrame(this.requestAnimationFrame),this.requestAnimationFrame=null)},_updateTranslate:function(){var t=this;(t.x!==t.lastX||t.y!==t.lastY)&&t.setTranslate(t.x,t.y),t.requestAnimationFrame=requestAnimationFrame(function(){t._updateTranslate()})},_createScrollBar:function(t){var e=i.createElement("div"),s=i.createElement("div");return e.className=o+" "+t,s.className=r,e.appendChild(s),t===a?(this.scrollbarY=e,this.scrollbarIndicatorY=s):t===l&&(this.scrollbarX=e,this.scrollbarIndicatorX=s),this.wrapper.appendChild(e),e},_preventDefaultException:function(t,e){for(var i in e)if(e[i].test(t[i]))return!0;return!1},_reLayout:function(){if(this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.indicators.map(function(t){t.refresh()}),this.options.snap&&"string"==typeof this.options.snap){var t=this.scroller.querySelectorAll(this.options.snap);this.itemLength=0,this.snaps=[];for(var e=0,i=t.length;i>e;e++){var s=t[e];s.parentNode===this.scroller&&(this.itemLength++,this.snaps.push(s))}this._initSnap()}},_momentum:function(t,e,i,n,o,r){var a,l,c=parseFloat(Math.abs(e)/i);return r=r===s?6e-4:r,a=t+c*c/(2*r)*(0>e?-1:1),l=c/r,n>a?(a=o?n-o/2.5*(c/8):n,e=Math.abs(a-t),l=e/c):a>0&&(a=o?o/2.5*(c/8):0,e=Math.abs(t)+a,l=e/c),{destination:Math.round(a),duration:l}},_getTranslateStr:function(t,e){return this.options.hardwareAccelerated?"translate3d("+t+"px,"+e+"px,0px) "+this.translateZ:"translate("+t+"px,"+e+"px) "},setStopped:function(t){this.stopped=!!t},setTranslate:function(e,i){if(this.x=e,this.y=i,this.scrollerStyle.webkitTransform=this._getTranslateStr(e,i),this.parallaxElement&&this.options.scrollY){var s=i*this.options.parallaxRatio,n=1+s/((this.parallaxHeight-s)/2);n>1?(this.parallaxImgStyle.opacity=1-s/100*this.options.parallaxRatio,this.parallaxStyle.webkitTransform=this._getTranslateStr(0,-s)+" scale("+n+","+n+")"):(this.parallaxImgStyle.opacity=1,this.parallaxStyle.webkitTransform=this._getTranslateStr(0,-1)+" scale(1,1)")}if(this.indicators)for(var o=this.indicators.length;o--;)this.indicators[o].updatePosition();this.lastX=this.x,this.lastY=this.y,t.trigger(this.scroller,"scroll",this)},reLayout:function(){this.wrapper.offsetHeight;var e=parseFloat(t.getStyles(this.wrapper,"padding-left"))||0,i=parseFloat(t.getStyles(this.wrapper,"padding-right"))||0,s=parseFloat(t.getStyles(this.wrapper,"padding-top"))||0,n=parseFloat(t.getStyles(this.wrapper,"padding-bottom"))||0,o=this.wrapper.clientWidth,r=this.wrapper.clientHeight;this.scrollerWidth=this.scroller.offsetWidth,this.scrollerHeight=this.scroller.offsetHeight,this.wrapperWidth=o-e-i,this.wrapperHeight=r-s-n,this.maxScrollX=Math.min(this.wrapperWidth-this.scrollerWidth,0),this.maxScrollY=Math.min(this.wrapperHeight-this.scrollerHeight,0),this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this._reLayout()},resetPosition:function(t){var e=this.x,i=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?e=0:this.x<this.maxScrollX&&(e=this.maxScrollX),!this.hasVerticalScroll||this.y>0?i=0:this.y<this.maxScrollY&&(i=this.maxScrollY),(e!=this.x||i!=this.y)&&(this.scrollTo(e,i,t,this.options.scrollEasing),!0)},_reInit:function(){for(var t=this.wrapper.querySelectorAll("."+n),e=0,i=t.length;i>e;e++)if(t[e].parentNode===this.wrapper){this.scroller=t[e];break}this.scrollerStyle=this.scroller&&this.scroller.style},refresh:function(){this._reInit(),this.reLayout(),t.trigger(this.scroller,"refresh",this),this.resetPosition()},scrollTo:function(t,e,i,s){var s=s||h.circular;this.isInTransition=i>0,this.isInTransition?(this._clearRequestAnimationFrame(),this._transitionTimingFunction(s.style),this._transitionTime(i),this.setTranslate(t,e)):this.setTranslate(t,e)},scrollToBottom:function(t,e){t=t||this.options.scrollTime,this.scrollTo(0,this.maxScrollY,t,e)},gotoPage:function(t){this._gotoPage(t)},destroy:function(){this._initEvent(!0),delete t.data[this.wrapper.getAttribute("data-scroll")],this.wrapper.setAttribute("data-scroll","")}}),d=function(e,s){this.wrapper="string"==typeof s.el?i.querySelector(s.el):s.el,this.wrapperStyle=this.wrapper.style,this.indicator=this.wrapper.children[0],this.indicatorStyle=this.indicator.style,this.scroller=e,this.options=t.extend({listenX:!0,listenY:!0,fade:!1,speedRatioX:0,speedRatioY:0},s),this.sizeRatioX=1,this.sizeRatioY=1,this.maxPosX=0,this.maxPosY=0,this.options.fade&&(this.wrapperStyle.webkitTransform=this.scroller.translateZ,this.wrapperStyle.webkitTransitionDuration=this.options.fixedBadAndorid&&t.os.isBadAndroid?"0.001s":"0ms",this.wrapperStyle.opacity="0")};d.prototype={handleEvent:function(t){},transitionTime:function(e){e=e||0,this.indicatorStyle.webkitTransitionDuration=e+"ms",this.scroller.options.fixedBadAndorid&&!e&&t.os.isBadAndroid&&(this.indicatorStyle.webkitTransitionDuration="0.001s")},transitionTimingFunction:function(t){this.indicatorStyle.webkitTransitionTimingFunction=t},refresh:function(){this.transitionTime(),this.options.listenX&&!this.options.listenY?this.indicatorStyle.display=this.scroller.hasHorizontalScroll?"block":"none":this.options.listenY&&!this.options.listenX?this.indicatorStyle.display=this.scroller.hasVerticalScroll?"block":"none":this.indicatorStyle.display=this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none",this.wrapper.offsetHeight,this.options.listenX&&(this.wrapperWidth=this.wrapper.clientWidth,this.indicatorWidth=Math.max(Math.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8),this.indicatorStyle.width=this.indicatorWidth+"px",this.maxPosX=this.wrapperWidth-this.indicatorWidth,this.minBoundaryX=0,this.maxBoundaryX=this.maxPosX,this.sizeRatioX=this.options.speedRatioX||this.scroller.maxScrollX&&this.maxPosX/this.scroller.maxScrollX),this.options.listenY&&(this.wrapperHeight=this.wrapper.clientHeight,this.indicatorHeight=Math.max(Math.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8),this.indicatorStyle.height=this.indicatorHeight+"px",this.maxPosY=this.wrapperHeight-this.indicatorHeight,this.minBoundaryY=0,this.maxBoundaryY=this.maxPosY,this.sizeRatioY=this.options.speedRatioY||this.scroller.maxScrollY&&this.maxPosY/this.scroller.maxScrollY),this.updatePosition()},updatePosition:function(){var t=this.options.listenX&&Math.round(this.sizeRatioX*this.scroller.x)||0,e=this.options.listenY&&Math.round(this.sizeRatioY*this.scroller.y)||0;t<this.minBoundaryX?(this.width=Math.max(this.indicatorWidth+t,8),this.indicatorStyle.width=this.width+"px",t=this.minBoundaryX):t>this.maxBoundaryX?(this.width=Math.max(this.indicatorWidth-(t-this.maxPosX),8),this.indicatorStyle.width=this.width+"px",t=this.maxPosX+this.indicatorWidth-this.width):this.width!=this.indicatorWidth&&(this.width=this.indicatorWidth,this.indicatorStyle.width=this.width+"px"),e<this.minBoundaryY?(this.height=Math.max(this.indicatorHeight+3*e,8),this.indicatorStyle.height=this.height+"px",e=this.minBoundaryY):e>this.maxBoundaryY?(this.height=Math.max(this.indicatorHeight-3*(e-this.maxPosY),8),this.indicatorStyle.height=this.height+"px",e=this.maxPosY+this.indicatorHeight-this.height):this.height!=this.indicatorHeight&&(this.height=this.indicatorHeight,this.indicatorStyle.height=this.height+"px"),this.x=t,this.y=e,this.indicatorStyle.webkitTransform=this.scroller._getTranslateStr(t,e)},fade:function(t,e){if(!e||this.visible){clearTimeout(this.fadeTimeout),this.fadeTimeout=null;var i=t?250:500,s=t?0:300;t=t?"1":"0",this.wrapperStyle.webkitTransitionDuration=i+"ms",this.fadeTimeout=setTimeout(function(t){this.wrapperStyle.opacity=t,this.visible=+t}.bind(this,t),s)}}},t.Scroll=u,t.fn.scroll=function(e){var i=[];return this.each(function(){var s=null,n=this,o=n.getAttribute("data-scroll");if(o)s=t.data[o];else{o=++t.uuid;var r=t.extend({},e);n.classList.contains("mui-segmented-control")&&(r=t.extend(r,{scrollY:!1,scrollX:!0,indicators:!1,snap:".mui-control-item"})),t.data[o]=s=new u(n,r),n.setAttribute("data-scroll",o)}i.push(s)}),1===i.length?i[0]:i}}(mui,window,document),function(t,e,i,s){var n="mui-visibility",o="mui-hidden",r=t.Scroll.extend(t.extend({handleEvent:function(t){this._super(t),"scrollbottom"===t.type&&t.target===this.scroller&&this._scrollbottom()},_scrollbottom:function(){this.pulldown||this.loading||(this.pulldown=!1,this._initPullupRefresh(),this.pullupLoading())},_start:function(t){t.touches&&t.touches.length&&t.touches[0].clientX>30&&t.target&&!this._preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault(),this.loading||(this.pulldown=this.pullPocket=this.pullCaption=this.pullLoading=!1),this._super(t)},_drag:function(t){this._super(t),!this.pulldown&&!this.loading&&this.topPocket&&"down"===t.detail.direction&&this.y>=0&&this._initPulldownRefresh(),this.pulldown&&this._setCaption(this.y>this.options.down.height?this.options.down.contentover:this.options.down.contentdown)},_reLayout:function(){this.hasVerticalScroll=!0,this._super()},resetPosition:function(t){if(this.pulldown){if(this.y>=this.options.down.height)return this.pulldownLoading(s,t||0),!0;!this.loading&&this.topPocket.classList.remove(n)}return this._super(t)},pulldownLoading:function(t,e){if("undefined"==typeof t&&(t=this.options.down.height),this.scrollTo(0,t,e,this.options.bounceEasing),!this.loading){this._initPulldownRefresh(),this._setCaption(this.options.down.contentrefresh),this.loading=!0,this.indicators.map(function(t){t.fade(0)});var i=this.options.down.callback;i&&i.call(this)}},endPulldownToRefresh:function(){var t=this;t.topPocket&&t.loading&&this.pulldown&&(t.scrollTo(0,0,t.options.bounceTime,t.options.bounceEasing),t.loading=!1,t._setCaption(t.options.down.contentdown,!0),setTimeout(function(){t.loading||t.topPocket.classList.remove(n)},350))},pullupLoading:function(t,e,i){e=e||0,this.scrollTo(e,this.maxScrollY,i,this.options.bounceEasing),this.loading||(this._initPullupRefresh(),this._setCaption(this.options.up.contentrefresh),this.indicators.map(function(t){t.fade(0)}),this.loading=!0,t=t||this.options.up.callback,t&&t.call(this))},endPullupToRefresh:function(t){var e=this;e.bottomPocket&&(e.loading=!1,t?(this.finished=!0,e._setCaption(e.options.up.contentnomore),e.wrapper.removeEventListener("scrollbottom",e)):(e._setCaption(e.options.up.contentdown),e.loading||e.bottomPocket.classList.remove(n)))},disablePullupToRefresh:function(){this._initPullupRefresh(),this.bottomPocket.className="mui-pull-bottom-pocket "+o,this.wrapper.removeEventListener("scrollbottom",this)},enablePullupToRefresh:function(){this._initPullupRefresh(),this.bottomPocket.classList.remove(o),this._setCaption(this.options.up.contentdown),this.wrapper.addEventListener("scrollbottom",this)},refresh:function(t){t&&this.finished&&(this.enablePullupToRefresh(),this.finished=!1),this._super()}},t.PullRefresh));t.fn.pullRefresh=function(e){if(1===this.length){var i=this[0],s=null;e=e||{};var n=i.getAttribute("data-pullrefresh");return n?s=t.data[n]:(n=++t.uuid,t.data[n]=s=new r(i,e),i.setAttribute("data-pullrefresh",n)),e.down&&e.down.auto?s.pulldownLoading(e.down.autoY):e.up&&e.up.auto&&s.pullupLoading(),s}}}(mui,window,document),function(t,e){var i="mui-slider",s="mui-slider-group",n="mui-slider-loop",o="mui-action-previous",r="mui-action-next",a="mui-slider-item",l="mui-active",c="."+a,h=".mui-slider-progress-bar",u=t.Slider=t.Scroll.extend({init:function(e,i){this._super(e,t.extend(!0,{fingers:1,interval:0,scrollY:!1,scrollX:!0,indicators:!1,scrollTime:1e3,startX:!1,slideTime:0,snap:c},i)),this.options.startX},_init:function(){this._reInit(),this.scroller&&(this.scrollerStyle=this.scroller.style,this.progressBar=this.wrapper.querySelector(h),this.progressBar&&(this.progressBarWidth=this.progressBar.offsetWidth,this.progressBarStyle=this.progressBar.style),this._super(),this._initTimer())},_triggerSlide:function(){var e=this;e.isInTransition=!1,e.currentPage,e.slideNumber=e._fixedSlideNumber(),e.loop&&(0===e.slideNumber?e.setTranslate(e.pages[1][0].x,0):e.slideNumber===e.itemLength-3&&e.setTranslate(e.pages[e.itemLength-2][0].x,0)),e.lastSlideNumber!=e.slideNumber&&(e.lastSlideNumber=e.slideNumber,e.lastPage=e.currentPage,t.trigger(e.wrapper,"slide",{slideNumber:e.slideNumber})),e._initTimer()},_handleSlide:function(e){var i=this;if(e.target===i.wrapper){var s=e.detail;s.slideNumber=s.slideNumber||0;for(var n=i.scroller.querySelectorAll(c),o=[],r=0,a=n.length;a>r;r++){var h=n[r];h.parentNode===i.scroller&&o.push(h)}var u=s.slideNumber;if(i.loop&&(u+=1),!i.wrapper.classList.contains("mui-segmented-control"))for(var r=0,a=o.length;a>r;r++){var h=o[r];h.parentNode===i.scroller&&(r===u?h.classList.add(l):h.classList.remove(l))}var d=i.wrapper.querySelector(".mui-slider-indicator");if(d){d.getAttribute("data-scroll")&&t(d).scroll().gotoPage(s.slideNumber);var p=d.querySelectorAll(".mui-indicator");if(p.length>0)for(var r=0,a=p.length;a>r;r++)p[r].classList[r===s.slideNumber?"add":"remove"](l);else{var f=d.querySelector(".mui-number span");if(f)f.innerText=s.slideNumber+1;else for(var g=d.querySelectorAll(".mui-control-item"),r=0,a=g.length;a>r;r++)g[r].classList[r===s.slideNumber?"add":"remove"](l)}}e.stopPropagation()}},_handleTabShow:function(t){var e=this;e.gotoItem(t.detail.tabNumber||0,e.options.slideTime)},_handleIndicatorTap:function(t){var e=this,i=t.target;(i.classList.contains(o)||i.classList.contains(r))&&(e[i.classList.contains(o)?"prevItem":"nextItem"](),t.stopPropagation())},_initEvent:function(e){var i=this;i._super(e);var s=e?"removeEventListener":"addEventListener";i.wrapper[s]("slide",this),i.wrapper[s](t.eventName("shown","tab"),this)},handleEvent:function(e){switch(this._super(e),e.type){case"slide":this._handleSlide(e);break;case t.eventName("shown","tab"):~this.snaps.indexOf(e.target)&&this._handleTabShow(e)}},_scrollend:function(t){this._super(t),this._triggerSlide(t)},_drag:function(t){this._super(t);var i=t.detail.direction;if("left"===i||"right"===i){var s=this.wrapper.getAttribute("data-slidershowTimer");s&&e.clearTimeout(s),t.stopPropagation()}},_initTimer:function(){var t=this,i=t.wrapper,s=t.options.interval,n=i.getAttribute("data-slidershowTimer");n&&e.clearTimeout(n),s&&(n=e.setTimeout(function(){i&&((i.offsetWidth||i.offsetHeight)&&t.nextItem(!0),t._initTimer())},s),i.setAttribute("data-slidershowTimer",n))},_fixedSlideNumber:function(t){t=t||this.currentPage;var e=t.pageX;return this.loop&&(e=0===t.pageX?this.itemLength-3:t.pageX===this.itemLength-1?0:t.pageX-1),
e},_reLayout:function(){this.hasHorizontalScroll=!0,this.loop=this.scroller.classList.contains(n),this._super()},_getScroll:function(){var e=t.parseTranslateMatrix(t.getStyles(this.scroller,"webkitTransform"));return e?e.x:0},_transitionEnd:function(e){e.target===this.scroller&&this.isInTransition&&(this._transitionTime(),this.isInTransition=!1,t.trigger(this.wrapper,"scrollend",this))},_flick:function(t){if(this.moved){var e=t.detail,i=e.direction;this._clearRequestAnimationFrame(),this.isInTransition=!0,"flick"===t.type?(e.deltaTime<200&&(this.x=this._getPage(this.slideNumber+("right"===i?-1:1),!0).x),this.resetPosition(this.options.bounceTime)):"dragend"!==t.type||e.flick||this.resetPosition(this.options.bounceTime),t.stopPropagation()}},_initSnap:function(){if(this.scrollerWidth=this.itemLength*this.scrollerWidth,this.maxScrollX=Math.min(this.wrapperWidth-this.scrollerWidth,0),this._super(),this.currentPage.x)this.slideNumber=this._fixedSlideNumber(),this.lastSlideNumber="undefined"==typeof this.lastSlideNumber?this.slideNumber:this.lastSlideNumber;else{var t=this.pages[this.loop?1:0];if(t=t||this.pages[0],!t)return;this.currentPage=t[0],this.slideNumber=0,this.lastSlideNumber="undefined"==typeof this.lastSlideNumber?0:this.lastSlideNumber}this.options.startX=this.currentPage.x||0},_getSnapX:function(t){return Math.max(-t,this.maxScrollX)},_getPage:function(t,e){return this.loop?t>this.itemLength-(e?2:3)?(t=1,time=0):(e?-1:0)>t?(t=this.itemLength-2,time=0):t+=1:(e||(t>this.itemLength-1?(t=0,time=0):0>t&&(t=this.itemLength-1,time=0)),t=Math.min(Math.max(0,t),this.itemLength-1)),this.pages[t][0]},_gotoItem:function(e,i){this.currentPage=this._getPage(e,!0),this.scrollTo(this.currentPage.x,0,i,this.options.scrollEasing),0===i&&t.trigger(this.wrapper,"scrollend",this)},setTranslate:function(t,e){this._super(t,e);var i=this.progressBar;i&&(this.progressBarStyle.webkitTransform=this._getTranslateStr(-t*(this.progressBarWidth/this.wrapperWidth),0))},resetPosition:function(t){return t=t||0,this.x>0?this.x=0:this.x<this.maxScrollX&&(this.x=this.maxScrollX),this.currentPage=this._nearestSnap(this.x),this.scrollTo(this.currentPage.x,0,t,this.options.scrollEasing),!0},gotoItem:function(t,e){this._gotoItem(t,"undefined"==typeof e?this.options.scrollTime:e)},nextItem:function(){this._gotoItem(this.slideNumber+1,this.options.scrollTime)},prevItem:function(){this._gotoItem(this.slideNumber-1,this.options.scrollTime)},getSlideNumber:function(){return this.slideNumber||0},_reInit:function(){for(var t=this.wrapper.querySelectorAll("."+s),e=0,i=t.length;i>e;e++)if(t[e].parentNode===this.wrapper){this.scroller=t[e];break}this.scrollerStyle=this.scroller&&this.scroller.style,this.progressBar&&(this.progressBarWidth=this.progressBar.offsetWidth,this.progressBarStyle=this.progressBar.style)},refresh:function(e){e?(t.extend(this.options,e),this._super(),this._initTimer()):this._super()},destroy:function(){this._initEvent(!0),delete t.data[this.wrapper.getAttribute("data-slider")],this.wrapper.setAttribute("data-slider","")}});t.fn.slider=function(e){var s=null;return this.each(function(){var n=this;if(this.classList.contains(i)||(n=this.querySelector("."+i)),n&&n.querySelector(c)){var o=n.getAttribute("data-slider");o?(s=t.data[o],s&&e&&s.refresh(e)):(o=++t.uuid,t.data[o]=s=new u(n,e),n.setAttribute("data-slider",o))}}),s},t.ready(function(){t(".mui-slider").slider(),t(".mui-scroll-wrapper.mui-slider-indicator.mui-segmented-control").scroll({scrollY:!1,scrollX:!0,indicators:!1,snap:".mui-control-item"})})}(mui,window),function(t,e){if(t.os.plus&&t.os.android){var i="mui-plus-pullrefresh",s="mui-visibility",n="mui-hidden",o="mui-block",r="mui-pull-caption",a="mui-pull-caption-down",l="mui-pull-caption-refresh",c="mui-pull-caption-nomore",h=t.Class.extend({init:function(t,e){this.element=t,this.options=e,this.wrapper=this.scroller=t,this._init(),this._initPulldownRefreshEvent()},_init:function(){var t=this;window.addEventListener("dragup",t),e.addEventListener("plusscrollbottom",t),t.scrollInterval=window.setInterval(function(){t.isScroll&&!t.loading&&window.pageYOffset+window.innerHeight+10>=e.documentElement.scrollHeight&&(t.isScroll=!1,t.bottomPocket&&t.pullupLoading())},100)},_initPulldownRefreshEvent:function(){var e=this;e.topPocket&&e.options.webviewId&&t.plusReady(function(){var t=plus.webview.getWebviewById(e.options.webviewId);if(t){e.options.webview=t;var i=e.options.down,s=i.height;t.addEventListener("dragBounce",function(s){switch(e.pulldown?e.pullPocket.classList.add(o):e._initPulldownRefresh(),s.status){case"beforeChangeOffset":e._setCaption(i.contentdown);break;case"afterChangeOffset":e._setCaption(i.contentover);break;case"dragEndAfterChangeOffset":t.evalJS("mui&&mui.options.pullRefresh.down.callback()"),e._setCaption(i.contentrefresh)}},!1),t.setBounce({position:{top:2*s+"px"},changeoffset:{top:s+"px"}})}})},handleEvent:function(t){var e=this;e.stopped||(e.isScroll=!1,("dragup"===t.type||"plusscrollbottom"===t.type)&&(e.isScroll=!0,setTimeout(function(){e.isScroll=!1},1e3)))}}).extend(t.extend({setStopped:function(t){this.stopped=!!t;var e=plus.webview.currentWebview();if(this.stopped)e.setStyle({bounce:"none"}),e.setBounce({position:{top:"none"}});else{var i=this.options.down.height;e.setStyle({bounce:"vertical"}),e.setBounce({position:{top:2*i+"px"},changeoffset:{top:i+"px"}})}},pulldownLoading:function(){t.plusReady(function(){plus.webview.currentWebview().setBounce({offset:{top:this.options.down.height+"px"}})}.bind(this))},endPulldownToRefresh:function(){var t=plus.webview.currentWebview();t.parent().evalJS("mui&&mui(document.querySelector('.mui-content')).pullRefresh('"+JSON.stringify({webviewId:t.id})+"')._endPulldownToRefresh()")},_endPulldownToRefresh:function(){var t=this;t.topPocket&&t.options.webview&&(t.options.webview.endPullToRefresh(),t.loading=!1,t._setCaption(t.options.down.contentdown,!0),setTimeout(function(){t.loading||t.topPocket.classList.remove(o)},350))},pullupLoading:function(t){var e=this;e.isLoading||(e.isLoading=!0,e.pulldown!==!1?e._initPullupRefresh():this.pullPocket.classList.add(o),setTimeout(function(){e.pullLoading.classList.add(s),e.pullLoading.classList.remove(n),e.pullCaption.innerHTML="",e.pullCaption.className=r+" "+l,e.pullCaption.innerHTML=e.options.up.contentrefresh,t=t||e.options.up.callback,t&&t.call(e)},300))},endPullupToRefresh:function(t){var i=this;i.pullLoading&&(i.pullLoading.classList.remove(s),i.pullLoading.classList.add(n),i.isLoading=!1,t?(i.finished=!0,i.pullCaption.className=r+" "+c,i.pullCaption.innerHTML=i.options.up.contentnomore,e.removeEventListener("plusscrollbottom",i),window.removeEventListener("dragup",i)):(i.pullCaption.className=r+" "+a,i.pullCaption.innerHTML=i.options.up.contentdown))},disablePullupToRefresh:function(){this._initPullupRefresh(),this.bottomPocket.className="mui-pull-bottom-pocket "+n,window.removeEventListener("dragup",this)},enablePullupToRefresh:function(){this._initPullupRefresh(),this.bottomPocket.classList.remove(n),this.pullCaption.className=r+" "+a,this.pullCaption.innerHTML=this.options.up.contentdown,e.addEventListener("plusscrollbottom",this),window.addEventListener("dragup",this)},scrollTo:function(e,i,s){t.scrollTo(i,s)},refresh:function(t){t&&this.finished&&(this.enablePullupToRefresh(),this.finished=!1)}},t.PullRefresh));t.fn.pullRefresh=function(s){var n;0===this.length?(n=e.createElement("div"),n.className="mui-content",e.body.appendChild(n)):n=this[0],s=s||{},"string"==typeof s&&(s=t.parseJSON(s)),!s.webviewId&&(s.webviewId=plus.webview.currentWebview().id||plus.webview.currentWebview().getURL());var o=null,r=s.webviewId&&s.webviewId.replace(/\//g,"_"),a=n.getAttribute("data-pullrefresh-plus-"+r);return a?o=t.data[a]:(a=++t.uuid,n.setAttribute("data-pullrefresh-plus-"+r,a),e.body.classList.add(i),t.data[a]=o=new h(n,s)),s.down&&s.down.auto?o.pulldownLoading():s.up&&s.up.auto&&o.pullupLoading(),o}}}(mui,document),function(t,e,i,s){var n="mui-off-canvas-left",o="mui-off-canvas-right",r="mui-off-canvas-backdrop",a="mui-off-canvas-wrap",l="mui-slide-in",c="mui-active",h="mui-transitioning",u=".mui-inner-wrap",d=t.Class.extend({init:function(e,s){this.wrapper=this.element=e,this.scroller=this.wrapper.querySelector(u),this.classList=this.wrapper.classList,this.scroller&&(this.options=t.extend(!0,{dragThresholdX:10,scale:.8,opacity:.1,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|VIDEO)$/}},s),i.body.classList.add("mui-fullscreen"),this.refresh(),this.initEvent())},_preventDefaultException:function(t,e){for(var i in e)if(e[i].test(t[i]))return!0;return!1},refresh:function(t){this.slideIn=this.classList.contains(l),this.scalable=this.classList.contains("mui-scalable")&&!this.slideIn,this.scroller=this.wrapper.querySelector(u),this.offCanvasLefts=this.wrapper.querySelectorAll("."+n),this.offCanvasRights=this.wrapper.querySelectorAll("."+o),t?t.classList.contains(n)?this.offCanvasLeft=t:t.classList.contains(o)&&(this.offCanvasRight=t):(this.offCanvasRight=this.wrapper.querySelector("."+o),this.offCanvasLeft=this.wrapper.querySelector("."+n)),this.offCanvasRightWidth=this.offCanvasLeftWidth=0,this.offCanvasLeftSlideIn=this.offCanvasRightSlideIn=!1,this.offCanvasRight&&(this.offCanvasRightWidth=this.offCanvasRight.offsetWidth,this.offCanvasRightSlideIn=this.slideIn&&this.offCanvasRight.parentNode===this.wrapper),this.offCanvasLeft&&(this.offCanvasLeftWidth=this.offCanvasLeft.offsetWidth,this.offCanvasLeftSlideIn=this.slideIn&&this.offCanvasLeft.parentNode===this.wrapper),this.backdrop=this.scroller.querySelector("."+r),this.options.dragThresholdX=this.options.dragThresholdX||10,this.visible=!1,this.startX=null,this.lastX=null,this.offsetX=null,this.lastTranslateX=null},handleEvent:function(e){switch(e.type){case t.EVENT_START:e.target&&!this._preventDefaultException(e.target,this.options.preventDefaultException)&&e.preventDefault();break;case"webkitTransitionEnd":e.target===this.scroller&&this._dispatchEvent();break;case"drag":var i=e.detail;this.startX?this.lastX=i.center.x:(this.startX=i.center.x,this.lastX=this.startX),!this.isDragging&&Math.abs(this.lastX-this.startX)>this.options.dragThresholdX&&("left"===i.direction||"right"===i.direction)&&(this.slideIn?(this.scroller=this.wrapper.querySelector(u),this.classList.contains(c)?this.offCanvasRight&&this.offCanvasRight.classList.contains(c)?(this.offCanvas=this.offCanvasRight,this.offCanvasWidth=this.offCanvasRightWidth):(this.offCanvas=this.offCanvasLeft,this.offCanvasWidth=this.offCanvasLeftWidth):"left"===i.direction&&this.offCanvasRight?(this.offCanvas=this.offCanvasRight,this.offCanvasWidth=this.offCanvasRightWidth):"right"===i.direction&&this.offCanvasLeft?(this.offCanvas=this.offCanvasLeft,this.offCanvasWidth=this.offCanvasLeftWidth):this.scroller=null):this.classList.contains(c)?"left"===i.direction?(this.offCanvas=this.offCanvasLeft,this.offCanvasWidth=this.offCanvasLeftWidth):(this.offCanvas=this.offCanvasRight,this.offCanvasWidth=this.offCanvasRightWidth):"right"===i.direction?(this.offCanvas=this.offCanvasLeft,this.offCanvasWidth=this.offCanvasLeftWidth):(this.offCanvas=this.offCanvasRight,this.offCanvasWidth=this.offCanvasRightWidth),this.offCanvas&&this.scroller&&(this.startX=this.lastX,this.isDragging=!0,t.gestures.session.lockDirection=!0,t.gestures.session.startDirection=i.direction,this.offCanvas.classList.remove(h),this.scroller.classList.remove(h),this.offsetX=this.getTranslateX(),this._initOffCanvasVisible())),this.isDragging&&(this.updateTranslate(this.offsetX+(this.lastX-this.startX)),i.gesture.preventDefault(),e.stopPropagation());break;case"dragend":if(this.isDragging){var i=e.detail,s=i.direction;this.isDragging=!1,this.offCanvas.classList.add(h),this.scroller.classList.add(h);var n=0,o=this.getTranslateX();if(this.slideIn){if(n=o>=0?this.offCanvasRightWidth&&o/this.offCanvasRightWidth||0:this.offCanvasLeftWidth&&o/this.offCanvasLeftWidth||0,"right"===s&&0>=n&&(n>=-.5||i.swipe)?this.openPercentage(100):"right"===s&&n>0&&(n>=.5||i.swipe)?this.openPercentage(0):"right"===s&&-.5>=n?this.openPercentage(0):"right"===s&&n>0&&.5>=n?this.openPercentage(-100):"left"===s&&n>=0&&(.5>=n||i.swipe)?this.openPercentage(-100):"left"===s&&0>n&&(-.5>=n||i.swipe)?this.openPercentage(0):"left"===s&&n>=.5?this.openPercentage(0):"left"===s&&n>=-.5&&0>n?this.openPercentage(100):this.openPercentage(0),1===n||-1===n||0===n)return void this._dispatchEvent()}else{if(n=o>=0?this.offCanvasLeftWidth&&o/this.offCanvasLeftWidth||0:this.offCanvasRightWidth&&o/this.offCanvasRightWidth||0,0===n)return this.openPercentage(0),void this._dispatchEvent();"right"===s&&n>=0&&(n>=.5||i.swipe)?this.openPercentage(100):"right"===s&&0>n&&(n>-.5||i.swipe)?this.openPercentage(0):"right"===s&&n>0&&.5>n?this.openPercentage(0):"right"===s&&.5>n?this.openPercentage(-100):"left"===s&&0>=n&&(-.5>=n||i.swipe)?this.openPercentage(-100):"left"===s&&n>0&&(.5>=n||i.swipe)?this.openPercentage(0):"left"===s&&0>n&&n>=-.5?this.openPercentage(0):"left"===s&&n>.5?this.openPercentage(100):this.openPercentage(0),(1===n||-1===n)&&this._dispatchEvent()}}}},_dispatchEvent:function(){this.classList.contains(c)?t.trigger(this.wrapper,"shown",this):t.trigger(this.wrapper,"hidden",this)},_initOffCanvasVisible:function(){this.visible||(this.visible=!0,this.offCanvasLeft&&(this.offCanvasLeft.style.visibility="visible"),this.offCanvasRight&&(this.offCanvasRight.style.visibility="visible"))},initEvent:function(){var e=this;e.backdrop&&e.backdrop.addEventListener("tap",function(t){e.close(),t.detail.gesture.preventDefault()}),this.classList.contains("mui-draggable")&&(this.wrapper.addEventListener(t.EVENT_START,this),this.wrapper.addEventListener("drag",this),this.wrapper.addEventListener("dragend",this)),this.wrapper.addEventListener("webkitTransitionEnd",this)},openPercentage:function(t){var e=t/100;this.slideIn?(this.offCanvasLeft&&t>=0?(e=0===e?-1:0,this.updateTranslate(this.offCanvasLeftWidth*e),this.offCanvasLeft.classList[0!==t?"add":"remove"](c)):this.offCanvasRight&&0>=t&&(e=0===e?1:0,this.updateTranslate(this.offCanvasRightWidth*e),this.offCanvasRight.classList[0!==t?"add":"remove"](c)),this.classList[0!==t?"add":"remove"](c)):(this.offCanvasLeft&&t>=0?(this.updateTranslate(this.offCanvasLeftWidth*e),this.offCanvasLeft.classList[0!==e?"add":"remove"](c)):this.offCanvasRight&&0>=t&&(this.updateTranslate(this.offCanvasRightWidth*e),this.offCanvasRight.classList[0!==e?"add":"remove"](c)),this.classList[0!==e?"add":"remove"](c))},updateTranslate:function(e){if(e!==this.lastTranslateX){if(this.slideIn){if(this.offCanvas.classList.contains(o)){if(0>e)return void this.setTranslateX(0);if(e>this.offCanvasRightWidth)return void this.setTranslateX(this.offCanvasRightWidth)}else{if(e>0)return void this.setTranslateX(0);if(e<-this.offCanvasLeftWidth)return void this.setTranslateX(-this.offCanvasLeftWidth)}this.setTranslateX(e)}else{if(!this.offCanvasLeft&&e>0||!this.offCanvasRight&&0>e)return void this.setTranslateX(0);if(this.leftShowing&&e>this.offCanvasLeftWidth)return void this.setTranslateX(this.offCanvasLeftWidth);if(this.rightShowing&&e<-this.offCanvasRightWidth)return void this.setTranslateX(-this.offCanvasRightWidth);this.setTranslateX(e),e>=0?(this.leftShowing=!0,this.rightShowing=!1,e>0&&(this.offCanvasLeft&&t.each(this.offCanvasLefts,function(t,e){e===this.offCanvasLeft?this.offCanvasLeft.style.zIndex=0:e.style.zIndex=-1}.bind(this)),this.offCanvasRight&&(this.offCanvasRight.style.zIndex=-1))):(this.rightShowing=!0,this.leftShowing=!1,this.offCanvasRight&&t.each(this.offCanvasRights,function(t,e){e===this.offCanvasRight?e.style.zIndex=0:e.style.zIndex=-1}.bind(this)),this.offCanvasLeft&&(this.offCanvasLeft.style.zIndex=-1))}this.lastTranslateX=e}},setTranslateX:t.animationFrame(function(t){if(this.scroller)if(this.scalable&&this.offCanvas.parentNode===this.wrapper){var e=Math.abs(t)/this.offCanvasWidth,i=1-(1-this.options.scale)*e,s=this.options.scale+(1-this.options.scale)*e,o=(1-(1-this.options.opacity)*e,this.options.opacity+(1-this.options.opacity)*e);this.offCanvas.classList.contains(n)?(this.offCanvas.style.webkitTransformOrigin="-100%",this.scroller.style.webkitTransformOrigin="left"):(this.offCanvas.style.webkitTransformOrigin="200%",this.scroller.style.webkitTransformOrigin="right"),this.offCanvas.style.opacity=o,this.offCanvas.style.webkitTransform="translate3d(0,0,0) scale("+s+")",this.scroller.style.webkitTransform="translate3d("+t+"px,0,0) scale("+i+")"}else this.slideIn?this.offCanvas.style.webkitTransform="translate3d("+t+"px,0,0)":this.scroller.style.webkitTransform="translate3d("+t+"px,0,0)"}),getTranslateX:function(){if(this.offCanvas){var e=this.slideIn?this.offCanvas:this.scroller,i=t.parseTranslateMatrix(t.getStyles(e,"webkitTransform"));return i&&i.x||0}return 0},isShown:function(t){var e=!1;if(this.slideIn)e="left"===t?this.classList.contains(c)&&this.wrapper.querySelector("."+n+"."+c):"right"===t?this.classList.contains(c)&&this.wrapper.querySelector("."+o+"."+c):this.classList.contains(c)&&(this.wrapper.querySelector("."+n+"."+c)||this.wrapper.querySelector("."+o+"."+c));else{var i=this.getTranslateX();e="right"===t?this.classList.contains(c)&&0>i:"left"===t?this.classList.contains(c)&&i>0:this.classList.contains(c)&&0!==i}return e},close:function(){this._initOffCanvasVisible(),this.offCanvas=this.wrapper.querySelector("."+o+"."+c)||this.wrapper.querySelector("."+n+"."+c),this.offCanvasWidth=this.offCanvas.offsetWidth,this.scroller&&(this.offCanvas.offsetHeight,this.offCanvas.classList.add(h),this.scroller.classList.add(h),this.openPercentage(0))},show:function(t){return this._initOffCanvasVisible(),!this.isShown(t)&&(t||(t=this.wrapper.querySelector("."+o)?"right":"left"),"right"===t?(this.offCanvas=this.offCanvasRight,this.offCanvasWidth=this.offCanvasRightWidth):(this.offCanvas=this.offCanvasLeft,this.offCanvasWidth=this.offCanvasLeftWidth),this.scroller&&(this.offCanvas.offsetHeight,this.offCanvas.classList.add(h),this.scroller.classList.add(h),this.openPercentage("left"===t?100:-100)),!0)},toggle:function(t){var e=t;t&&t.classList&&(e=t.classList.contains(n)?"left":"right",this.refresh(t)),this.show(e)||this.close()}}),p=function(t){if(parentNode=t.parentNode,parentNode){if(parentNode.classList.contains(a))return parentNode;if(parentNode=parentNode.parentNode,parentNode.classList.contains(a))return parentNode}},f=function(e,s){if("A"===s.tagName&&s.hash){var n=i.getElementById(s.hash.replace("#",""));if(n){var o=p(n);if(o)return t.targets._container=o,n}}return!1};t.registerTarget({name:s,index:60,handle:f,target:!1,isReset:!1,isContinue:!0}),e.addEventListener("tap",function(e){if(t.targets.offcanvas)for(var s=e.target;s&&s!==i;s=s.parentNode)if("A"===s.tagName&&s.hash&&s.hash==="#"+t.targets.offcanvas.id){e.detail&&e.detail.gesture&&e.detail.gesture.preventDefault(),t(t.targets._container).offCanvas().toggle(t.targets.offcanvas),t.targets.offcanvas=t.targets._container=null;break}}),t.fn.offCanvas=function(e){var i=[];return this.each(function(){var s=null,n=this;n.classList.contains(a)||(n=p(n));var o=n.getAttribute("data-offCanvas");o?s=t.data[o]:(o=++t.uuid,t.data[o]=s=new d(n,e),n.setAttribute("data-offCanvas",o)),("show"===e||"close"===e||"toggle"===e)&&s.toggle(),i.push(s)}),1===i.length?i[0]:i},t.ready(function(){t(".mui-off-canvas-wrap").offCanvas()})}(mui,window,document,"offcanvas"),function(t,e){var i="mui-action",s=function(t,e){var s=e.className||"";return"string"!=typeof s&&(s=""),!(!s||!~s.indexOf(i))&&(e.classList.contains("mui-action-back")&&t.preventDefault(),e)};t.registerTarget({name:e,index:50,handle:s,target:!1,isContinue:!0})}(mui,"action"),function(t,e,i,s){var n="mui-modal",o=function(t,e){if("A"===e.tagName&&e.hash){var s=i.getElementById(e.hash.replace("#",""));if(s&&s.classList.contains(n))return s}return!1};t.registerTarget({name:s,index:50,handle:o,target:!1,isReset:!1,isContinue:!0}),e.addEventListener("tap",function(e){t.targets.modal&&(e.detail.gesture.preventDefault(),t.targets.modal.classList.toggle("mui-active"))})}(mui,window,document,"modal"),function(t,e,i,s){var n="mui-popover",o="mui-popover-arrow",r="mui-popover-action",a="mui-backdrop",l="mui-bar-popover",c="mui-bar-backdrop",h="mui-backdrop-action",u="mui-active",d="mui-bottom",p=function(e,s){if("A"===s.tagName&&s.hash){if(t.targets._popover=i.getElementById(s.hash.replace("#","")),t.targets._popover&&t.targets._popover.classList.contains(n))return s;t.targets._popover=null}return!1};t.registerTarget({name:s,index:60,handle:p,target:!1,isReset:!1,isContinue:!0});var f,g=function(t){},m=function(e){this.removeEventListener("webkitTransitionEnd",m),this.addEventListener(t.EVENT_MOVE,t.preventDefault),t.trigger(this,"shown",this)},v=function(e){L(this,"none"),this.removeEventListener("webkitTransitionEnd",v),this.removeEventListener(t.EVENT_MOVE,t.preventDefault),g(!1),t.trigger(this,"hidden",this)},w=function(){var e=i.createElement("div");return e.classList.add(a),e.addEventListener(t.EVENT_MOVE,t.preventDefault),e.addEventListener("tap",function(e){var s=t.targets._popover;s&&(s.addEventListener("webkitTransitionEnd",v),s.classList.remove(u),b(s),i.body.setAttribute("style",""))}),e}(),b=function(e){w.setAttribute("style","opacity:0"),t.targets.popover=t.targets._popover=null,f=t.later(function(){!e.classList.contains(u)&&w.parentNode&&w.parentNode===i.body&&i.body.removeChild(w)},350)};e.addEventListener("tap",function(e){if(t.targets.popover){for(var s=!1,n=e.target;n&&n!==i;n=n.parentNode)n===t.targets.popover&&(s=!0);s&&(e.detail.gesture.preventDefault(),y(t.targets._popover,t.targets.popover))}});var y=function(t,e,s){if(!("show"===s&&t.classList.contains(u)||"hide"===s&&!t.classList.contains(u))){f&&f.cancel(),t.removeEventListener("webkitTransitionEnd",m),t.removeEventListener("webkitTransitionEnd",v),w.classList.remove(c),w.classList.remove(h);var n=i.querySelector(".mui-popover.mui-active");if(n&&(n.addEventListener("webkitTransitionEnd",v),n.classList.remove(u),t===n))return void b(n);var o=!1;(t.classList.contains(l)||t.classList.contains(r))&&(t.classList.contains(r)?(o=!0,w.classList.add(h)):w.classList.add(c)),L(t,"block"),t.offsetHeight,t.classList.add(u),w.setAttribute("style",""),i.body.appendChild(w),g(!0),T(t,e,o),w.classList.add(u),t.addEventListener("webkitTransitionEnd",m)}},L=function(t,e,i,s){var n=t.style;"undefined"!=typeof e&&(n.display=e),"undefined"!=typeof i&&(n.top=i+"px"),"undefined"!=typeof s&&(n.left=s+"px")},T=function(s,n,a){if(s&&n){if(a)return void L(s,"block");var l=e.innerWidth,c=e.innerHeight,h=s.offsetWidth,u=s.offsetHeight,p=n.offsetWidth,f=n.offsetHeight,g=t.offset(n),m=s.querySelector("."+o);m||(m=i.createElement("div"),m.className=o,s.appendChild(m));var v=m&&m.offsetWidth/2||0,w=0,b=0,y=0,T=0,E=s.classList.contains(r)?0:5,x="top";u+v<g.top-e.pageYOffset?w=g.top-u-v:u+v<c-(g.top-e.pageYOffset)-f?(x="bottom",w=g.top+f+v):(x="middle",w=Math.max((c-u)/2+e.pageYOffset,0),b=Math.max((l-h)/2+e.pageXOffset,0)),"top"===x||"bottom"===x?(b=p/2+g.left-h/2,y=b,E>b&&(b=E),b+h>l&&(b=l-h-E),m&&("top"===x?m.classList.add(d):m.classList.remove(d),y-=b,T=h/2-v/2+y,T=Math.max(Math.min(T,h-2*v-6),6),m.setAttribute("style","left:"+T+"px"))):"middle"===x&&m.setAttribute("style","display:none"),L(s,"block",w,b)}};t.createMask=function(e){var s=i.createElement("div");s.classList.add(a),s.addEventListener(t.EVENT_MOVE,t.preventDefault),s.addEventListener("tap",function(){n.close()});var n=[s];return n._show=!1,n.show=function(){return n._show=!0,s.setAttribute("style","opacity:1"),i.body.appendChild(s),n},n._remove=function(){return n._show&&(n._show=!1,s.setAttribute("style","opacity:0"),t.later(function(){var t=i.body;s.parentNode===t&&t.removeChild(s)},350)),n},n.close=function(){e?e()!==!1&&n._remove():n._remove()},n},t.fn.popover=function(){var e=arguments;this.each(function(){t.targets._popover=this,("show"===e[0]||"hide"===e[0]||"toggle"===e[0])&&y(this,e[1],e[0])})}}(mui,window,document,"popover"),function(t,e,i,s,n){var o="mui-control-item",r="mui-segmented-control",a="mui-segmented-control-vertical",l="mui-control-content",c="mui-bar-tab",h="mui-tab-item",u=function(t,e){return!(!e.classList||!e.classList.contains(o)&&!e.classList.contains(h))&&(e.parentNode&&e.parentNode.classList&&e.parentNode.classList.contains(a)||t.preventDefault(),e)};t.registerTarget({name:s,index:80,handle:u,target:!1}),e.addEventListener("tap",function(e){var n=t.targets.tab;if(n){for(var a,u,d,p="mui-active",f="."+p,g=n.parentNode;g&&g!==i;g=g.parentNode){if(g.classList.contains(r)){a=g.querySelector(f+"."+o);break}g.classList.contains(c)&&(a=g.querySelector(f+"."+h))}a&&a.classList.remove(p);var m=n===a;if(n&&n.classList.add(p),n.hash&&(d=i.getElementById(n.hash.replace("#","")))){if(!d.classList.contains(l))return void n.classList[m?"remove":"add"](p);if(!m){var v=d.parentNode;u=v.querySelectorAll("."+l+f);for(var w=0;w<u.length;w++){var b=u[w];b.parentNode===v&&b.classList.remove(p)}d.classList.add(p);for(var y=[],L=v.querySelectorAll("."+l),w=0;w<L.length;w++)L[w].parentNode===v&&y.push(L[w]);t.trigger(d,t.eventName("shown",s),{tabNumber:Array.prototype.indexOf.call(y,d)}),e.detail&&e.detail.gesture.preventDefault()}}}})}(mui,window,document,"tab"),function(t,e,i){var s="mui-switch",n="mui-switch-handle",o="mui-active",r="mui-dragging",a="mui-disabled",l="."+n,c=function(t,e){return!(!e.classList||!e.classList.contains(s))&&e};t.registerTarget({name:i,index:100,handle:c,target:!1});var h=function(t){this.element=t,this.classList=this.element.classList,this.handle=this.element.querySelector(l),this.init(),this.initEvent()};h.prototype.init=function(){this.toggleWidth=this.element.offsetWidth,this.handleWidth=this.handle.offsetWidth,this.handleX=this.toggleWidth-this.handleWidth-3},h.prototype.initEvent=function(){this.element.addEventListener(t.EVENT_START,this),this.element.addEventListener("drag",this),this.element.addEventListener("swiperight",this),this.element.addEventListener(t.EVENT_END,this),this.element.addEventListener(t.EVENT_CANCEL,this)},h.prototype.handleEvent=function(e){if(!this.classList.contains(a))switch(e.type){case t.EVENT_START:this.start(e);break;case"drag":this.drag(e);break;case"swiperight":this.swiperight();break;case t.EVENT_END:case t.EVENT_CANCEL:this.end(e)}},h.prototype.start=function(t){this.handle.style.webkitTransitionDuration=this.element.style.webkitTransitionDuration=".2s",this.classList.add(r),(0===this.toggleWidth||0===this.handleWidth)&&this.init()},h.prototype.drag=function(t){var e=t.detail;this.isDragging||("left"===e.direction||"right"===e.direction)&&(this.isDragging=!0,this.lastChanged=void 0,this.initialState=this.classList.contains(o)),this.isDragging&&(this.setTranslateX(e.deltaX),t.stopPropagation(),e.gesture.preventDefault())},h.prototype.swiperight=function(t){this.isDragging&&t.stopPropagation()},h.prototype.end=function(e){this.classList.remove(r),this.isDragging?(this.isDragging=!1,e.stopPropagation(),t.trigger(this.element,"toggle",{isActive:this.classList.contains(o)})):this.toggle()},h.prototype.toggle=function(e){var i=this.classList;e===!1?this.handle.style.webkitTransitionDuration=this.element.style.webkitTransitionDuration="0s":this.handle.style.webkitTransitionDuration=this.element.style.webkitTransitionDuration=".2s",i.contains(o)?(i.remove(o),this.handle.style.webkitTransform="translate(0,0)"):(i.add(o),this.handle.style.webkitTransform="translate("+this.handleX+"px,0)"),t.trigger(this.element,"toggle",{isActive:this.classList.contains(o)})},h.prototype.setTranslateX=t.animationFrame(function(t){if(this.isDragging){var e=!1;(this.initialState&&-t>this.handleX/2||!this.initialState&&t>this.handleX/2)&&(e=!0),this.lastChanged!==e&&(e?(this.handle.style.webkitTransform="translate("+(this.initialState?0:this.handleX)+"px,0)",this.classList[this.initialState?"remove":"add"](o)):(this.handle.style.webkitTransform="translate("+(this.initialState?this.handleX:0)+"px,0)",this.classList[this.initialState?"add":"remove"](o)),this.lastChanged=e)}}),t.fn["switch"]=function(e){var i=[];return this.each(function(){var e=null,s=this.getAttribute("data-switch");s?e=t.data[s]:(s=++t.uuid,t.data[s]=new h(this),this.setAttribute("data-switch",s)),i.push(e)}),i.length>1?i:i[0]},t.ready(function(){t("."+s)["switch"]()})}(mui,window,"toggle"),function(t,e,i){function s(t,e){var i=e?"removeEventListener":"addEventListener";t[i]("drag",P),t[i]("dragend",P),t[i]("swiperight",P),t[i]("swipeleft",P),t[i]("flick",P)}var n,o,r="mui-active",a="mui-selected",l="mui-grid-view",c="mui-table-view-radio",h="mui-table-view-cell",u="mui-collapse-content",d="mui-disabled",p="mui-switch",f="mui-btn",g="mui-slider-handle",m="mui-slider-left",v="mui-slider-right",w="mui-transitioning",b="."+g,y="."+m,L="."+v,T="."+a,E="."+f,x=.8,S=isOpened=openedActions=progress=!1,_=sliderActionLeft=sliderActionRight=buttonsLeft=buttonsRight=sliderDirection=sliderRequestAnimationFrame=!1,C=translateX=lastTranslateX=sliderActionLeftWidth=sliderActionRightWidth=0,k=function(t){t?o?o.classList.add(r):n&&n.classList.add(r):(C&&C.cancel(),o?o.classList.remove(r):n&&n.classList.remove(r))},N=function(){if(translateX!==lastTranslateX){if(buttonsRight&&buttonsRight.length>0){progress=translateX/sliderActionRightWidth,translateX<-sliderActionRightWidth&&(translateX=-sliderActionRightWidth-Math.pow(-translateX-sliderActionRightWidth,x));for(var t=0,e=buttonsRight.length;e>t;t++){var i=buttonsRight[t];"undefined"==typeof i._buttonOffset&&(i._buttonOffset=i.offsetLeft),buttonOffset=i._buttonOffset,A(i,translateX-buttonOffset*(1+Math.max(progress,-1)))}}if(buttonsLeft&&buttonsLeft.length>0){progress=translateX/sliderActionLeftWidth,translateX>sliderActionLeftWidth&&(translateX=sliderActionLeftWidth+Math.pow(translateX-sliderActionLeftWidth,x));for(var t=0,e=buttonsLeft.length;e>t;t++){var s=buttonsLeft[t];"undefined"==typeof s._buttonOffset&&(s._buttonOffset=sliderActionLeftWidth-s.offsetLeft-s.offsetWidth),buttonOffset=s._buttonOffset,buttonsLeft.length>1&&(s.style.zIndex=buttonsLeft.length-t),A(s,translateX+buttonOffset*(1-Math.min(progress,1)))}}A(_,translateX),lastTranslateX=translateX}sliderRequestAnimationFrame=requestAnimationFrame(function(){N()})},A=function(t,e){t&&(t.style.webkitTransform="translate("+e+"px,0)")};e.addEventListener(t.EVENT_START,function(e){n&&k(!1),n=o=!1,S=isOpened=openedActions=!1;for(var r=e.target,a=!1;r&&r!==i;r=r.parentNode)if(r.classList){var g=r.classList;if(("INPUT"===r.tagName&&"radio"!==r.type&&"checkbox"!==r.type||"BUTTON"===r.tagName||g.contains(p)||g.contains(f)||g.contains(d))&&(a=!0),g.contains(u))break;if(g.contains(h)){n=r;var m=n.parentNode.querySelector(T);if(!n.parentNode.classList.contains(c)&&m&&m!==n)return t.swipeoutClose(m),void(n=a=!1);if(!n.parentNode.classList.contains(l)){var v=n.querySelector("a");v&&v.parentNode===n&&(o=v)}var w=n.querySelector(b);w&&(s(n),e.stopPropagation()),a||(w?(C&&C.cancel(),C=t.later(function(){k(!0)},100)):k(!0));break}}}),e.addEventListener(t.EVENT_MOVE,function(t){k(!1)});var P={handleEvent:function(t){switch(t.type){case"drag":this.drag(t);break;case"dragend":this.dragend(t);break;case"flick":this.flick(t);break;case"swiperight":this.swiperight(t);break;case"swipeleft":this.swipeleft(t)}},drag:function(t){if(n){S||(_=sliderActionLeft=sliderActionRight=buttonsLeft=buttonsRight=sliderDirection=sliderRequestAnimationFrame=!1,_=n.querySelector(b),_&&(sliderActionLeft=n.querySelector(y),sliderActionRight=n.querySelector(L),sliderActionLeft&&(sliderActionLeftWidth=sliderActionLeft.offsetWidth,buttonsLeft=sliderActionLeft.querySelectorAll(E)),sliderActionRight&&(sliderActionRightWidth=sliderActionRight.offsetWidth,buttonsRight=sliderActionRight.querySelectorAll(E)),n.classList.remove(w),isOpened=n.classList.contains(a),isOpened&&(openedActions=n.querySelector(y+T)?"left":"right")));var e=t.detail,i=e.direction,s=e.angle;if("left"===i&&(s>150||-150>s)?(buttonsRight||buttonsLeft&&isOpened)&&(S=!0):"right"===i&&s>-30&&30>s&&(buttonsLeft||buttonsRight&&isOpened)&&(S=!0),
S){t.stopPropagation(),t.detail.gesture.preventDefault();var o=t.detail.deltaX;if(isOpened&&("right"===openedActions?o-=sliderActionRightWidth:o+=sliderActionLeftWidth),o>0&&!buttonsLeft||0>o&&!buttonsRight){if(!isOpened)return;o=0}0>o?sliderDirection="toLeft":o>0?sliderDirection="toRight":sliderDirection||(sliderDirection="toLeft"),sliderRequestAnimationFrame||N(),translateX=o}}},flick:function(t){S&&t.stopPropagation()},swipeleft:function(t){S&&t.stopPropagation()},swiperight:function(t){S&&t.stopPropagation()},dragend:function(e){if(S){e.stopPropagation(),sliderRequestAnimationFrame&&(cancelAnimationFrame(sliderRequestAnimationFrame),sliderRequestAnimationFrame=null);var i=e.detail;S=!1;var s="close",o="toLeft"===sliderDirection?sliderActionRightWidth:sliderActionLeftWidth,r=i.swipe||Math.abs(translateX)>o/2;r&&(isOpened?"left"===i.direction&&"right"===openedActions?s="open":"right"===i.direction&&"left"===openedActions&&(s="open"):s="open"),n.classList.add(w);var l;if("open"===s){var c="toLeft"===sliderDirection?-o:o;if(A(_,c),l="toLeft"===sliderDirection?buttonsRight:buttonsLeft,"undefined"!=typeof l){for(var h=null,u=0;u<l.length;u++)h=l[u],A(h,c);h.parentNode.classList.add(a),n.classList.add(a),isOpened||t.trigger(n,"toLeft"===sliderDirection?"slideleft":"slideright")}}else A(_,0),sliderActionLeft&&sliderActionLeft.classList.remove(a),sliderActionRight&&sliderActionRight.classList.remove(a),n.classList.remove(a);var d;if(buttonsLeft&&buttonsLeft.length>0&&buttonsLeft!==l)for(var u=0,p=buttonsLeft.length;p>u;u++){var f=buttonsLeft[u];d=f._buttonOffset,"undefined"==typeof d&&(f._buttonOffset=sliderActionLeftWidth-f.offsetLeft-f.offsetWidth),A(f,d)}if(buttonsRight&&buttonsRight.length>0&&buttonsRight!==l)for(var u=0,p=buttonsRight.length;p>u;u++){var g=buttonsRight[u];d=g._buttonOffset,"undefined"==typeof d&&(g._buttonOffset=g.offsetLeft),A(g,-d)}}}};t.swipeoutOpen=function(e,i){if(e){var s=e.classList;if(!s.contains(a)){i||(i=e.querySelector(L)?"right":"left");var n=e.querySelector(t.classSelector(".slider-"+i));if(n){n.classList.add(a),s.add(a),s.remove(w);for(var o,r=n.querySelectorAll(E),l=n.offsetWidth,c="right"===i?-l:l,h=r.length,u=0;h>u;u++)o=r[u],"right"===i?A(o,-o.offsetLeft):A(o,l-o.offsetWidth-o.offsetLeft);s.add(w);for(var u=0;h>u;u++)A(r[u],c);A(e.querySelector(b),c)}}}},t.swipeoutClose=function(e){if(e){var i=e.classList;if(i.contains(a)){var s=e.querySelector(L+T)?"right":"left",n=e.querySelector(t.classSelector(".slider-"+s));if(n){n.classList.remove(a),i.remove(a),i.add(w);var o,r=n.querySelectorAll(E),l=n.offsetWidth,c=r.length;A(e.querySelector(b),0);for(var h=0;c>h;h++)o=r[h],"right"===s?A(o,-o.offsetLeft):A(o,l-o.offsetWidth-o.offsetLeft)}}}},e.addEventListener(t.EVENT_END,function(t){n&&(k(!1),_&&s(n,!0))}),e.addEventListener(t.EVENT_CANCEL,function(t){n&&(k(!1),_&&s(n,!0))});var R=function(e){var i=e.target&&e.target.type||"";if("radio"!==i&&"checkbox"!==i){var s=n.classList;if(s.contains("mui-radio")){var o=n.querySelector("input[type=radio]");o&&(o.disabled||o.readOnly||(o.checked=!o.checked,t.trigger(o,"change")))}else if(s.contains("mui-checkbox")){var o=n.querySelector("input[type=checkbox]");o&&(o.disabled||o.readOnly||(o.checked=!o.checked,t.trigger(o,"change")))}}};e.addEventListener(t.EVENT_CLICK,function(t){n&&n.classList.contains("mui-collapse")&&t.preventDefault()}),e.addEventListener("doubletap",function(t){n&&R(t)});var X=/^(INPUT|TEXTAREA|BUTTON|SELECT)$/;e.addEventListener("tap",function(e){if(n){var i=!1,s=n.classList,o=n.parentNode;if(o&&o.classList.contains(c)){if(s.contains(a))return;var l=o.querySelector("li"+T);return l&&l.classList.remove(a),s.add(a),void t.trigger(n,"selected",{el:n})}if(s.contains("mui-collapse")&&!n.parentNode.classList.contains("mui-unfold")){if(X.test(e.target.tagName)||e.detail.gesture.preventDefault(),!s.contains(r)){var h=n.parentNode.querySelector(".mui-collapse.mui-active");h&&h.classList.remove(r),i=!0}s.toggle(r),i&&t.trigger(n,"expand")}else R(e)}})}(mui,window,document),function(t,e){t.alert=function(i,s,n,o){if(t.os.plus){if("undefined"==typeof i)return;"function"==typeof s?(o=s,s=null,n="确定"):"function"==typeof n&&(o=n,n=null),t.plusReady(function(){plus.nativeUI.alert(i,o,s,n)})}else e.alert(i)}}(mui,window),function(t,e){t.confirm=function(i,s,n,o){if(t.os.plus){if("undefined"==typeof i)return;"function"==typeof s?(o=s,s=null,n=null):"function"==typeof n&&(o=n,n=null),t.plusReady(function(){plus.nativeUI.confirm(i,o,s,n)})}else o(e.confirm(i)?{index:0}:{index:1})}}(mui,window),function(t,e){t.prompt=function(i,s,n,o,r){if(t.os.plus){if("undefined"==typeof message)return;"function"==typeof s?(r=s,s=null,n=null,o=null):"function"==typeof n?(r=n,n=null,o=null):"function"==typeof o&&(r=o,o=null),t.plusReady(function(){plus.nativeUI.prompt(i,r,n,s,o)})}else{var a=e.prompt(i);r(a?{index:0,value:a}:{index:1,value:""})}}}(mui,window),function(t,e){var i="mui-active";t.toast=function(e){if(t.os.plus)t.plusReady(function(){plus.nativeUI.toast(e,{verticalAlign:"bottom"})});else{var s=document.createElement("div");s.classList.add("mui-toast-container"),s.innerHTML='<div class="mui-toast-message">'+e+"</div>",s.addEventListener("webkitTransitionEnd",function(){s.classList.contains(i)||s.parentNode.removeChild(s)}),document.body.appendChild(s),s.offsetHeight,s.classList.add(i),setTimeout(function(){s.classList.remove(i)},2e3)}}}(mui,window),function(t,e,i){var s="mui-popup",n="mui-popup-backdrop",o="mui-popup-in",r="mui-popup-out",a="mui-popup-inner",l="mui-popup-title",c="mui-popup-text",h="mui-popup-input",u="mui-popup-buttons",d="mui-popup-button",p="mui-popup-button-bold",n="mui-popup-backdrop",f="mui-active",g=[],m=function(){var e=i.createElement("div");return e.classList.add(n),e.addEventListener(t.EVENT_MOVE,t.preventDefault),e.addEventListener("webkitTransitionEnd",function(){this.classList.contains(f)||e.parentNode&&e.parentNode.removeChild(e)}),e}(),v=function(t){return'<div class="'+h+'"><input type="text" autofocus placeholder="'+(t||"")+'"/></div>'},w=function(t,e,i){return'<div class="'+a+'"><div class="'+l+'">'+e+'</div><div class="'+c+'">'+t.replace(/\r\n/g,"<br/>").replace(/\n/g,"<br/>")+"</div>"+(i||"")+"</div>"},b=function(t){for(var e=t.length,i=[],s=0;e>s;s++)i.push('<span class="'+d+(s===e-1?" "+p:"")+'">'+t[s]+"</span>");return'<div class="'+u+'">'+i.join("")+"</div>"},y=function(e,n){var a=i.createElement("div");a.className=s,a.innerHTML=e;var l=function(){a.parentNode&&a.parentNode.removeChild(a),a=null};a.addEventListener(t.EVENT_MOVE,t.preventDefault),a.addEventListener("webkitTransitionEnd",function(t){a&&t.target===a&&a.classList.contains(r)&&l()}),a.style.display="block",i.body.appendChild(a),a.offsetHeight,a.classList.add(o),m.classList.contains(f)||(m.style.display="block",i.body.appendChild(m),m.offsetHeight,m.classList.add(f));var c=t.qsa("."+d,a),u=a.querySelector("."+h+" input"),p={element:a,close:function(t,e){a&&(n&&n({index:t||0,value:u&&u.value||""}),e!==!1?(a.classList.remove(o),a.classList.add(r)):l(),g.pop(),g.length?g[g.length-1].show(e):m.classList.remove(f))}},v=function(t){p.close(c.indexOf(t.target))};return t(a).on("tap","."+d,v),g.length&&g[g.length-1].hide(),g.push({close:p.close,show:function(t){a.style.display="block",a.offsetHeight,a.classList.add(o)},hide:function(){a.style.display="none",a.classList.remove(o)}}),p},L=function(e,i,s,n,o){return"undefined"!=typeof e?("function"==typeof i?(n=i,o=s,i=null,s=null):"function"==typeof s&&(o=n,n=s,s=null),t.os.plus&&"div"!==o?plus.nativeUI.alert(e,n,i||"提示",s||"确定"):y(w(e,i||"提示")+b([s||"确定"]),n)):void 0},T=function(e,i,s,n,o){return"undefined"!=typeof e?("function"==typeof i?(n=i,o=s,i=null,s=null):"function"==typeof s&&(o=n,n=s,s=null),t.os.plus&&"div"!==o?plus.nativeUI.confirm(e,n,i,s||["取消","确认"]):y(w(e,i||"提示")+b(s||["取消","确认"]),n)):void 0},E=function(e,i,s,n,o,r){return"undefined"!=typeof e?("function"==typeof i?(o=i,r=s,i=null,s=null,n=null):"function"==typeof s?(o=s,r=n,s=null,n=null):"function"==typeof n&&(r=o,o=n,n=null),t.os.plus&&"div"!==r?plus.nativeUI.prompt(e,o,s||"提示",i,n||["取消","确认"]):y(w(e,s||"提示",v(i))+b(n||["取消","确认"]),o)):void 0},x=function(){return!!g.length&&(g[g.length-1].close(),!0)},S=function(){for(;g.length;)g[g.length-1].close()};t.closePopup=x,t.closePopups=S,t.alert=L,t.confirm=T,t.prompt=E}(mui,window,document),function(t,e){var i="mui-progressbar",s="mui-progressbar-in",n="mui-progressbar-out",o="mui-progressbar-infinite",r=".mui-progressbar",a=function(e){if(e=t(e||"body"),0!==e.length){if(e=e[0],e.classList.contains(i))return e;var s=e.querySelectorAll(r);if(s)for(var n=0,o=s.length;o>n;n++){var a=s[n];if(a.parentNode===e)return a}}},l=function(a,l,c){if("number"==typeof a&&(c=l,l=a,a="body"),a=t(a||"body"),0!==a.length){a=a[0];var u;if(a.classList.contains(i))u=a;else{var d=a.querySelectorAll(r+":not(."+n+")");if(d)for(var p=0,f=d.length;f>p;p++){var g=d[p];if(g.parentNode===a){u=g;break}}u?u.classList.add(s):(u=e.createElement("span"),u.className=i+" "+s+("undefined"!=typeof l?"":" "+o)+(c?" "+i+"-"+c:""),"undefined"!=typeof l&&(u.innerHTML="<span></span>"),a.appendChild(u))}return l&&h(a,l),u}},c=function(t){var e=a(t);if(e){var i=e.classList;i.contains(s)&&!i.contains(n)&&(i.remove(s),i.add(n),e.addEventListener("webkitAnimationEnd",function(){e.parentNode&&e.parentNode.removeChild(e),e=null}))}},h=function(t,e,i){"number"==typeof t&&(i=e,e=t,t=!1);var s=a(t);if(s&&!s.classList.contains(o)){e&&(e=Math.min(Math.max(e,0),100)),s.offsetHeight;var n=s.querySelector("span");if(n){var r=n.style;r.webkitTransform="translate3d("+(-100+e)+"%,0,0)","undefined"!=typeof i?r.webkitTransitionDuration=i+"ms":r.webkitTransitionDuration=""}return s}};t.fn.progressbar=function(t){var e=[];return t=t||{},this.each(function(){var i=this,s=i.mui_plugin_progressbar;s?t&&s.setOptions(t):i.mui_plugin_progressbar=s={options:t,setOptions:function(t){this.options=t},show:function(){return l(i,this.options.progress,this.options.color)},setProgress:function(t){return h(i,t)},hide:function(){return c(i)}},e.push(s)}),1===e.length?e[0]:e}}(mui,document),function(t,e,i){var s="mui-icon",n="mui-icon-clear",o="mui-icon-speech",r="mui-icon-search",a="mui-icon-eye",l="mui-input-row",c="mui-placeholder",h="mui-tooltip",u="mui-hidden",d="mui-focusin",p="."+n,f="."+o,g="."+a,m="."+c,v="."+h,w=function(t){for(;t&&t!==i;t=t.parentNode)if(t.classList&&t.classList.contains(l))return t;return null},b=function(t,e){this.element=t,this.options=e||{actions:"clear"},~this.options.actions.indexOf("slider")?(this.sliderActionClass=h+" "+u,this.sliderActionSelector=v):(~this.options.actions.indexOf("clear")&&(this.clearActionClass=s+" "+n+" "+u,this.clearActionSelector=p),~this.options.actions.indexOf("speech")&&(this.speechActionClass=s+" "+o,this.speechActionSelector=f),~this.options.actions.indexOf("search")&&(this.searchActionClass=c,this.searchActionSelector=m),~this.options.actions.indexOf("password")&&(this.passwordActionClass=s+" "+a,this.passwordActionSelector=g)),this.init()};b.prototype.init=function(){this.initAction(),this.initElementEvent()},b.prototype.initAction=function(){var e=this,i=e.element.parentNode;i&&(e.sliderActionClass?e.sliderAction=e.createAction(i,e.sliderActionClass,e.sliderActionSelector):(e.searchActionClass&&(e.searchAction=e.createAction(i,e.searchActionClass,e.searchActionSelector),e.searchAction.addEventListener("tap",function(i){t.focus(e.element),i.stopPropagation()})),e.speechActionClass&&(e.speechAction=e.createAction(i,e.speechActionClass,e.speechActionSelector),e.speechAction.addEventListener("click",t.stopPropagation),e.speechAction.addEventListener("tap",function(t){e.speechActionClick(t)})),e.clearActionClass&&(e.clearAction=e.createAction(i,e.clearActionClass,e.clearActionSelector),e.clearAction.addEventListener("tap",function(t){e.clearActionClick(t)})),e.passwordActionClass&&(e.passwordAction=e.createAction(i,e.passwordActionClass,e.passwordActionSelector),e.passwordAction.addEventListener("tap",function(t){e.passwordActionClick(t)}))))},b.prototype.createAction=function(t,e,n){var o=t.querySelector(n);if(!o){var o=i.createElement("span");o.className=e,e===this.searchActionClass&&(o.innerHTML='<span class="'+s+" "+r+'"></span><span>'+this.element.getAttribute("placeholder")+"</span>",this.element.setAttribute("placeholder",""),this.element.value.trim()&&t.classList.add("mui-active")),t.insertBefore(o,this.element.nextSibling)}return o},b.prototype.initElementEvent=function(){var e=this.element;if(this.sliderActionClass){var i=this.sliderAction,s=null,n=function(){i.classList.remove(u);var t=e.offsetLeft,n=e.offsetWidth-28,o=i.offsetWidth,r=Math.abs(e.max-e.min),a=n/r*Math.abs(e.value-e.min);i.style.left=14+t+a-o/2+"px",i.innerText=e.value,s&&clearTimeout(s),s=setTimeout(function(){i.classList.add(u)},1e3)};e.addEventListener("input",n),e.addEventListener("tap",n),e.addEventListener(t.EVENT_MOVE,function(t){t.stopPropagation()})}else{if(this.clearActionClass){var o=this.clearAction;if(!o)return;t.each(["keyup","change","input","focus","cut","paste"],function(t,i){!function(t){e.addEventListener(t,function(){o.classList[e.value.trim()?"remove":"add"](u)})}(i)}),e.addEventListener("blur",function(){o.classList.add(u)})}this.searchActionClass&&(e.addEventListener("focus",function(){e.parentNode.classList.add("mui-active")}),e.addEventListener("blur",function(){e.value.trim()||e.parentNode.classList.remove("mui-active")}))}},b.prototype.setPlaceholder=function(t){if(this.searchActionClass){var e=this.element.parentNode.querySelector(m);e&&(e.getElementsByTagName("span")[1].innerText=t)}else this.element.setAttribute("placeholder",t)},b.prototype.passwordActionClick=function(t){"text"===this.element.type?this.element.type="password":this.element.type="text",this.passwordAction.classList.toggle("mui-active"),t.preventDefault()},b.prototype.clearActionClick=function(e){var i=this;i.element.value="",t.focus(i.element),i.clearAction.classList.add(u),e.preventDefault()},b.prototype.speechActionClick=function(s){if(e.plus){var n=this,o=n.element.value;n.element.value="",i.body.classList.add(d),plus.speech.startRecognize({engine:"iFly"},function(e){n.element.value+=e,t.focus(n.element),plus.speech.stopRecognize(),t.trigger(n.element,"recognized",{value:n.element.value}),o!==n.element.value&&(t.trigger(n.element,"change"),t.trigger(n.element,"input"))},function(t){i.body.classList.remove(d)})}else alert("only for 5+");s.preventDefault()},t.fn.input=function(e){var i=[];return this.each(function(){var e=null,s=[],n=w(this.parentNode);if("range"===this.type&&n.classList.contains("mui-input-range"))s.push("slider");else{var o=this.classList;o.contains("mui-input-clear")&&s.push("clear"),t.os.android&&t.os.stream||!o.contains("mui-input-speech")||s.push("speech"),o.contains("mui-input-password")&&s.push("password"),"search"===this.type&&n.classList.contains("mui-search")&&s.push("search")}var r=this.getAttribute("data-input-"+s[0]);if(r)e=t.data[r];else{r=++t.uuid,e=t.data[r]=new b(this,{actions:s.join(",")});for(var a=0,l=s.length;l>a;a++)this.setAttribute("data-input-"+s[a],r)}i.push(e)}),1===i.length?i[0]:i},t.ready(function(){t(".mui-input-row input").input()})}(mui,window,document),function(t,e){var i=/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,s=function(t){var e=t.match(i);return e&&5===e.length?[e[1],e[2],e[3],e[4]]:[]},n=function(e,i){this.element=e,this.options=t.extend({top:0,offset:150,duration:16},i||{}),this._style=this.element.style,this._bgColor=this._style.backgroundColor;var n=s(mui.getStyles(this.element,"backgroundColor"));if(!n.length)throw new Error("元素背景颜色必须为RGBA");this._R=n[0],this._G=n[1],this._B=n[2],this._A=n[3],this._bufferFn=t.buffer(this.handleScroll,this.options.duration,this),this.initEvent()};n.prototype.initEvent=function(){e.addEventListener("scroll",this._bufferFn),e.addEventListener(t.EVENT_MOVE,this._bufferFn)},n.prototype.handleScroll=function(){this._style.backgroundColor="rgba("+this._R+","+this._G+","+this._B+","+(e.scrollY-this.options.top)/this.options.offset+")"},n.prototype.destory=function(){e.removeEventListener("scroll",this._bufferFn),e.removeEventListener(t.EVENT_MOVE,this._bufferFn),this.element.style.backgroundColor=this._bgColor,this.element.mui_plugin_transparent=null},t.fn.transparent=function(t){t=t||{};var e=[];return this.each(function(){var i=this.mui_plugin_transparent;if(!i){var s=this.getAttribute("data-top"),o=this.getAttribute("data-offset"),r=this.getAttribute("data-duration");null!==s&&"undefined"==typeof t.top&&(t.top=s),null!==o&&"undefined"==typeof t.offset&&(t.offset=o),null!==r&&"undefined"==typeof t.duration&&(t.duration=r),i=this.mui_plugin_transparent=new n(this,t)}e.push(i)}),1===e.length?e[0]:e},t.ready(function(){t(".mui-bar-transparent").transparent()})}(mui,window),function(t){var e="ontouchstart"in document,i=e?"tap":"click",s="change",n="mui-numbox",o=".mui-btn-numbox-plus,.mui-numbox-btn-plus",r=".mui-btn-numbox-minus,.mui-numbox-btn-minus",a=".mui-input-numbox,.mui-numbox-input",l=t.Numbox=t.Class.extend({init:function(e,i){var s=this;if(!e)throw"构造 numbox 时缺少容器元素";s.holder=e,i=i||{},i.step=parseInt(i.step||1),s.options=i,s.input=t.qsa(a,s.holder)[0],s.plus=t.qsa(o,s.holder)[0],s.minus=t.qsa(r,s.holder)[0],s.checkValue(),s.initEvent()},initEvent:function(){var e=this;e.plus.addEventListener(i,function(i){var n=parseInt(e.input.value)+e.options.step;e.input.value=n.toString(),t.trigger(e.input,s,null)}),e.minus.addEventListener(i,function(i){var n=parseInt(e.input.value)-e.options.step;e.input.value=n.toString(),t.trigger(e.input,s,null)}),e.input.addEventListener(s,function(i){e.checkValue();var n=parseInt(e.input.value);t.trigger(e.holder,s,{value:n})})},getValue:function(){var t=this;return parseInt(t.input.value)},checkValue:function(){var t=this,e=t.input.value;if(null==e||""==e||isNaN(e))t.input.value=t.options.min||0,t.minus.disabled=null!=t.options.min;else{var e=parseInt(e);null!=t.options.max&&!isNaN(t.options.max)&&e>=parseInt(t.options.max)?(e=t.options.max,t.plus.disabled=!0):t.plus.disabled=!1,null!=t.options.min&&!isNaN(t.options.min)&&e<=parseInt(t.options.min)?(e=t.options.min,t.minus.disabled=!0):t.minus.disabled=!1,t.input.value=e}},setOption:function(t,e){var i=this;i.options[t]=e}});t.fn.numbox=function(t){return this.each(function(t,e){if(!e.numbox)if(s)e.numbox=new l(e,s);else{var i=e.getAttribute("data-numbox-options"),s=i?JSON.parse(i):{};s.step=e.getAttribute("data-numbox-step")||s.step,s.min=e.getAttribute("data-numbox-min")||s.min,s.max=e.getAttribute("data-numbox-max")||s.max,e.numbox=new l(e,s)}}),this[0]?this[0].numbox:null},t.ready(function(){t("."+n).numbox()})}(mui);